import moment from 'moment'

import { badRequestError } from '@errors'

import { GetSchoolIdLeads, LeadSourceCostGet } from '@interfaces'

import {
  GET_LEADS_FOR_SCHOOL_QUERY_PROCESS_1,
  GET_LEADS_FOR_SCHOOL_QUERY_PROCESS_2,
  GET_LEADS_FOR_SCHOOL_QUERY_FILTERS,
  GET_LEADS_FOR_SCHOOL_QUERY_FILTERS_DB,
  GET_LEADS_FOR_SCHOOL_QUERY_FILTERS_DATES,
  GET_LEADS_FOR_SCHOOL_QUERY_FILTERS_2,
  REMINDER_PERIODS,
} from '@constants'

/**
 * Returns the query to search the leads record by application_token
 */
export function getByApplicationToken () {
  return `SELECT id, school_id, application_token, status, parent_relationship, parent_first_name,
  parent_last_name, phone, email, preferred_contact, child_first_name, child_last_name, grade
  FROM leads where application_token = $1`
}

/**
 * Returns the query to search the leads by id and school_id
 */
export function getLead () {
  return `SELECT id, school_id, application_token, status, application_id, parent_relationship, parent_first_name,
  parent_last_name, phone, email, preferred_contact, child_first_name, child_last_name, grade, user_id
  FROM leads where id = $1 AND school_id = $2`
}


/**
 * Returns the query to search the leads by id
 */
export function getLeadById () {
  return `SELECT id, school_id, application_token, status, application_id, parent_relationship, parent_first_name,
  parent_last_name, phone, email, preferred_contact, child_first_name, child_last_name, grade, user_id
  FROM leads where id = $1`
}

/**
 * Returns the first part of the insert query for the lead_additional_data_reasons_for_leaving table
 * that will be later used to build the bulk insert query
 * @returns {string}
 */
export function bulkInsertLeadAdditionalDataReasonsForLeaving () {
  return `INSERT INTO lead_additional_data_reasons_for_leaving(school_id,
    lead_additional_data_id, created_at, other, reason_id) VALUES `
}

/**
 * Returns a record from leads table
 * @returns {string}
 */
export function getLeadByEmail() {
  return `SELECT * FROM leads WHERE email = $1`
}

/**
 * @returns {string}
 */
export function getLeadFields() {
  return `SELECT * FROM lead_fields WHERE school_id = $1`
}

/**
 * @returns {string}
 */
export function schoolExists() {
  return `select exists(select 1 from schools where id=$1)`
}


/**
 * @returns {string}
 */
export function getLeadsForSchoolCountQuery(query_conditions: string) {
  return `
            SELECT
              COUNT(*)
            FROM
              (
                SELECT
                  l.id,
                  l.school_id,
                  l.parent_first_name,
                  l.parent_last_name,
                  l.user_id,
                  l.email,
                  l.phone,
                  l.preferred_contact,
                  l.child_first_name,
                  l.child_last_name,
                  l.grade,
                  l.child_birthdate,
                  l.status,
                  l.application_token,
                  l.application_id,
                  l.parent_relationship,
                  l.tour_requested,
                  l.note,
                  l.created_at,
                  l.user_student_id,
                  l.year,
                  coalesce(l.language,'english') as language,
                  l.custom_field_1,
                  l.custom_field_2,
                  l.custom_field_3,
                  l.custom_field_4,
                  l.custom_field_5,
                  l.lead_source_id,
                  l.address_description,
                  l.sync_intg_status,
                  l.sync_intg_with,
                  l.sync_intg_date,
                  l.iep,
                  l.sibling_in_school,
                  l.district,
                  l.intg_lead_id,
                  l.intg_app_id,
                  l.intg_campus,
                Case
                  when l.lead_source_id = -1 then 'ScholaMatch'
                  when l.lead_source_id = -2 then 'GeneralLead'
                  when l.lead_source_id = -3 then 'ScholaMatch2'
                  when l.lead_source_id = -4 then 'Schola School Campaign'
                  when l.lead_source_id = -5 then 'ScholaMatch3'
                  when l.lead_source_id = -6 then 'PotentialLeadsRO'
                  when l.lead_source_id = -7 then 'AutoScholaMatch'
                  when l.lead_source_id = -8 then 'Schola Pre-Qualify'
                  when l.lead_source_id = -9 then 'Scholamatch SPOT'
                  when l.lead_source_id = -10 then 'Schola Profile A'
                  when l.lead_source_id = -11 then 'Schola General Campaign - ASM'
                  else lsource.name
                End    lead_source,
                l.lead_status_id,
                lstatus.name as lead_status,
                l.zipcode,
                l.reason_id,
                r.name reason,
                l.referred_by,
                l.referred_by_other,
                l.country,
                l.assigned_to,
                case when not app.id is null then true
                else
                coalesce(l.application_received, false) end application_received,
                coalesce(app.created_at, l.application_received_at) application_received_at,
                coalesce(l.application_valid, true) application_valid,
                coalesce(l.enrollment_confirmed, false) enrollment_confirmed,
                l.enrollment_confirmed_at,
                COALESCE(lt.need,'No') lead_transportation_need,
                l.year_accepted,
                COALESCE(l.attendance_confirmed,false) attendance_confirmed,
                COALESCE(l.pqe,false) pqe,
                l.score_weight_grade,
                l.avg_score_weight,
                (Select n.note
                          From
                            Notes n
                          Where
                            n.object_type = 'lead' and n.object_id = l.id
                order by n.id desc
                limit 1) as last_note
                FROM public.view_leads l
                  left join public.lead_sources lsource on lsource.id = l.lead_source_id
                  left join public.lead_statuses lstatus on lstatus.id = l.lead_status_id
                  left join reasons r on l.reason_id = r.id
                  left join lead_transportations lt on l.id = lt.lead_id
                  left join completed_applications app on l.application_id=app.id
                WHERE
                    l.school_id = $1
                    AND (l.status = $2 OR ''=$2)
                    AND COALESCE(l.deleted, false) = false

              ) l
            WHERE
              1 = 1
              ${query_conditions}
            `
}

/**
 * @returns {string}
 */
export function getLeadsForSchoolMainQuery(query_conditions: string, parameters_count: number, direction: string, order_field: string, get_app: boolean) {
  if(order_field==='score_weight_grade') order_field='avg_score_weight'
  return `
  SELECT
    *
  FROM
    (
      SELECT
          l.id,
          l.school_id,
          l.parent_first_name,
          l.parent_last_name,
          l.user_id,
          l.email,
          l.phone,
          l.preferred_contact,
          l.child_first_name,
          l.child_last_name,
          l.grade,
          l.child_birthdate,
          l.status,
          l.application_token,
          l.application_id,
          l.parent_relationship,
          l.tour_requested,
          l.note,
          l.created_at,
          l.user_student_id,
          l.year,
          coalesce(l.language,'english') as language,
          l.custom_field_1,
          l.custom_field_2,
          l.custom_field_3,
          l.custom_field_4,
          l.custom_field_5,
          l.lead_source_id,
          COALESCE(l.attendance_confirmed,false) attendance_confirmed,
          COALESCE(l.pqe,false) pqe,
          Case
            when l.lead_source_id = -1 then 'ScholaMatch'
            when l.lead_source_id = -2 then 'GeneralLead'
            when l.lead_source_id = -3 then 'ScholaMatch2'
            when l.lead_source_id = -4 then 'Schola School Campaign'
            when l.lead_source_id = -5 then 'ScholaMatch3'
            when l.lead_source_id = -6 then 'PotentialLeadsRO'
            when l.lead_source_id = -7 then 'AutoScholaMatch'
            when l.lead_source_id = -8 then 'Schola Pre-Qualify'
            when l.lead_source_id = -9 then 'Scholamatch SPOT'
            when l.lead_source_id = -10 then 'Schola Profile A'
            when l.lead_source_id = -11 then 'Schola General Campaign - ASM'
            else lsource.name
          End    lead_source,
          l.lead_status_id,
          lstatus.name as lead_status,
          l.zipcode,
          viewed_at,
          Case
            When l.reason_id is NULL AND l.reason_other is not null then  -1
            else l.reason_id
          End reason_id,
          COALESCE(r.name, l.reason_other) reason,
          l.address,
          l.latitude,
          l.longitude,
          l.address_description,
          l.state,
          l.city,
          l.external_campaign_id,
          COALESCE((
            Select case when n.updated_at>l.updated_at then n.updated_at else l.updated_at end updated_at
            From
              Notes n
            Where
              n.object_type = 'lead' and n.object_id = l.id
            order by n.id desc
            limit 1)
          ,l.updated_at) as updated_at,
          (Select left(n.note,50)  as last_note
                      From
                        Notes n
                      Where
                        n.object_type = 'lead' and n.object_id = l.id
            order by n.id desc
            limit 1) as last_note,
          (Select sum(total) from
            (Select count(*) as total
              From
                Notes n
              Where
                n.object_type = 'lead' and n.object_id = l.id
            union all
            Select count(*) as total
              From
                sms_audits
              Where
                lead_id = l.id
            union all
            Select count(*)  as total
              From
                email_audits
              Where
                lead_id = l.id
            ) n
          ) as notes_count,
          (select count(*) from messages where lead_id=l.id) as messages_count,
          l.referred_by,
          l.referred_by_other,
          l.country,
          l.assigned_to,
          case when not app.id is null then true
          else
          coalesce(l.application_received, false) end application_received,
          coalesce(app.created_at,l.application_received_at) application_received_at,
          coalesce(l.application_valid, true) application_valid,
          coalesce(l.enrollment_confirmed, false) enrollment_confirmed,
          l.enrollment_confirmed_at,
          'lead' type_row,
          COALESCE(lt.need,'No') lead_transportation_need,
          l.year_accepted,
          l.created_on,
          l.sync_intg_with,
          l.iep,
          l.sibling_in_school,
          l.district,
          l.intg_lead_id,
          l.intg_campus,
          l.intg_app_id,
          l.sync_intg_status,
          l.sync_intg_date,
          l.score_weight_grade,
          l.avg_score_weight
          ${(get_app) ? ',app.application' : ''}
      FROM
        public.view_leads l
        left join public.lead_sources lsource on lsource.id = l.lead_source_id
        left join public.lead_statuses lstatus on lstatus.id = l.lead_status_id
        left join reasons r on l.reason_id = r.id
        left join lead_transportations lt on l.id = lt.lead_id
        left join completed_applications app on l.application_id=app.id
      WHERE
          l.school_id = $1
          AND (l.status = $2 OR ''=$2)
          AND COALESCE(l.deleted, false) = false
    ) result
  WHERE
    1 = 1
    ${query_conditions}
  ORDER BY ${order_field} ${direction}, id
  LIMIT $${parameters_count + 3}
  OFFSET $${parameters_count + 4}
  `
}
/*
 * Returns the query to search the leads by user_id
 * @returns {string}
 */
export function getLeadsByUserId() {
  return `SELECT * FROM leads WHERE user_id = $1`
}

/*
 * Returns the query to search for the lead source by name for a given school
 * @returns {string}
 */
export function getLeadSource(): string {
  return `SELECT id, school_id, name, deleted, created_at, updated_at
  FROM lead_sources WHERE school_id = $1 AND name = $2`
}

/**
 * Returns the query to search for the lead statuses for a given school
 * @returns {string}
 */
export function getLeadStatuses(): string {
  return `SELECT id, school_id, name, deleted, created_at, updated_at
  FROM lead_statuses WHERE deleted=false and school_id = $1
  ORDER BY name`
}

/**
 * Returns the query to search for the lead statuses for a given school
 * @returns {string}
 */
export function getLeadStatusByName(): string {
  return `SELECT id, school_id, name, deleted, created_at, updated_at
  FROM lead_statuses WHERE deleted=false and school_id = $1 and rtrim(ltrim(lower(name)))=$2
  `
}

/**
 * Returns the query to update the last notify for leads
 * @returns {string}
 */
export function markLeadsAsReminded(last_notify: string): string {
  return `UPDATE LEADS SET last_notify = $1
  WHERE viewed_at IS NULL AND school_id = $2 AND COALESCE(deleted, false) = false
  AND DATE_PART('day', current_date - created_at) * 24 + DATE_PART('hour', current_date - created_at)
  BETWEEN $3 AND $4
  AND (last_notify IS NULL ${last_notify ? `OR last_notify = $5` : ''})
  `
}

/**
 * @param $1 {number} school_id
 * @param $2: latitude
 * @param $3: longitude
 * [@param $4: text]
 * [@param $5: grade]
 * Returns the query get the count for potential leads for a given school
 * @returns {string}
 */
export function getPotentialLeadsForSchoolCount(filters: any): string {
  let where = ''
  let index = 4
  if(filters) {
    if(filters.text) {
      where += ` AND (address LIKE $${index})`
      index++
    }
    if(filters.grade) {
      where += ` AND (UPPER(grade) LIKE UPPER($${index}))`
      index++
    }
  }
  return `With _sh as (
    SELECT
      sh.id, sh.lat, sh.lon, sh.address, sh.user_id, sh.grade, sh.created_at,
      case when lat=cast($2 as real) and lon=cast($3 as real) then 0
      else (
        3959 * acos( cos( radians($2) )
        * cos( radians( lat ) )
        * cos( radians( lon ) - radians(  $3  ) )
        + sin( radians( $2  ) )
        * sin( radians( lat ) )
        )
      ) end distance,
      row_number() over (partition by sh.user_id order by sh.updated_at desc)
    FROM
      search_history as sh
      left join blacklist_users bs on sh.user_id = bs.user_id
      left join search_history_lead shl on shl.user_id = sh.user_id and shl.school_id = $1
    WHERE
      bs.id is null AND
      shl.id is null AND
      case
      when lat=cast($2 as real) and lon=cast($3 as real) then 0
      else
      (3959 * acos( cos( radians($2) )
            * cos( radians( lat ) )
            * cos( radians( lon ) - radians(  $3  ) )
            + sin( radians( $2  ) )
            * sin( radians( lat ) )
      )) end < 20
      AND sh.user_id is not null
  )
  SELECT COUNT(*) FROM _sh WHERE _sh.row_number = 1 ${where}`
}

/**
 * Returns query to get the general leads
 * Query Params:
 * $1 school_id {number}
 * @returns {string}
 */
export function getGeneralLeads() {
  const GENERAL_LEAD_SOURCE_ID = -2
  return `SELECT * FROM leads WHERE COALESCE(deleted, false) = false
  AND lead_source_id = ${GENERAL_LEAD_SOURCE_ID} AND school_id = $1`
}

/**
 * Returns query to get the general lead source cost count
 * Query Params:
 * $1 school_id {number}
 * @returns {string}
 */
export function getLeadSourceCostCount() {
  return `
    SELECT
      count(lsc.id) as lead_source_cost_count
    FROM
      lead_source_costs lsc
    WHERE
      lsc.deleted = false AND
      lsc.school_id = $1`
}

/**
 * Returns the query to update leads
 * @returns {string}
 */
export function updateLeads(elements: Array<string>, leads_ids: Array<number>): string {
  const intElem = leads_ids.filter(lead_id => Number.isInteger(lead_id))
  if (intElem.length !== leads_ids.length) {
    throw badRequestError
  }

  const values = []
  for (const [index,value] of elements.entries()) {
    if(value==='key_duplicated_auto') {
      values.push(`key_duplicated = concat($${index + 1}::text, id) `)
    } else {
      values.push(`${value} = $${index + 1}`)
    }

  }

  return `UPDATE leads SET ${values.join(',')} WHERE id IN (${leads_ids.join(',')}) RETURNING *`
}

/**
 * Returns the query to update leads
 * @returns {string}
 */
export function builSelectToUpdate(keys: Array<string>, values: Array<unknown>, leads_ids: Array<number>): string {
  const intElem = leads_ids.filter(lead_id => Number.isInteger(lead_id))
  if (intElem.length !== leads_ids.length) {
    throw badRequestError
  }

  const validation = ['key_duplicated', 'parent_first_name', 'parent_last_name', 'email', 'child_first_name', 'child_last_name', 'grade', 'year']
  const dup_validation = []
  const values_to_update = []

  for (const [index, key] of keys.entries()) {
    values_to_update.push(`${key} = $${index + 1}`)
    const validation_i = validation.indexOf(key)
    if (validation_i >= 0) {
      validation.splice(validation_i, 1)
      dup_validation.push(`$${index + 1} as ${key}`)
    }
  }

  for (const key of validation) {
    dup_validation.push(`${key}`)
  }

  return `UPDATE leads SET ${values_to_update.join(',') } WHERE id IN( select id from
            (
              select count(l1.id) as count, l1.id from
                ( select id, school_id, ${dup_validation.join(', ')} from leads where id IN (${leads_ids.join(',')})) l1
                join
                ( select id, school_id, ${dup_validation.join(', ')} from leads where school_id = $${values.length + 1}) l2
                ON l1.school_id = l2.school_id
                  AND lower(l1.key_duplicated) = lower(l2.key_duplicated)
                  AND lower(l1.parent_first_name) = lower(l2.parent_first_name)
                  AND lower(l2.parent_last_name) = lower(l2.parent_last_name)
                  AND lower(l1.email) = lower(l2.email)
                  AND lower(l1.child_first_name) = lower(l2.child_first_name)
                  AND lower(l1.child_last_name) = lower(l2.child_last_name)
                  AND lower(l1.grade) = lower(l1.grade)
                  AND COALESCE(l1.year, ''::character varying) = COALESCE(l2.year, ''::character varying)
              group by l1.id
            ) r
          where count <= 1) RETURNING *`
}

/**
 * Returns the query to select a lead history
 * @returns {string}
 */
export function getLeadHistory(): string {
  return `SELECT * FROM leads_history WHERE lead_id = $1 ORDER BY created_at DESC LIMIT 1 `
}

/**
 * @returns {string}
 */
export function bulkInsertLeadFields () {
  return `INSERT INTO lead_fields(school_id, display_name, enabled, field_indentifier,
    type, created_at, updated_at) VALUES `
}

/**
 * @returns {string}
 */
export function updateLeadFields () {
  return   `UPDATE lead_fields
  SET school_id=$1, updated_at=$2, type=$3, field_indentifier=$4, display_name=$5, enabled=$6
  WHERE id=$7`
}


export function proccessGetLeadsForSchoolQueryParams(query_params: GetSchoolIdLeads) {
  const filters: any = {}
  for (const param in query_params) {
    if (GET_LEADS_FOR_SCHOOL_QUERY_PROCESS_1.includes(param)) {
      filters[param] = (query_params[param as keyof GetSchoolIdLeads] || '').replace(/'/g, "''")
    }
    else if (GET_LEADS_FOR_SCHOOL_QUERY_PROCESS_2.includes(param)) {
      filters[param] = query_params[param as keyof GetSchoolIdLeads] || ''
    }
  }

  const pagination_options = {
    page: Number(query_params.page) || 1,
    page_size: Number(query_params.pageSize) || 10,
    page_offset: 0
  }

  pagination_options.page_offset = ((Number(pagination_options.page) - 1) * Number(pagination_options.page_size))

  const lead_sort = {
    field: query_params.fieldSort || 'created_at',
    direction: query_params.fieldDirection || 'DESC'
  }

  if (lead_sort.field === 'parent_first_name') {
    lead_sort.field = `CONCAT(parent_first_name, ' ', parent_last_name)`
  } else if (lead_sort.field === 'child_first_name') {
    lead_sort.field = `CONCAT(child_first_name, ' ', child_last_name)`
  }

  return { filters, pagination_options, lead_sort }
}

export function prepareQuery(query_filters: any, lead_fields: any) {
  const values = []
  let parameters_count = 3
  let query_conditions = ''
  if (!query_filters.lead_id && !query_filters.lead_ids) {
    if (query_filters.text) {
      query_conditions += ` AND (`
      if (lead_fields.length > 0) {
        lead_fields.map((field: any) => {
          if (field.enabled === true) {
            query_conditions += ` ${field.field_indentifier} ilike $${parameters_count} OR`
          }
        })
      }
      query_conditions += ` parent_first_name ilike $${parameters_count} OR
        parent_last_name ilike $${parameters_count} OR
        email            ilike $${parameters_count} OR
        phone            ilike $${parameters_count} OR
        child_first_name ilike $${parameters_count} OR
        child_last_name  ilike $${parameters_count} OR
        lead_source      ilike $${parameters_count} OR
        lead_status      ilike $${parameters_count} OR
        zipcode          ilike $${parameters_count} OR
        last_note        ilike $${parameters_count} OR
        referred_by      ilike $${parameters_count} OR
        referred_by_other ilike $${parameters_count} OR
        id::varchar = $${parameters_count+1})`
      values.push(`%${query_filters.text}%`)
      values.push(`${query_filters.text}`)
      parameters_count+=2
    }

    if (query_filters.year) {
      query_conditions += ` AND COALESCE(year,'N/A') = $${parameters_count}`
      values.push(query_filters.year)
      parameters_count++
    }

    for (const [index, filter] of GET_LEADS_FOR_SCHOOL_QUERY_FILTERS_2.entries()) {
      if (query_filters[filter]) {
        if(filter !== 'leadTransportationNeed'){
          query_conditions += ` AND lower(${GET_LEADS_FOR_SCHOOL_QUERY_FILTERS_2[index]}) = lower($${parameters_count})`
        } else {
          query_conditions += ` AND ${GET_LEADS_FOR_SCHOOL_QUERY_FILTERS_2[index]} = $${parameters_count}`
        }

        values.push(query_filters[filter])
        parameters_count++
      }
    }

    if (query_filters.applicationReceived) {
      query_conditions += ` AND application_received = $${parameters_count}`
      values.push((query_filters.applicationReceived.toLowerCase() === 'yes' ? 'true' : 'false'))
      parameters_count++
    }
    if (query_filters.parent) {
      query_conditions += ` AND(
          COALESCE(parent_first_name,'') ilike $${parameters_count} OR
          COALESCE(parent_last_name,'') ilike $${parameters_count}
      )`
      values.push(`%${query_filters.parent}%`)
      parameters_count++
    }
    if (query_filters.student) {
      query_conditions += ` AND(
          COALESCE(child_first_name,'') ilike $${parameters_count} OR
          COALESCE(child_last_name,'') ilike $${parameters_count}
      )`
      values.push(`%${query_filters.student}%`)
      parameters_count++
    }

    for (const [index, filter] of GET_LEADS_FOR_SCHOOL_QUERY_FILTERS.entries()) {
      if (query_filters[filter]) {
        query_conditions += ` AND COALESCE(${GET_LEADS_FOR_SCHOOL_QUERY_FILTERS_DB[index]},'') ilike $${parameters_count}`
        values.push(query_filters[filter])
        parameters_count++
      }
    }

    for (let i = 0; i < GET_LEADS_FOR_SCHOOL_QUERY_FILTERS_DATES.length; i = i + 3) {
      if (query_filters[GET_LEADS_FOR_SCHOOL_QUERY_FILTERS_DATES[i]] && query_filters[GET_LEADS_FOR_SCHOOL_QUERY_FILTERS_DATES[i + 1]]) {
        query_conditions += ` AND (${GET_LEADS_FOR_SCHOOL_QUERY_FILTERS_DATES[i+2]}>=$${parameters_count} AND ${GET_LEADS_FOR_SCHOOL_QUERY_FILTERS_DATES[i + 2]}<=$${++parameters_count})`
        values.push(query_filters[GET_LEADS_FOR_SCHOOL_QUERY_FILTERS_DATES[i]], query_filters[GET_LEADS_FOR_SCHOOL_QUERY_FILTERS_DATES[i + 1]])
        ++parameters_count
      }
    }

    if (query_filters.enrollmentConfirmed) {
      query_conditions += ` AND COALESCE(enrollment_confirmed,false) = ${(query_filters.enrollmentConfirmed.toLowerCase() === 'yes' ? 'true' : 'false')}`
    }
    if (query_filters.attendanceConfirmed) {
      query_conditions += ` AND COALESCE(attendance_confirmed,false) = ${(query_filters.attendanceConfirmed.toLowerCase() === 'yes' ? 'true' : 'false')}`
    }
    if (query_filters.pqe) {
      query_conditions += ` AND COALESCE(pqe,false) = ${(query_filters.pqe.toLowerCase() === 'yes' ? 'true' : 'false')}`
    }
    if (query_filters.iep) {
      query_conditions += ` AND COALESCE(iep,false) = ${(query_filters.iep.toLowerCase() === 'yes' ? 'true' : 'false')}`
    }
    if (query_filters.sibling_in_school) {
      query_conditions += ` AND COALESCE(sibling_in_school,false) = ${(query_filters.sibling_in_school.toLowerCase() === 'yes' ? 'true' : 'false')}`
    }
    if (query_filters.score_weight_grade) {
      query_conditions += ` AND score_weight_grade='${query_filters.score_weight_grade}'`
    }
  } else if (query_filters.lead_id) {
    query_conditions += `AND id = '${query_filters.lead_id}'`
  } else {
    const ids = query_filters.lead_ids.split(',').map(Number)
    query_conditions += `AND id in (${ids.filter(Number).join(',')})`
  }
  return { query_conditions, values }
}

/**
 * @returns {string}
 */
export function getLeadsRelated () {
  return   `SELECT
  s.id as school_id, s.name as school, s.plan_id, s.subscription_end, s.subscription_months, s.city, s.state,
  l1.id, l1.status, l1.created_at, l1.parent_first_name, l1.parent_last_name, l1.child_first_name, l1.child_last_name,
  l1.email, l1.phone, l1.grade, l1.year
  FROM leads l1
    INNER JOIN (
      SELECT * FROM leads WHERE id=$1 AND school_id=$2) l2 ON lower(l1.parent_first_name)=lower(l2.parent_first_name)
    AND lower(l1.parent_last_name)=lower(l2.parent_last_name)
    AND lower(l1.child_first_name)=lower(l2.child_first_name)
    AND lower(l1.child_last_name)=lower(l2.child_last_name)
    AND lower(l1.email)=lower(l2.email)
    AND l1.grade=l2.grade
    LEFT JOIN schools s ON l1.school_id=s.id
    WHERE l1.id<>$1 AND COALESCE(l1.deleted, false) = false
    ORDER BY id DESC`
}

/**
 * @returns {string}
 */
export function getLeadByData () {
  return   `SELECT
  id
  FROM leads
    WHERE COALESCE(deleted, false) = false AND school_id=$1
    AND lower(parent_first_name)=lower($2)
    AND lower(parent_last_name)=lower($3)
    AND lower(child_first_name)=lower($4)
    AND lower(child_last_name)=lower($5)
    AND lower(email)=lower($6)
    AND COALESCE(grade,'')=COALESCE($7,'')
    AND COALESCE(year,'')=COALESCE($8,'')
    LIMIT 1 `
}

/**
 * @returns {string}
 */
export function getExportTypeApplicationsForSchool () {
  return `SELECT DISTINCT
    COALESCE (l.application_source, 'universal') AS application_source,
    COALESCE(l.application_form_id, '') as application_form_id
  FROM
    leads l
  WHERE
    school_id=$1
    AND COALESCE(l.deleted, false) = false`
}

/**
 * @returns {string}
 */
export function schoolSubscriptionInfo () {
  return `SELECT plan_id, subscription_end, subscription_months
    FROM schools
    WHERE id=$1`
}

/**
 * @returns {string}
 */
export function leadCountForMarketingCampaigns () {
  return `
    SELECT
      language, status, grade, lead_source_id, year, enrollment_confirmed, application_received, sync_intg_status, lead_status_id, intg_campus, count(*) count
    FROM (
      SELECT
        CASE l.language
        WHEN 'spanish' then 'es'
        ELSE 'en'
        END as language
        ,l.status
        ,l.grade
        ,COALESCE(l.lead_source_id, 0) as lead_source_id
        ,l.year
        ,l.enrollment_confirmed
        ,l.application_received
        ,l.sync_intg_status
        ,l.intg_campus
        ,COALESCE(l.lead_status_id, -1) as lead_status_id
      FROM
        leads l
        LEFT JOIN reasons r on l.reason_id=r.id
      WHERE
        COALESCE(l.deleted, false) = false
        AND l.school_id = $1
        AND COALESCE(r.dnc, false)=false
        AND NOT COALESCE(reason_other,'') ilike '%DNC%'
    ) l
    GROUP BY
      l.language,
      l.status,
      l.grade,
      l.lead_source_id,
      l.year,
      l.enrollment_confirmed,
      l.application_received,
      l.sync_intg_status,
      l.lead_status_id,
      l.intg_campus
  `
}

/**
 * @returns {string}
 */
export function getSchoolLatLon () {
  return `SELECT latitude, longitude FROM schools WHERE id = $1`
}

/**
 * @returns {string}
 */
export function potentialLeadsForSchool (
  school_id: number,
  latitude: number,
  longitude: number,
  filters: any,
  sort: any,
  pagination_options: any) {

  let param_count = 4
  const values = []
  let query_filter = ''

  values.push(latitude, longitude, school_id)
  if(filters.text) {
    query_filter += `AND (address ilike $${param_count})`
    values.push(`%${filters.text}%`)
    param_count++
  }
  if(filters.grade) {
    query_filter+= `AND (grade ilike $${param_count} OR  grade = 'all')`
    values.push(filters.grade)
    param_count++
  }
  values.push(
    pagination_options.pageSize,
    (pagination_options.page - 1) * pagination_options.pageSize
  )

  return {
    values,
    query_string: `
    WITH _sh AS (
      SELECT
        sh.id,
        sh.lat,
        sh.lon,
        sh.address,
        sh.user_id,
        sh.grade,
        sh.created_at,
        sh.id search_history_id,
        case
        when lat=cast($1 as real) and lon=cast($2 as real) then 0
        else
        (3959 * acos( cos( radians($1) )
        * cos( radians( lat ) )
        * cos( radians( lon ) - radians(  $2  ) )
        + sin( radians( $1  ) )
        * sin( radians( lat ) )
        )) end distance,
        'potential' type_row,
        row_number() over (partition by sh.user_id order by sh.updated_at desc)
      FROM
        search_history as sh
        left join blacklist_users bs on sh.user_id = bs.user_id
        left join search_history_lead shl on shl.user_id = sh.user_id and shl.school_id = $3
      WHERE
        bs.id is null AND
        shl.id is null AND
        case
        when lat=cast($1 as real) and lon=cast($2 as real) then 0
        else
        (3959 * acos( cos( radians($1) )
              * cos( radians( lat ) )
              * cos( radians( lon ) - radians(  $2  ) )
              + sin( radians( $1  ) )
              * sin( radians( lat ) )
        )) end < 20
        AND sh.user_id is not null
      )
      SELECT
      _sh.*,
        ( SELECT f.name AS top_search_feature
          FROM features AS f
          WHERE f.id = shft.top_1_feature_id
        ),
        ( SELECT f.name AS secondary_feature
          FROM features AS f
          WHERE f.id = shft.top_2_feature_id
        ),
        'potential-leads' status
      FROM
        _sh
        left join search_top_features_history shft on _sh.id = shft.search_history_id
      WHERE
        _sh.row_number = 1
        ${query_filter}
      ORDER BY ${sort.field} ${sort.direction.toLowerCase() === 'asc' ? 'ASC': 'DESC'}
      LIMIT $${param_count}
      OFFSET $${++param_count}`
  }
}

/**
 * @returns {string}
 */
export function getExpiredLeads() {
  return `SELECT id, child_last_name, child_first_name, parent_last_name, parent_first_name, grade, school_id, created_by FROM leads WHERE
  status='new' AND
  lead_source_id = -7 AND lead_source_id = -11
  (
    (note ~* $1 AND EXTRACT(DAY FROM AGE(NOW(),created_at)) >= 19) OR
    (note ~* $2 AND EXTRACT(DAY FROM AGE(NOW(),created_at)) >= 9) OR
    (note ~* $3 AND EXTRACT(DAY FROM AGE(NOW(),created_at)) >= 9)
  ) AND
  id NOT IN
  (
    SELECT id FROM
    (
      SELECT lead_id AS id FROM sms_audits UNION ALL SELECT lead_id AS id FROM email_audits UNION ALL SELECT lead_id AS id FROM message_counts
    ) AS data GROUP BY id
  )`
}

/**
 * @returns {string}
 */
export function getMatchingLeads(leads_ids: Array<number>) {

  const intElem = leads_ids.filter(lead_id => Number.isInteger(lead_id))
  if (intElem.length !== leads_ids.length) {
    throw badRequestError
  }

  return `SELECT id FROM leads WHERE
  status='new' AND
  lead_source_id = -7 AND
  lead_source_id = -11 AND
  id not in (${leads_ids.join(',')}) AND
  parent_first_name = $1 AND
  parent_last_name = $2 AND
  child_first_name = $3 AND
  child_last_name = $4 AND
  "grade" = $5`
}

/**
 * Returns the query to search the leads by id and school_id
 */
export function leadSourceCost (query_params: LeadSourceCostGet) {
  let query_filter = ''
  let param_count = 1
  const values = []

  query_filter += `AND lsc.school_id = $${param_count} `
  values.push(query_params.school_id)
  param_count++

  if (query_params.start_date) {
    query_filter += `AND lsc.start_date >=  $${param_count} `
    const start_date = moment(query_params.start_date, 'YYYY-MM-DD HH:mm:ss').utc()
    values.push(start_date)
    param_count++
  }
  if (query_params.end_date) {
    query_filter += `AND lsc.end_date <=  $${param_count} `
    const end_date = moment(query_params.end_date, 'YYYY-MM-DD HH:mm:ss').utc()
    values.push(end_date)
    param_count++
  }
  if (query_params.source_id) {
    query_filter += `AND lsc.sources_id =  $${param_count} `
    values.push(query_params.source_id)
    param_count++
  }

  const count_query = `
  SELECT count(lsc.id)
  FROM
    lead_source_costs lsc
    left join lead_sources ls on lsc.sources_id = ls.id
  WHERE
    lsc.deleted = false
    ${query_filter}
  `
  const main_query = `
  SELECT lsc.*
  FROM
    lead_source_costs lsc
    left join lead_sources ls on lsc.sources_id = ls.id
  WHERE
    lsc.deleted = false
    ${query_filter}
  ORDER BY ${query_params.fieldSort} ${query_params.fieldDirection}
  LIMIT $${param_count} OFFSET $${++param_count} `

  return { main_query, count_query, values }
}

/**
 * Returns the query to get the count of leads for a given school
 * Query Params:
 * $1 school_id {number}
 * @returns {string}
 */
export function getLeadsForSchoolCountByStatus() {
  return `SELECT status, COUNT(*) FROM (
    SELECT status FROM leads WHERE school_id = $1 AND COALESCE(deleted, false) = false
  ) r GROUP BY status`
}

/**
 * Returns the query to get a list of leads
 * Query Params:
 * $1 leads_ids {Array<number>}
 * $2 school_id {number}
 * @returns {string}
 */
export function getLeads(): string {
  return `SELECT id, school_id, application_token, status, parent_relationship, parent_first_name,
  parent_last_name, phone, email, preferred_contact, child_first_name, child_last_name, grade, 
  scholamatch_url, year, country, city, state, zipcode
  FROM leads WHERE id = ANY($1::int[]) AND school_id = $2`
}

/**
 * Returns the query to update a given lead's note
 * Query Params:
 * $1 lead_id {number}
 * $2 note {string}
 * @returns {string}
 */
export function updateLeadNote(): string {
  return `UPDATE leads SET note = $2 WHERE id = $1 RETURNING *`
}

/**
 * Returns the query to the the leads history for a given school and an array of leads
 * Query Params:
 * $1 school_id {number}
 * $2 leads_ids {Array<number>}
 * @returns {string}
 */
export function getLeadsHistory(): string {
  return `SELECT id, lead_id, field, old_value, new_value, created_at, updated_at
  FROM leads_history WHERE school_id = $1 AND lead_id = ANY($2::int[])`
}

/**
 * Returns the query to the list of leads ids for a given school
 * Query Params:
 * $1 school_id {number}
 * @returns {string}
 */
export function getLeadsIdsBySchool(): string {
  return `SELECT id FROM leads WHERE school_id = $1`
}

/**
 * Returns the query to the list of filtered by email and where the user_id is null
 * Query Params:
 * $1 email {string}
 * @returns {string}
 */
export function getUnassociatedLeadByEmail(): string {
  return `SELECT id, school_id, application_token, status, parent_relationship, parent_first_name,
  parent_last_name, phone, email, preferred_contact, child_first_name, child_last_name, grade
  FROM leads WHERE email = $1 AND user_id IS NULL`
}

/**
 * Returns the query to update a given lead's note
 * Query Params:
 * $1 lead_id {number}
 * $2 user_id {string}
 * @returns {string}
 */
export function updateLeadUserId(): string {
  return `UPDATE leads SET user_id = $2 WHERE id = $1 RETURNING *`
}

/**
 * Returns the query to get a list of lead sources for a given school
 * Query Params:
 * $1 lead_id {number}
 * $2 user_id {string}
 * @returns {string}
 */
export function getLeadSources(): string {
  return `SELECT id, school_id, name, deleted, created_at, updated_at
  FROM lead_sources WHERE school_id = $1 AND deleted = FALSE`
}

/**
 * Returns the query to update a given lead's note
 * Query Params:
 * $1 lead_id {number}
 * $2 user_id {string}
 * @returns {string}
 */
export function deleteLeads(): string {
  return `UPDATE leads SET deleted_by = $3, deleted = TRUE, deleted_at = NOW(), updated_at = NOW(), key_duplicated = concat('DEL-', id)
  WHERE id = ANY($1::int[]) AND school_id = $2 RETURNING *`
}

/**
 * Returns the query to update a given lead's note
 * Query Params:
 * $1 lead_id {number}
 * $2 user_id {string}
 * @returns {string}
 */
export function getLeadsToRemind(): string {
  const queries = REMINDER_PERIODS.map((period) => `SELECT '${period.init}' as period, COUNT(*) FROM leads
    WHERE viewed_at IS NULL
    AND school_id = $1 AND status = 'new'
    AND COALESCE(deleted, false) = false
    AND DATE_PART('day', current_date - created_at) * 24 + DATE_PART('hour', current_date - created_at)
    BETWEEN ${period.init} AND ${period.end}
    AND (last_notify IS NULL ${period.lastNotify ? `OR last_notify = '${period.lastNotify}'` : ''})`)
  return queries.join(' UNION ')
}

/**
 * Returns the query to get the lead transportation for a given lead and school
 * Query Params:
 * $1 lead_id {number}
 * $2 school_id {number}
 * @returns {string}
 */
export function getLeadTransportation(): string {
  return `SELECT lead_transportations.*, reason_id
  FROM (
    SELECT id, school_id, lead_id, carpool, free_transport_preferred, need, created_at, updated_at
    FROM lead_transportations
    WHERE lead_id = $1 AND school_id = $2
  ) lead_transportations
  LEFT JOIN lead_transportation_reasons ON lead_transportations.id = lead_transportation_reasons.lead_transportation_id
  ;`
}

/**
 * Returns the query to get the count of latest new leads for a given school and since a given date
 * Query Params:
 * $1 school_id {number}
 * $2 since_date {string}
 * @returns {string}
 */
export function getCountLatestNewLeads(): string {
  return `SELECT COUNT(*) FROM leads
  WHERE school_id = $1 AND COALESCE(deleted, false) = false AND status = 'new'
  AND created_at >= $2;`
}

/**
 * Returns the query to get the count of latest applications received for a given school and since a given date
 * Query Params:
 * $1 school_id {number}
 * $2 since_date {string}
 * @returns {string}
 */
export function getCountLatestNewApplications(): string {
  return `SELECT COUNT(*) FROM leads
  WHERE school_id = $1 AND COALESCE(deleted, false) = false AND status = 'application-received'
  AND updated_at >= $2;`
}

/**
 * Returns the query to get the count of latest leads accepted for a given school and since a given date
 * Query Params:
 * $1 school_id {number}
 * $2 since_date {string}
 * @returns {string}
 */
export function getCountLatestNewAccepted(): string {
  return `SELECT COUNT(*) FROM leads
  WHERE school_id = $1 AND COALESCE(deleted, false) = false AND status = 'accepted'
  AND updated_at >= $2;`
}

/**
 * Returns the query to get the lead year options for a given school
 * Query Params:
 * $1 school_id {number}
 * @returns {string}
 */
export function getLeadYearOptions(): string {
  return `SELECT year FROM leads WHERE school_id = $1
  AND COALESCE(year, '') != ''
  AND COALESCE(deleted, false) = false
  GROUP BY year;`
}

/**
 * Returns the query to get the list of completed applications for a list of ids from a school
 * Query Params:
 * $1 school_id {number}
 * $2 app_ids {Array<number>}
 * @returns {string}
 */
export function getCompletedApplicationsForschool(): string {
  return `SELECT completed_applications.id, schools.name as school_name,
  leads.note as lead_note,
  completed_applications.application as data
  FROM
  (
  	SELECT id, school_id, application FROM completed_applications WHERE school_id = $1 AND id = $2
  ) completed_applications
  LEFT JOIN schools ON schools.id = completed_applications.school_id
  LEFT JOIN leads ON leads.application_id = completed_applications.id
;`
}

/**
 * Returns the query to get the leads information from a given school and a list of status to export it
 * Query Params:
 * $1 school_id {number}
 * $2 status {Array<string>}
 * @returns {string}
 */
export function getLeadsExport(): string {
  return `SELECT * FROM
  (
    SELECT
      l.id,
      l.school_id,
      l.parent_first_name,
      l.parent_last_name,
      l.user_id,
      l.email,
      l.phone,
      l.preferred_contact,
      l.child_first_name,
      l.child_last_name,
      l.grade,
      l.child_birthdate,
      l.status,
      l.application_token,
      l.application_id,
      l.parent_relationship,
      l.tour_requested,
      l.note,
      l.created_at,
      l.user_student_id,
      l.year,
      coalesce(l.language, 'english') as language,
      l.custom_field_1,
      l.custom_field_2,
      l.custom_field_3,
      l.custom_field_4,
      l.custom_field_5,
      l.lead_source_id,
      Case
        when l.lead_source_id = -1 then 'ScholaMatch'
        when l.lead_source_id = -2 then 'GeneralLead'
        when l.lead_source_id = -3 then 'ScholaMatch2'
        when l.lead_source_id = -4 then 'Schola School Campaign'
        when l.lead_source_id = -5 then 'ScholaMatch3'
        when l.lead_source_id = -6 then 'PotentialLeadsRO'
        when l.lead_source_id = -7 then 'AutoScholaMatch'
        when l.lead_source_id = -8 then 'Schola Pre-Qualify'
        when l.lead_source_id = -9 then 'Scholamatch SPOT'
        when l.lead_source_id = -10 then 'Schola Profile A'
        when l.lead_source_id = -11 then 'Schola General Campaign - ASM'
        else lsource.name
      End lead_source,
      l.lead_status_id,
      lstatus.name as lead_status,
      l.zipcode,
      viewed_at,
      Case
        When l.reason_id is NULL
        AND l.reason_other is not null then -1
        else l.reason_id
      End reason_id,
      COALESCE(r.name, l.reason_other) reason,
      l.address,
      l.latitude,
      l.longitude,
      l.address_description,
      l.state,
      l.city,
      l.external_campaign_id,
      l.referred_by,
      l.referred_by_other,
      l.country,
      l.assigned_to,
      l.sync_intg_status,
      l.sync_intg_with,
      l.iep,
      l.sibling_in_school,
      l.district,
      l.intg_lead_id,
      l.intg_campus,
      l.intg_app_id,
      l.sync_intg_date,
      case
        when not app.id is null then 'Yes'
        else (
          case
            when l.application_received = true then 'Yes'
            else 'No'
          end
        )
      end application_received,
      coalesce(app.created_at, l.application_received_at) application_received_at,
      case
        when coalesce(l.enrollment_confirmed, false) = true then 'Yes'
        else 'No'
      end enrollment_confirmed,
      l.enrollment_confirmed_at,
      case
        when l.application_valid = true then 'Yes'
        else 'No'
      end application_valid,
      case
        when lt.need = 'Yes' then 'Yes'
        else 'No'
      end lead_transportation_need,
      COALESCE(
        (
          Select
            case
              when n.updated_at > l.updated_at then n.updated_at
              else l.updated_at
            end updated_at
          From
            Notes n
          Where
            n.object_type = 'lead'
            and n.object_id = l.id
          order by
            n.id desc
          limit
            1
        ), l.updated_at
      ) as updated_at,
      (
        Select
          left(n.note, 50) as last_note
        From
          Notes n
        Where
          n.object_type = 'lead'
          and n.object_id = l.id
        order by
          n.id desc
        limit
          1
      ) as last_note,
      (
        Select
          sum(total)
        from
          (
            Select
              count(*) as total
            From
              Notes n
            Where
              n.object_type = 'lead'
              and n.object_id = l.id
            union
            all
            Select
              count(*) as total
            From
              sms_audits
            Where
              lead_id = l.id
            union
            all
            Select
              count(*) as total
            From
              email_audits
            Where
              lead_id = l.id
          ) n
      ) as notes_count,
      (
        select
          string_agg(note, chr(10)) notes
        from
          notes
        where
          object_type = 'lead'
          and object_id = l.id
        group by
          object_id
      ) notes,
      l.external_campaign_id,
      l.year_accepted
    FROM
      public.leads l
      left join public.lead_sources lsource on lsource.id = l.lead_source_id
      left join public.lead_statuses lstatus on lstatus.id = l.lead_status_id
      left join reasons r on l.reason_id = r.id
      left join lead_transportations lt on l.id = lt.lead_id
      left join completed_applications app on l.application_id = app.id
    WHERE
      l.school_id = $1
      AND l.status = ANY ($2::varchar[])
      AND COALESCE(l.deleted, false) = false
  ) result
WHERE
  1 = 1;`
}

/**
 * Returns the query to get all the leads list by school
 * Query Params:
 * $1 school_id {number}
 * @returns {string}
 */
export function getAllLeadsExport(): string {
  return `SELECT
  *
FROM
  (
    SELECT
      l.id,
      l.school_id,
      l.parent_first_name,
      l.parent_last_name,
      l.user_id,
      l.email,
      l.phone,
      l.preferred_contact,
      l.child_first_name,
      l.child_last_name,
      l.grade,
      l.child_birthdate,
      l.status,
      l.application_token,
      l.application_id,
      l.parent_relationship,
      l.tour_requested,
      l.note,
      l.created_at,
      l.user_student_id,
      l.year,
      coalesce(l.language, 'english') as language,
      l.custom_field_1,
      l.custom_field_2,
      l.custom_field_3,
      l.custom_field_4,
      l.custom_field_5,
      l.lead_source_id,
      Case
        when l.lead_source_id = -1 then 'ScholaMatch'
        when l.lead_source_id = -2 then 'GeneralLead'
        when l.lead_source_id = -3 then 'ScholaMatch2'
        when l.lead_source_id = -4 then 'Schola School Campaign'
        when l.lead_source_id = -5 then 'ScholaMatch3'
        when l.lead_source_id = -6 then 'PotentialLeadsRO'
        when l.lead_source_id = -7 then 'AutoScholaMatch'
        when l.lead_source_id = -8 then 'Schola Pre-Qualify'
        when l.lead_source_id = -9 then 'Scholamatch SPOT'
        when l.lead_source_id = -10 then 'Schola Profile A'
        when l.lead_source_id = -11 then 'Schola General Campaign - ASM'
        else lsource.name
      End lead_source,
      l.lead_status_id,
      lstatus.name as lead_status,
      l.zipcode,
      viewed_at,
      Case
        When l.reason_id is NULL
        AND l.reason_other is not null then -1
        else l.reason_id
      End reason_id,
      COALESCE(r.name, l.reason_other) reason,
      l.address,
      l.latitude,
      l.longitude,
      l.address_description,
      l.state,
      l.city,
      l.external_campaign_id,
      l.referred_by,
      l.referred_by_other,
      (
        Select
          case
            when n.updated_at > l.updated_at then n.updated_at
            else l.updated_at
          end updated_at
        From
          Notes n
        Where
          n.object_type = 'lead'
          and n.object_id = l.id
        order by
          n.id desc
        limit
          1
      ) as updated_at,
      (
        Select
          left(n.note, 50) as last_note
        From
          Notes n
        Where
          n.object_type = 'lead'
          and n.object_id = l.id
        order by
          n.id desc
        limit
          1
      ) as last_note,
      (
        Select
          sum(total)
        from
          (
            Select
              count(*) as total
            From
              Notes n
            Where
              n.object_type = 'lead'
              and n.object_id = l.id
            union
            all
            Select
              count(*) as total
            From
              sms_audits
            Where
              lead_id = l.id
            union
            all
            Select
              count(*) as total
            From
              email_audits
            Where
              lead_id = l.id
          ) n
      ) as notes_count,
      (
        select
          string_agg(note, chr(10)) notes
        from
          notes
        where
          object_type = 'lead'
          and object_id = l.id
        group by
          object_id
      ) notes,
      l.year_accepted
    FROM
      public.leads l
      left join public.lead_sources lsource on lsource.id = l.lead_source_id
      left join public.lead_statuses lstatus on lstatus.id = l.lead_status_id
      left join reasons r on l.reason_id = r.id
    WHERE
      l.school_id = $1
      AND COALESCE(l.deleted, false) = false
  ) result
WHERE
  1 = 1`
}

/**
 * Returns the query to get the application fields
 * @returns {string}
 */
export function getApplicationFields(): string {
  return `SELECT id, section_id, text FROM application_fields;`
}

/**
 * Returns the query to get the jotform applications for export
 * Query Params:
 * $1 school_id {number}
 * $2 application_form_id {string}
 * @returns {string}
 */
export function getApplicationsForExportJotform(): string {
  return `SELECT l.id as lead_id, email, grade, phone, child_last_name, child_first_name, parent_last_name, parent_first_name, parent_relationship,
  preferred_contact, custom_field_1, custom_field_2, custom_field_3, custom_field_4, custom_field_5,
  status, r.name as reason,
  completed_applications.application AS data 
  FROM completed_applications
  INNER JOIN leads l ON l.application_id = completed_applications.id
  LEFT JOIN reasons r ON r.id = l.reason_id
  WHERE l.application_form_id = $2
  AND completed_applications.school_id = $1;`
}

/**
 * Returns the query to get the application for export
 * Query Params:
 * $1 school_id {number}
 * @returns {string}
 */
export function getApplicationsForExport(): string {
  return `SELECT l.id as lead_id, email, grade, phone, child_last_name, child_first_name, parent_last_name, parent_first_name, parent_relationship,
  preferred_contact, ca.application as data, custom_field_1, custom_field_2, custom_field_3, custom_field_4, custom_field_5,
  status, r.name as reason
FROM
  leads l
  LEFT JOIN completed_applications ca ON l.application_id = ca.id
  LEFT JOIN reasons r ON r.id = l.reason_id
WHERE
  l.school_id = $1
  AND application_id IS NOT NULL
  AND l.application_source IS NULL
  AND l.status in ('accepted', 'archived')
  AND COALESCE(l.deleted, false) = false;`
}

/**
 * Returns the query to get the application for export individual
 * Query Params:
 * $1 school_id {number}
 * $1 application_id {string}
 * @returns {string}
 */
export function getApplicationsForExportIndividual(): string {
  return `SELECT email, grade, phone, child_last_name, child_first_name, parent_last_name, parent_first_name,
  parent_relationship, preferred_contact, ca.application as data, custom_field_1, custom_field_2,
  custom_field_3, custom_field_4, custom_field_5, status
FROM leads l
  INNER JOIN completed_applications ca ON l.application_id = ca.id
  AND ca.id = $2
WHERE
  l.school_id = $1
  AND COALESCE(l.deleted, false) = false;`
}

/**
 * Returns the query to get the application for export individual
 * Query Params:
 * $1 school_id {number}
 * $1 lead_ids {Array<number>}
 * @returns {string}
 */
export function getApplicationsByIds(): string {
  return `Select email, grade, phone, child_last_name, child_first_name, parent_last_name,
  parent_first_name, parent_relationship, preferred_contact, ca.application as data, custom_field_1, custom_field_2,
  custom_field_3, custom_field_4, custom_field_5, status
From leads l
  left join completed_applications ca on l.application_id = ca.id
Where
  l.school_id = $1
  AND l.id = ANY ($2::int[])
  AND COALESCE(l.deleted, false) = false;`
}

/**
 * Returns the query to get the count of leads by period
 * @returns {string}
 */
export function getCountBySchool(last_notify: string): string {
  return `SELECT school_id, COUNT(id) FROM leads
    WHERE viewed_at IS NULL
    AND COALESCE(deleted, false) = false
    AND DATE_PART('day', current_date - created_at) * 24 + DATE_PART('hour', current_date - created_at)
    BETWEEN $1 AND $2
    AND (last_notify IS NULL ${last_notify ? `OR last_notify = $3` : ''})
    GROUP BY school_id`
}

/**
 * Returns the query to get the last lead by school and phone
 * Query Params:
 * $1 school_id {number}
 * $2 phone {string}
 * @returns {string}
 */
export function getLeadBySchoolAndPhone(): string {
  return `
  SELECT id as lead_id, school_id
  FROM leads
  WHERE
    school_id= $1 AND
    CASE
      WHEN regexp_replace(phone, '[^0-9,+]', '', 'g') ILIKE '+%' then regexp_replace(phone, '[^0-9,+]', '', 'g')
      ELSE concat('+1', regexp_replace(phone, '[^0-9,+]', '', 'g'))
    END = $2
    AND COALESCE(deleted, false) = false
  ORDER BY id DESC
  LIMIT 1`
}

/**
 * Returns the query to get an email from the lead
 * @returns {string}
 */
export function getLeadEmailFromPhone(): string {
  return `select LOWER(email) as email, INITCAP(parent_first_name) as parent_first_name, INITCAP(parent_last_name) as parent_last_name, LOWER(COALESCE(language,'english')) as language from leads where RIGHT(NULLIF(regexp_replace(phone, '\\D','','g'), '')::text, 10) = $1 and email like '%@%' order by created_at desc`
}

/**
 * Returns the query to get an email from the lead
 * @returns {string}
 */
export function getAiResponses(): string {
  // return `SELECT DISTINCT ON (lead_id, req_type) * FROM leads_ai_responses WHERE lead_id = $1 ORDER BY lead_id, req_type, created_at DESC`
  return `SELECT * FROM leads_ai_responses WHERE lead_id = $1 ORDER BY created_at DESC`
}

/**
 * Returns the query to create new lead_integrations row
 * @returns {string}
 */
export function createLeadIntegration(): string {
  return `INSERT INTO lead_integrations (lead_id, created_at, sync_intg_with, intg_data) VALUES ($1,now(),$2,$3)`
}

/**
 * Returns the query to create new lead_integrations row
 * @returns {string}
 */
export function selectLeadIntegrationExtraData(): string {
  return `select li.intg_data, li.created_at from lead_integrations li join leads l on l.id = li.lead_id where li.lead_id = $1 and l.school_id = $2`
}

/**
 * Returns the query to get the lead by school_id and phone
 * @returns {string}
 */
export function getLeadBySchoolIdAndPhone(): string {
  return `SELECT * FROM leads where RIGHT(NULLIF(regexp_replace(phone, '\\D','','g'), '')::text, 10) = $1 and school_id = $2 and (deleted != true or deleted isnull) order by CASE status
          WHEN 'new' THEN 1
          WHEN 'application-sent' THEN 2
          WHEN 'application-received' THEN 3
          WHEN 'waitlisted' THEN 4
          WHEN 'accepted' THEN 5
          WHEN 'declined' THEN 6
          WHEN 'archived' THEN 7
          ELSE 8
          END;`
}

/**
 * Updates the name of a school's lead source in the database.
 * @returns {string} The SQL query string for updating a school's lead source.
 */
export function updateSchoolLeadSource(): string {
  return `UPDATE lead_sources SET name = $3, updated_at = $4 WHERE school_id = $1 AND id = $2 RETURNING *;`
}

/**
 * Generates a query to soft-remove (deactivate) a school lead source by updating its status.
 * This function does not delete the record but marks it as inactive.
 * @returns {string} The SQL query string for soft-removing a school's lead source.
 */
export function removeSchoolLeadSource(): string {
  return `UPDATE lead_sources SET deleted = true, updated_at = NOW() WHERE school_id = $1 AND id = $2 RETURNING *;`
}

/**
 * Generates a query to soft-remove (deactivate) a school lead source by updating its status.
 * This function does not delete the record but marks it as inactive, typically by setting a flag in a column.
 * @returns {string} The SQL query string for soft-removing a school's lead source.
 */
export function leadSourceUnlink(): string {
  return `UPDATE leads SET lead_source_id = NULL WHERE school_id = $1 AND lead_source_id = $2`
}


/**
 * Generates a SQL query to count the number of leads associated with a specific lead source for a given school.
 * This function is useful for assessing the impact of unlinking or removing a lead source by determining how many leads are currently associated with it.
 * @returns {string} The SQL query string for counting leads associated with a specific source for a given school.
 */
export function countLeadsBySource(): string {
  return `SELECT COUNT(*) FROM leads WHERE school_id = $1 AND lead_source_id = $2`
}

/**
 * Generates a query to update the name of a school lead stage in the database.
 * @returns {string} The SQL query string for updating a school's lead stage name.
 */
export function updateSchoolLeadStage(): string {
  return `UPDATE lead_statuses SET name = $3, updated_at = NOW() WHERE school_id = $1 AND id = $2 AND deleted = false RETURNING *;`
}


/**
 * Generates a query to soft-remove (deactivate) a school lead stage by updating its status.
 * This function does not delete the record but marks it as inactive, typically by setting a flag in a column.
 * @returns {string} The SQL query string for soft-removing a school's lead stage.
 */
export function removeSchoolLeadStage(): string {
  return `UPDATE lead_statuses SET deleted = true, updated_at = NOW() WHERE school_id = $1 AND id = $2 AND deleted = false RETURNING *;`
}

/**
 * Generates a query to soft-remove (deactivate) a school lead stage by updating its status.
 * This function does not delete the record but marks it as inactive, typically by setting a flag in a column.
 * @returns {string} The SQL query string for soft-removing a school's lead stage.
 */
export function leadStageUnlink(): string {
  return `UPDATE leads SET lead_source_id = NULL WHERE school_id = $1 AND lead_status_id = $2`
}

/**
 * Generates a SQL query to count the number of leads associated with a specific lead stage for a given school.
 * This function is useful for assessing the impact of unlinking or removing a lead stage by determining how many leads are currently associated with it.
 * @returns {string} The SQL query string for counting leads associated with a specific stage for a given school.
 */
export function countLeadsByStage(): string {
  return `SELECT COUNT(*) FROM leads WHERE school_id = $1 AND lead_status_id = $2`
}

/**
 * Generates a SQL query get the referred_by list
 * @returns {string}
 */
export function getHearAboutUs(): string {
  return `select  referred_by  from leads where school_id = $1 group by referred_by`
}

/**
 * Generates a SQL query to get integration status lead by school
 * @returns {string}
*/
export function getIntegrationStatus(): string {
  return `
    SELECT DISTINCT sync_intg_status
    FROM leads
    WHERE school_id = $1
    AND COALESCE(sync_intg_status, '') != ''
    AND COALESCE(deleted, false) = false
  `
}

/**
 * Generates a SQL query to get integration campus lead by school
 * @returns {string}
*/
export function getIntegrationCampus(): string {
  return `
    SELECT DISTINCT(intg_campus) as campus
    FROM leads
    WHERE school_id = $1
    AND COALESCE(intg_campus, '') != ''
    AND COALESCE(deleted, false) = false
  `
}

/**
 * Generates a SQL query with unresponsive leads
 * @returns {string}
 */
export function getUnresponsiveLeads(): string {
  return `select
	l.id as lead_id,
	s.name as school_name,
	CASE
	  WHEN lower(concat(l.parent_first_name, ' ', l.parent_last_name)) like '%nolastname%' or
	   	  lower(concat(l.parent_first_name, ' ', l.parent_last_name)) like '%tbd%' or
		  lower(concat(l.parent_first_name, ' ', l.parent_last_name)) like 'x %' or
		  lower(concat(l.parent_first_name, ' ', l.parent_last_name)) like '% x' or
		  lower(concat(l.parent_first_name, ' ', l.parent_last_name)) like '%n/a%' or
		  lower(concat(l.parent_first_name, ' ', l.parent_last_name)) like '% na' or
		  lower(concat(l.parent_first_name, ' ', l.parent_last_name)) like '% a' or
		  lower(concat(l.parent_first_name, ' ', l.parent_last_name)) like 'na %' or
		  lower(concat(l.parent_first_name, ' ', l.parent_last_name)) like 'a %' or
		  lower(concat(l.parent_first_name, ' ', l.parent_last_name)) like '%provided%'
	  THEN 'Parent'
	ELSE
	  INITCAP(REGEXP_REPLACE(lower(concat(l.parent_first_name, ' ', l.parent_last_name)), '[^a-zA-Z ]', '', 'g'))
	end as parent_name,
	CASE
	  WHEN lower(concat(l.child_first_name, ' ', l.child_last_name)) like '%nolastname%' or
	   	  lower(concat(l.child_first_name, ' ', l.child_last_name)) like '%tbd%' or
		  lower(concat(l.child_first_name, ' ', l.child_last_name)) like 'x %' or
		  lower(concat(l.child_first_name, ' ', l.child_last_name)) like '% x' or
		  lower(concat(l.child_first_name, ' ', l.child_last_name)) like '%n/a%' or
		  lower(concat(l.child_first_name, ' ', l.child_last_name)) like '% na' or
		  lower(concat(l.child_first_name, ' ', l.child_last_name)) like '% a' or
		  lower(concat(l.child_first_name, ' ', l.child_last_name)) like 'na %' or
		  lower(concat(l.child_first_name, ' ', l.child_last_name)) like 'a %' or
		  lower(concat(l.child_first_name, ' ', l.child_last_name)) like '%provided%'
	  THEN 'your child'
	  ELSE
	  	  INITCAP(REGEXP_REPLACE(lower(concat(l.child_first_name, ' ', l.child_last_name)), '[^a-zA-Z ]', '', 'g'))
	end as child_name,
	case
		when RIGHT(NULLIF(regexp_replace(l.phone, '\\D','','g'), '')::text, 10) in (select RIGHT(NULLIF(regexp_replace("to", '\\D','','g'), '')::text, 10) from blacklist_communication where school_id = -1)
		then null
		else
		  RIGHT(NULLIF(regexp_replace(l.phone, '\\D','','g'), '')::text, 10)
	end as phone,
	l.email as email
from leads l left join schools s on s.id = l.school_id where l.id in
(select max(id) FROM
    leads where
    reason_id = 7 and
    lead_source_id != -7 and
    lead_source_id != -11 and
    email not like '%schola%' and
    email not like '%null%' and
    email not like 'na@%' and
    email not like 'x@%' and
    email not like 'noemail@%' and
    email not like 'none@%' and
    email not like 'n/a@%' and
    email not like '%test%' and
    email not like '%appconfirm%'
GROUP BY lower(email)) and l.created_at > $1 limit 900 OFFSET $2`
}

/**
 * Generates a SQL query to change archived reason to LeadReassigned (email follow up to unresponsive leads)
 * @returns {string}
 */
export function updateUnresponsiveReassignedLeads(): string {
  return `update leads set reason_id = (select id from reasons where name = 'Lead Reassigned') where lower(email) = lower($1) and id != $2 and reason_id = (select id from reasons where name = 'Unresponsive')`
}

/**
 * Generates a SQL query to change archived reason to LeadReassigned (email follow up to unresponsive leads)
 * @returns {string}
 */
export function getDuplicatesToFlagByPhone(filters: string): string {
  return `SELECT
            LEAST(by_phone.fid, by_phone.id) id_dup1,
            GREATEST(by_phone.fid, by_phone.id) id_dup2,
            MAX(by_phone.pf_sim) pf_sim,
            MAX(by_phone.pl_sim) pl_sim,
            MAX(by_phone.cf_sim) cf_sim,
            MAX(by_phone.cl_sim) cl_sim,
            MAX(by_phone.child_birthdate) child_birthdate,
            MAX(by_phone.email_sim) email_sim
          FROM
          (SELECT DISTINCT * FROM
          (SELECT DISTINCT
              l.id fid,
              r.id,
              word_similarity(l.parent_first_name, r.parent_first_name) pf_sim,
              word_similarity(l.parent_last_name, r.parent_last_name) pl_sim,
              word_similarity(l.child_first_name, r.child_first_name) cf_sim,
              word_similarity(l.child_last_name, r.child_last_name) cl_sim,
              word_similarity(l.email, r.email) email_sim,
              word_similarity(l.child_birthdate::text, r.child_birthdate::text) child_birthdate,
              l.year lyear,
              r.year ryear,
              l.note,
              r.note
            FROM (SELECT id, unaccent(lower(parent_first_name)) parent_first_name, unaccent(lower(parent_last_name)) parent_last_name, unaccent(lower(child_first_name)) child_first_name, unaccent(lower(child_last_name)) child_last_name, lower(email) email, phone, child_birthdate, year, note FROM leads WHERE school_id = $1 AND ${filters} (deleted = false or deleted isnull) AND phone not in ('1234567890','1111111111','n/a','1234567899','************','0000000000')) l
            JOIN (SELECT id, unaccent(lower(parent_first_name)) parent_first_name, unaccent(lower(parent_last_name)) parent_last_name, unaccent(lower(child_first_name)) child_first_name, unaccent(lower(child_last_name)) child_last_name, lower(email) email, phone, child_birthdate, year, note FROM leads WHERE school_id = $1 AND ${filters} (deleted = false or deleted isnull) AND phone not in ('1234567890','1111111111','n/a','1234567899','************','0000000000')) r
            ON RIGHT(NULLIF(regexp_replace(l.phone, '\\D', '', 'g'), '')::text, 10) = RIGHT(NULLIF(regexp_replace(r.phone, '\\D', '', 'g'), '')::text, 10) AND l.id != r.id and (r.note not ILIKE concat('%non-dup-|',l.id,'|%') or l.note not ilike concat('%non-dup-|',r.id,'|%') OR l.note IS null OR r.note IS null)) leads_res WHERE ((pf_sim > 0.60 or pl_sim > 0.60) AND (cf_sim > 0.70 AND cl_sim > 0.60)) AND (lyear = ryear or lyear isnull or ryear isnull or lyear = '' or ryear = '')
          ) by_phone GROUP BY LEAST(by_phone.fid, by_phone.id), GREATEST(by_phone.fid, by_phone.id);`
}

/**
 * Generates a SQL query to change archived reason to LeadReassigned (email follow up to unresponsive leads)
 * @returns {string}
 */
export function getDuplicatesToFlagByEmail(filters: string): string {
  return `SELECT
            LEAST(by_email.fid, by_email.id) id_dup1,
            GREATEST(by_email.fid, by_email.id) id_dup2,
            MAX(by_email.pf_sim) pf_sim,
            MAX(by_email.pl_sim) pl_sim,
            MAX(by_email.cf_sim) cf_sim,
            MAX(by_email.cl_sim) cl_sim,
            MAX(by_email.child_birthdate) child_birthdate,
            MAX(by_email.phone_sim) phone_sim
          FROM
          (SELECT DISTINCT * FROM
          (SELECT distinct
            l.id fid,
            r.id,
            word_similarity(l.parent_first_name, r.parent_first_name) pf_sim,
            word_similarity(l.parent_last_name, r.parent_last_name) pl_sim,
            word_similarity(l.child_first_name, r.child_first_name) cf_sim,
            word_similarity(l.child_last_name, r.child_last_name) cl_sim,
            word_similarity(RIGHT(NULLIF(regexp_replace(l.phone, '\\D', '', 'g'), '')::text, 10)::text, RIGHT(NULLIF(regexp_replace(r.phone, '\\D', '', 'g'), '')::text, 10)::text) phone_sim,
            word_similarity(l.child_birthdate::text, r.child_birthdate::text) child_birthdate,
            l.year lyear,
            r.year ryear,
            l.note,
            r.note
          FROM (SELECT id, unaccent(lower(parent_first_name)) parent_first_name, unaccent(lower(parent_last_name)) parent_last_name, unaccent(lower(child_first_name)) child_first_name, unaccent(lower(child_last_name)) child_last_name, lower(email) email, phone, child_birthdate, year, note FROM leads WHERE school_id = $1 AND ${filters} (deleted = false or deleted isnull) and email not in ('<EMAIL>','<EMAIL>','n/<EMAIL>','<EMAIL>','n/a','<EMAIL>')) l
          JOIN (SELECT id, unaccent(lower(parent_first_name)) parent_first_name, unaccent(lower(parent_last_name)) parent_last_name, unaccent(lower(child_first_name)) child_first_name, unaccent(lower(child_last_name)) child_last_name, lower(email) email, phone, child_birthdate, year, note FROM leads WHERE school_id = $1 AND ${filters} (deleted = false or deleted isnull) and email not in ('<EMAIL>','<EMAIL>','n/<EMAIL>','<EMAIL>','n/a','<EMAIL>')) r
          ON l.email = r.email AND l.id != r.id and (r.note not ILIKE concat('%non-dup-|',l.id,'|%') or l.note not ilike concat('%non-dup-|',r.id,'|%') OR l.note IS null OR r.note IS null)) leads_res WHERE ((pf_sim > 0.60 or pl_sim > 0.60) AND (cf_sim > 0.70 AND cl_sim > 0.60)) AND (lyear = ryear or lyear isnull or ryear isnull or lyear = '' or ryear = ''))
          by_email GROUP BY LEAST(by_email.fid, by_email.id), GREATEST(by_email.fid, by_email.id);`
}

/**
 * Generates a string to get or insert a substage (lead_status) for a school
 * @returns {string}
 */
export function getInsertLeadSubstage(): string {
  return `WITH getset AS ( SELECT id FROM lead_statuses WHERE school_id = $1 AND name ilike $2),
    inserted AS (
      INSERT INTO lead_statuses (school_id, created_at, updated_at, name, deleted)
      SELECT $1, now(), now(), $2, false WHERE NOT EXISTS (SELECT 1 FROM getset) RETURNING id
    )
    SELECT id FROM getset UNION ALL SELECT id FROM inserted;`
}

/**
 * Generates a string to get or insert a substage (lead_status) for a school
 * @returns {string}
 */
export function nonDupLead(): string {
  return `UPDATE leads
          SET note = CASE
	          WHEN id = $1 and school_id = $3 THEN concat(note, ' non-dup-|',$2,'| ')
	          WHEN id = $2 and school_id = $3 THEN concat(note, ' non-dup-|',$1,'| ')
	          END
          WHERE id IN($1, $2) returning *`
}

/**
 * @returns {string}
 */
export function getSchoolsNonASMBlocked() {
  return `SELECT json_agg(s.school_id) AS school_ids FROM ( SELECT s.school_id FROM unnest($1::int[]) AS s(school_id) LEFT JOIN school_seat_availables sa  ON s.school_id = sa.school_id  AND sa.grade = $2 WHERE sa.school_id IS NULL OR (sa.grade = $2 AND sa.block_asm = false) ) AS s`
}

/**
 * @returns {string}
 */
export function getLeadsWithExtraSMData() {
  return `select json_object_agg(latest.lead_id, to_jsonb(latest)) as school_mint from ( select li.* from lead_integrations li join ( select lead_id, max(created_at) as max_created_at from lead_integrations
  where intg_data is not null group by lead_id ) recent on li.lead_id = recent.lead_id and li.created_at = recent.max_created_at join leads l on li.lead_id = l.id where l.school_id = $1 and
  (l.deleted != true or l.deleted is null) and li.intg_data is not null and li.sync_intg_with = 'schoolmint' ) latest`
}
