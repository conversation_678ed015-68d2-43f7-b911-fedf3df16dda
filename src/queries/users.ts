import { S3_BUCKET } from '@constants'

/**
 * Returns the query to get the searches by user_id
 * @returns {string}
 */
export function getSearches (): string {
  return `SELECT id, name, user_id, notification_options, search, created_at, updated_at
  FROM searches WHERE user_id = $1`
}

/**
 * Returns the query to get the tour list by user_id
 * @returns {string}
 */
export function getTourList (): string {
  return `SELECT id, school_id, full_name, user_id, email, phone, tour_date, tour_time, created_at, updated_at,
  tour_status, studen_name, grade, note, json_build_object('name', name, 'address', address, 'city', city,
  'state', state, 'plan_id', plan_id, 'subscription_months', subscription_months,
  'subscription_end', subscription_end, 'claimed', claimed) as school
  FROM (
  SELECT sst.id, sst.school_id, sst.full_name, sst.user_id, sst.email, sst.phone, sst.tour_date, sst.tour_time, sst.created_at, sst.updated_at,
    sst.tour_status, sst.studen_name, sst.grade, sst.note, s.name, s.address, s.city, s.state, s.plan_id, s.subscription_months,
    s.subscription_end, CASE WHEN us.school_id IS NULL then false ELSE true end as claimed, filename from
    (SELECT * FROM school_schedule_tour WHERE user_id = $1) sst
    LEFT JOIN schools s ON sst.school_id = s.id
    LEFT JOIN (SELECT DISTINCT(school_id) FROM users_schools WHERE status != 'Deleted') us ON us.school_id = sst.school_id
    LEFT JOIN (SELECT filename, school_id FROM school_images WHERE profile_image = true) si ON si.school_id = sst.school_id) r`
}

/**
 * Returns the query to get the user applications by user_id
 * @returns {string}
 */
export function getUserApplications (): string {
  return `SELECT id, user_id, parent_relationship, parent_first_name, parent_last_name, email, phone,
  preferred_contact, created_at,updated_at, student_id, json_build_object('name', name, 'age', age,
  'grade', grade, 'photo', photo, 'birthdate', birthdate, 'parent_relationship', student_parent_relationship,
  'enrollment_year', enrollment_year) as student
  FROM (
  SELECT ua.id, ua.user_id, ua.parent_relationship, ua.parent_first_name, ua.parent_last_name, ua.email, ua.phone, ua.preferred_contact, ua.created_at, ua.updated_at, ua.student_id, us.name, us.age, us.grade, us.photo, us.birthdate, us.parent_relationship as student_parent_relationship, us.enrollment_year from user_applications ua
    LEFT JOIN user_students us ON ua.student_id::int = us.id
  WHERE ua.user_id = $1
  ) r`
}

/**
 * Returns the query to get an application by application id
 * @returns {string}
 */
export function getUserApplication (): string {
  return `SELECT id, user_id, parent_relationship, parent_first_name, parent_last_name, email, phone, preferred_contact, student_id, created_at, updated_at
    FROM user_applications WHERE id=$1`
}

/**
 * Returns the query to get a user profile by user_id
 * @returns {string}
 */
export function getUserProfile (): string {
  return `SELECT id, user_id, email, partner_id, first_name, last_name, phone, title, ext, referred_by, preferred_contact,
  language, address, zipcode, referred_by_other, created_at, updated_at, timezone, timezone_offset, status, cloudtalk_id
    FROM user_profiles WHERE user_id = $1`
}

/**
 * Returns the query to get a user_school by user_id
 * @returns {string}
 */
export function getUserSchoolByUserId (): string {
  return `SELECT id, school_id, user_id, notify_new_lead, user_school_deparment_id, status, "isOwner", created_at, updated_at
    FROM users_schools WHERE user_id = $1`
}

/**
 * Returns the query to get a partner by partner_id
 * @returns {string}
 */
export function getPartnerUsers (): string {
  return `SELECT id, partner_id, user_id, created_at, updated_at
    FROM partner_users WHERE partner_id = $1`
}

/**
 * Returns the query to get the completed schola match
 * @returns {string}
 */
export function getScholaMatchCompleted (): string {
  return `SELECT COUNT(id) AS leads,
  SUM(CASE WHEN application_id IS NULL THEN 0 ELSE 1 END) AS applications
  FROM leads
  WHERE user_id = $1`
}

/**
 * Returns the query to get the school user notifications
 * @returns {string}
 */
export function getSchoolUserNotifications (): string {
  return `SELECT notify_new_lead, notify_new_app FROM users_schools
  WHERE school_id = $1 AND user_id = $2`
}

/**
 * Returns the query to get leads by user id
 * @returns {string}
 */
export function getLeadsByUserId (): string {
  return `SELECT l.school_id, s.name as school, s.type as school_type, s.state, s.city, s.address, s.phone, l.id as lead_id,
  (
  	SELECT CASE WHEN filename IS NOT NULL THEN concat('//${S3_BUCKET}.s3.amazonaws.com/', filename) ELSE NULL END AS filename
  	FROM school_images where school_id=l.school_id and profile_image=true
  ) as profile_image,
  CASE WHEN (SELECT COUNT(*) FROM users_schools WHERE school_id = l.school_id and status <> 'Deleted') > 0 THEN true ELSE false END claimed,
  s.plan_id, s.subscription_end, s.subscription_months, l.child_first_name, l.child_last_name, l.grade, l.year, l.status,
  (SELECT need FROM lead_transportations WHERE lead_id=l.id) as transportation_need, enrollment_confirmed, attendance_confirmed
  FROM leads l
  LEFT JOIN schools s ON l.school_id=s.id
  WHERE COALESCE(l.deleted, false) = false AND user_id = $1;`
}

/**
 * Returns the query to get the referral code that matches the filter
 * @returns {string}
 */
export function getReferralCode (): string {
  return `SELECT id, user_profile_id, user_id, referral_code, created_at, updated_at
  FROM user_referral_code WHERE referral_code = $1`
}

/**
 * Returns the query to get the search that matches the id
 * @returns {string}
 */
export function getSearch (): string {
  return `SELECT id, name, user_id, notification_options, search, created_at, updated_at
  FROM searches WHERE id = $1`
}

/**
 * Returns the query to delete the search that matches the id
 * @returns {string}
 */
export function deleteSearch (): string {
  return `DELETE FROM searches WHERE id = $1 AND user_id = $2`
}

/**
 * Returns the query to delete the search that matchfunctiones the id
 * @returns {string}
 */
export function deleteUserApplication (): string {
  return `DELETE FROM user_applications WHERE id = $1 AND user_id = $2`
}

/**
 * Returns the query to delete the search that matches the id
 * @returns {string}
 */
export function getUsersAssignmentsByRoleId (): string {
  return `SELECT r.user_id, r.role_id, p.first_name, p.last_name, p.email, p.status as user_status, p.cloudtalk_id, a.id AS assignment_id, a.school_id,
  s.name AS school_name, a.status, a.begin_at, a.end_at
  FROM user_roles r
  LEFT JOIN user_profiles p ON r.user_id = p.user_id
  LEFT JOIN school_assignments a ON r.user_id = a.user_id and a.status != 'deleted'
  LEFT JOIN schools s ON a.school_id = s.id
  WHERE r.role_id = $1 AND p.deleted = false
  ORDER BY CONCAT(p.first_name, ' ', p.last_name), a.begin_at`
}

/**
 * Returns the query to get the users by role id
 * Query Params:
 * $1 role_id {number},
 * @returns
 */
export function getUsersByRoleId(): string {
  return `Select r.user_id,p.first_name, p.last_name, p.email
  From user_roles r
  Left Join user_profiles p on r.user_id = p.user_id
  Where r.role_id = $1
  Order By concat(p.first_name, ' ', p.last_name)`
}

/**
 * Returns the query to get the last logins to admin by user id
 * Query Params:
 * $1 user_id {number},
 * @returns {string}
 */
export function getUserLogins(): string {
  return `
    SELECT id, user_id, email, created_at as login_time
    FROM log_auths
    WHERE user_id = $1 AND appsite = 'schools_admin'
    ORDER BY created_at DESC
    LIMIT 5
  `
}

/**
 * Returns the query to get the spot users by user_id
 * @returns {string}
 */
export function getSpotUsers(): string {
  return `SELECT * FROM user_profiles WHERE user_id = any($1) and deleted = false`
}

/**
 * Returns the query to get the spot user by user_id
 * @returns {string}
 */
export function getSpotUser(): string {
  return `SELECT user_id, cloudtalk_id FROM user_profiles WHERE user_id = $1 and deleted = false limit 1`
}

/**
 * Returns the query to delete the spot user
 * @returns {string}
 */
export function deleteSpotUser(): string {
  return `UPDATE user_profiles SET deleted = true, cloudtalk_id = null WHERE user_id = $1 RETURNING *`
}

/**
 * Returns the query to delete the spot user
 * @returns {string}
 */
export function getDeletedSpotUser(): string {
  return `SELECT * FROM user_profiles WHERE user_id = $1 and deleted = true`
}

/**
 * Returns the query to get the deleted spot user
 * @returns {string}
 */
export function udpdate0AuthUser(): string {
  return `UPDATE user_profiles SET deleted = false, user_id = $1 WHERE user_id = $2 RETURNING *`
}

/**
 * Returns the query to get the spot users
 * @returns {string}
 */
export function udpdateCloudtalkUser(): string {
  return `UPDATE user_profiles SET deleted = false, cloudtalk_id = $1 WHERE user_id = $2 RETURNING *`
}

/**
 * Returns the query to get the role id
 * @returns {string}
 */
export function getRoleId(): string {
  return `select id from roles where name ilike $1 limit 1`
}

/**
 * Returns the query to get the spot members from the user_roles table
 * @returns {string}
 */
export function getSpotMembers(): string {
  return `select user_id from user_roles join roles on user_roles.role_id = roles.id where roles.name ilike 'SPOT Team'`
}

/**
 * Returns the query to add the cloudtalk id to the user_profiles table
 * @returns {string}
 */
export function addCloudtalkId(): string {
  return `UPDATE user_profiles SET cloudtalk_id = $2 WHERE user_id = $1 RETURNING *`
}
