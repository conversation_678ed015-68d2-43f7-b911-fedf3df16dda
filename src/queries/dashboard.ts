/**
 * Returns the query to search to search for the lead history
 */
export function getLeadHistory () {
  return `SELECT
  l.id as lead_id,
  lh.old_value old_status,
  coalesce(lh.new_value, l.status) new_status,
  l.status current_status,
  l.year lead_year,
  Case
  When cp.id is not null then true
  Else false
  End has_completed_application,
    l.application_received,
    l.application_valid,
    l.created_at lead_created_at,
    coalesce(lh.created_at, l.created_at) status_updated_at
  FROM
    leads l
    left join leads_history lh on lh.lead_id = l.id
        AND lh.field = 'status'
        AND lh.created_at >= $1
        AND lh.created_at < $2
    left join completed_applications cp on l.application_id = cp.id
  WHERE
    l.school_id = $3
    AND COALESCE(l.deleted, false) = false
    AND coalesce(lh.created_at,l.created_at) >= $4
    AND coalesce(lh.created_at,l.created_at) < $5
  ORDER BY
    lh.created_at asc`
}

/**
 * Returns the query to get the leads, applications and accepted count grouped by month
 */

export function getLeadsStatusByMonth() {
  return `SELECT 
  CASE
    WHEN $1 = 'month' THEN to_char(leads_history.created_at, 'YYYY-MM')
    WHEN $1 = 'days' THEN to_char(leads_history.created_at, 'YYYY-MM-DD')
  END AS month, 
  COUNT(*) FILTER (WHERE new_value = 'new') AS new,
  COUNT(*) FILTER (WHERE new_value = 'application-received') AS applications,
  COUNT(*) FILTER (WHERE new_value = 'accepted') AS enrollments
  FROM leads_history
  INNER JOIN leads ON leads_history.lead_id = leads.id
  WHERE leads_history.created_at >= $3
  AND leads.school_id = $2
  GROUP By month
  ORDER By month`
}

/**
 * Returns the query to get the lead count grouped by status
 */
export function getLeadCounts () {
  return `SELECT status, count(*) count FROM leads
  WHERE
    school_id = $1
    AND COALESCE(deleted, false) = false
    AND created_at >= $2
    AND created_at < $3
  GROUP BY status`
}

export function getNewLeadsRange() {
  // return `SELECT
  //   COUNT(CASE WHEN lh.created_at >= $4 AND lh.created_at <= $3 AND lh.new_value = 'new' THEN 1 END) as previous_new,
  //   COUNT(CASE WHEN lh.created_at >= $3 AND lh.created_at <= $2 AND lh.new_value = 'new' THEN 1 END) as current_new,
  //   COUNT(CASE WHEN lh.created_at >= $4 AND lh.created_at <= $3 AND lh.new_value = 'application-received' THEN 1 END) as previous_applications,
  //   COUNT(CASE WHEN lh.created_at >= $3 AND lh.created_at <= $2 AND lh.new_value = 'application-received' THEN 1 END) as current_applications,
  //   COUNT(CASE WHEN lh.created_at >= $4 AND lh.created_at <= $3 AND lh.new_value = 'accepted' THEN 1 END) as previous_enrollments,
  //   COUNT(CASE WHEN lh.created_at >= $3 AND lh.created_at <= $2 AND lh.new_value = 'accepted' THEN 1 END) as current_enrollments
  // FROM leads_history lh
  // INNER JOIN leads ON lh.lead_id = leads.id
  // WHERE leads.school_id = $1
  // GROUP BY leads.school_id`
  return `
  SELECT
  (SELECT count(*) from leads l
  left join schools s on l.school_id=s.id
  where  (l.created_at AT TIME ZONE COALESCE(s.timezone, 'UTC'::text)) >= $2 and coalesce(l.deleted, false)=false and school_id = $1) current_new,
  (SELECT count(*) from leads l
  left join completed_applications ca on l.application_id=ca.id
  left join schools s on l.school_id=s.id
  where  (coalesce(ca.created_at,l.application_received_at)  AT TIME ZONE COALESCE(s.timezone, 'UTC'::text)) >= $2 and coalesce(l.deleted, false)=false and l.school_id = $1) current_applications,
  (SELECT count(*) from leads l
  left join completed_applications ca on l.application_id=ca.id
  left join schools s on l.school_id=s.id
  where  (l.enrollment_confirmed_at  AT TIME ZONE COALESCE(s.timezone, 'UTC'::text)) >= $2 and coalesce(l.deleted, false)=false and l.school_id = $1) current_enrollments`
}

export function getMessagesBySPOT () {
  return `
    SELECT 
      'emails' AS source, 
      COUNT(*) as total_count
    FROM messages msg 
    WHERE msg.communication_type = 'Email' AND msg.created_at >= $2 AND msg.school_id = $1
    UNION ALL
    SELECT 
      'sms' AS source, 
      COUNT(*) as total_count
    FROM messages msg 
    WHERE msg.communication_type = 'Sms' AND msg.created_at >= $2 AND msg.school_id = $1
    UNION ALL
    SELECT 
      'emails' AS source, 
      COUNT(*) as total_count
    FROM email_audits emails WHERE emails.created_at >= $2 AND emails.school_id = $1
    UNION ALL
    SELECT 
      'sms' AS source, 
      COUNT(*) as total_count
    FROM sms_audits sms WHERE sms.created_at >= $2 AND sms.school_id = $1
  `
}
