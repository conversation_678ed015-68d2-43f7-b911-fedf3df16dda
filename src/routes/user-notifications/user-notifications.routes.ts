import express from 'express'
import moment from 'moment'
// Import controller
import { userNotifications as controller } from '@controllers'
// Import error files and handlers
import { http_status_codes } from '../../error-handling/http-status-codes'
// Import middleware
import { validateManager } from '../../middleware/validate-token'
const router = express.Router()

router
  .route('/:user_id/:id')
  .patch(validateManager, async (req, res) => {
    try {
      const { id, user_id } = req.params
      const result = await controller.updateUserNotifications(Number(id), user_id, {...req.body,
        user_id})
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode,
      })
    }
  })

router
  .route('/:user_id/:id/read')
  .patch(validateManager, async (req, res) => {
    try {
      const { id, user_id } = req.params
      const result = await controller.updateUserNotifications(Number(id), user_id, {read_at: moment().utc()})
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode,
      })
    }
  })

router
  .route('/:user_id/:id/delete')
  .patch(validateManager, async (req, res) => {
    try {
      const { id, user_id } = req.params
      const result = await controller.updateUserNotifications(Number(id), user_id, { deleted_at: moment().utc() })
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode,
      })
    }
  })

router
  .route('/:school_id/:user_id')
  .post(validateManager, async (req, res) => {
    try {
      const { user_id, school_id } = req.params
      const notification = {
        ...req.body,
        user_id,
        school_id: (school_id !== undefined && school_id !== 'undefined') ? Number(school_id) : null
      }
      const result = await controller.addUserNotification(notification)
      res.status(http_status_codes.CREATED).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode,
      })
    }
  })

router
  .route('/:user_id')
  .get(validateManager, async (req, res) => {
    try {
      const { user_id, school_id } = req.params
      const _school_id = school_id !== undefined ? school_id : undefined
      const unreadOnly = req.query.unreadOnly === 'true'
      const result = await controller.getSchoolUserNotifications(user_id, _school_id, unreadOnly)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode,
      })
    }
  })
  .patch(validateManager, async (req, res) => {
    try {
      const { user_id } = req.params
      const { notificationIds, key } = req.body
      const result = await controller.updateNotificationsBatch(notificationIds, user_id, key)
      res.status(http_status_codes.OK).json({ updated: result })
    } catch (error) {
      console.error('updateNotificationsBatch:', error)
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode,
      })
    }
  })

export = router;
