import express from 'express'

// Import controller
import * as controller from '../../controllers/lead-assignments'
import { addUserNotification } from '../../controllers/user-notifications'
import { getLeadById} from '../../controllers/leads'

// Import error files and handlers
import { http_status_codes } from '../../error-handling/http-status-codes'

// Import middleware
import { validateSuperAdmin } from '../../middleware/validate-token'
import { ManagementLeadAssignmentSearchParams } from 'src/interfaces/lead-assignments'

const router = express.Router()

// GET lead assignments based on filters
router.route('/search')
  .post(validateSuperAdmin, async (req, res) => {
    try {
      const params = req.body
      const result = await controller.getLeadAssignments(params)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/generalAssignments')
  .post(validateSuperAdmin, async (req, res) => {
    const { assignedStatus, status, schoolId, page, pageSize } = req.body as ManagementLeadAssignmentSearchParams
    try {
      const result = await controller.getLeadsByManager({ assignedStatus, status, schoolId, page, pageSize })
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

// CREATE a lead assignment
router.route('/')
  .post(validateSuperAdmin, async (req, res) => {
    try {
      const body = req.body
      const result = await controller.addLeadAssignment(body)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

// Update substatus
router.route('/:id/sub_status')
  .patch(validateSuperAdmin, async (req, res) => {
    try {
      const { id } = req.params
      const params = { ...req.body, id: Number(id) }
      const assignment = await controller.getLeadAssignmentById(params.id)

      //change the old to reassigned
      const result = await controller.updateLeadAssignment({
        id: params.id,
        user_id: assignment.user_id,
        sub_status: params.sub_status,
        status: assignment.status
      })

      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

// Reassing a lead
router.route('/:id/reassign')
  .patch(validateSuperAdmin, async (req, res) => {
    try {
      const { id } = req.params
      const params = { ...req.body, id: Number(id) }
      const assignment = await controller.getLeadAssignmentById(params.id)
      const lead = await getLeadById(assignment.lead_id)

      //change the old to reassigned
      const result = await controller.updateLeadAssignment({
        id: params.id,
        user_id: assignment.user_id,
        status: 'reassigned',
      })
      try {
        const leadName =`${lead.parent_first_name} ${lead.parent_last_name}`.trim()
        await addUserNotification({
          user_id: assignment.user_id,
          type: 'lead_reassigned',
          priority: 'medium',
          message: `A lead${leadName ? ` (${leadName})` : ''} (Lead ID: ${lead.id}) has been reassigned to another user.`,
        })
      } catch (notificationError) {
        console.error('Error creating lead reassignment notification:', notificationError)
      }
      const assignment_type = 'school_assigned'

      await controller.addLeadAssignment({
        user_id: params.user_id,
        lead_id: assignment.lead_id,
        status: 'active',
        sub_status: '',
        assignment_type,
        assignment_method: 'manual'
      })
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

// assign process
router.route('/run-assign')
  .get(async (req, res) => {
    try {
      const result = await controller.assign()
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

// assign process
router.route('/run-reassign')
  .get(async (req, res) => {
    try {
      const result = await controller.reassign()
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

// GET Balance & Status of Team users
router.route('/getTeamBalanceStatus')
  .get(validateSuperAdmin, async (req, res) => {
    try {
      const result = await controller.getTeamBalanceStatus()
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

// GET Balance & Status of Team users
router.route('/getAnalytics')
  .post(validateSuperAdmin, async (req, res) => {
    try {
      const result = await controller.getAnalytics(req.body)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

// GET Balance & Status of Team users
router.route('/getUserAnalytics')
  .post(validateSuperAdmin, async (req, res) => {
    try {
      const result = await controller.getUserAnalytics(req.body)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

// GET Top3 schools
router.route('/getTop3Scholamatch')
  .post(validateSuperAdmin, async (req, res) => {
    try {
      const result = await controller.getTop3Scholamatch(req.body)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/getAssignmentCount/:user_id')
  .get(validateSuperAdmin, async (req, res) => {
    try {
      const result = await controller.getAssignmentCountByUser(req.params.user_id)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

export = router
