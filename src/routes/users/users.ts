import express from 'express'

import {users<PERSON><PERSON>roller as controller} from '@controllers'

import {validate as schema_validator} from '../../utils/'

import * as schemas from '../../schemas'

import { http_status_codes } from '../../error-handling/http-status-codes'

// Import middleware
import {validateManager, validate, optional, validateSuperAdmin} from '../../middleware/validate-token'

// Import s3 upload connector
import { upload } from '../../connectors/s3'

// Import interfaces
import {
  GetUsersByRoleIdRequest,
  RequestAuth,
} from '@interfaces'

import type { Response } from 'express'

// Import error handlers
import { badRequestError, notFoundError } from '@errors'

const router = express.Router()

router.route('/')
  .get(validateManager, async(req, res) => {
    try {
      const {user_id} = req.query
      if (typeof user_id !== 'string' ) throw badRequestError
      const result = await controller.getUserDetails(user_id)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })
  .post(validate, async(req, res) => {
    try {
      const {name, phone, email, spot_user = false} = req.body
      const result = await controller.createUser(name, phone, email, spot_user)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:user_id')
  .patch( validate, upload.fields([
    { name: 'avatar', maxCount: 1 },
    { name: 'original', maxCount: 1 }
  ]), async (req: RequestAuth, res: Response) => {
    try {
      const files = req.files as {[fieldname: string]: Express.Multer.File[]}
      const params = {
        user_id: req.params.user_id,
        ...req.body,
        token: req.headers['authorization'].split(' ')[1] ?? req.headers['authorization'],
        decoded_email: req.auth.credentials.email
      }
      delete params.decoded_email
      if(files.avatar && files.original) {
        params.avatar = files.avatar[0].buffer
        params.original = files.original[0].buffer
      }
      const result = await controller.updateUser(params)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:user_id/password')
  .post( validate, async (req, res) => {
    try {
      const {user_id} = req.params
      const {password} = req.body
      const result = await controller.requestPasswordChange(user_id, password)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })
  .patch(validate, async(req: RequestAuth, res) => {
    try {
      const {user_id} = req.params
      if (typeof user_id !== 'string' ) throw badRequestError
      const token = req.headers['authorization'].split(' ')[1] ?? req.headers['authorization']
      const result = await controller.updatePassword(user_id, req.body.password, token)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:user_id/partners')
  .post( validate, async (req, res) => {
    try {
      const {user_id, name, phone, email} = req.body
      const result = await controller.addPartnerUser(user_id, name, phone, email)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/avatars')
  .get(optional, async(req, res) => {
    try {
      await schema_validator(req.query, schemas.get_users_avatars)
      const user_ids = req.query.userIds as Array<string>
      const result = await controller.getUserAvatars(user_ids)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/validate-email')
  .post(async(req, res) => {
    try {
      const result = await controller.validateEmail(req.body.email)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/assignmentsbyrole')
  .post(validate, async(req, res) => {
    try {
      const result = await controller.getUsersAssignmentsByRoleId(req.body.role_id)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/reset-pwd')
  .post(validate, async(req, res) => {
    try {
      const result = await controller.resetPassword(req.body.email)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:email/id')
  .get(validate, async(req, res) => {
    try {
      const {email} = req.params
      if (typeof email !== 'string' ) throw badRequestError
      const result = await controller.getUserIdByEmail(email)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:user_id/searches')
  .get(validate, async(req, res) => {
    try {
      const {user_id} = req.params
      if (typeof user_id !== 'string' ) throw badRequestError
      const result = await controller.getSearches(user_id)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })
  .post(validate, async(req, res) => {
    try {
      const {user_id} = req.params
      if (typeof user_id !== 'string' ) throw badRequestError
      const {name, notifyFrecuency: notifyFrequency, search} = req.body
      const result = await controller.addSearch(user_id, name, notifyFrequency, search)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:user_id/tours')
  .get(optional, async(req, res) => {
    try {
      const {user_id} = req.params
      if (typeof user_id !== 'string' ) throw badRequestError
      const result = await controller.getTourList(user_id)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:user_id/applications')
  .get(validate, async(req, res) => {
    try {
      const {user_id} = req.params
      if (typeof user_id !== 'string' ) throw badRequestError
      const result = await controller.getUserApplications(user_id)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })
  .post(validate, upload.none(), async(req, res) => {
    try {
      const {user_id} = req.params
      if (typeof user_id !== 'string' ) throw badRequestError
      const result = await controller.addUserApplication({user_id, ...req.body})
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:user_id/applications/:application_id')
  .get(validate, async(req, res) => {
    try {
      const {user_id, application_id} = req.params
      const result = await controller.getUserApplication(user_id, Number(application_id))
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })
  .patch(validate, upload.none(), async(req, res) => {
    try {
      const {user_id, id} = req.params
      if (typeof user_id !== 'string' ) throw badRequestError
      const result = await controller.updateUserApplication(Number(id), {user_id, ...req.body})
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })
  .delete(validate, async(req, res) => {
    try {
      const {user_id, id} = req.params
      if (typeof user_id !== 'string' ) throw badRequestError
      const result = await controller.deleteUserApplication(Number(id), user_id)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:user_id/profile')
  .get(validate, async(req, res) => {
    try {
      const {user_id} = req.params
      if (typeof user_id !== 'string' ) throw badRequestError
      const result = await controller.getUserProfile(user_id)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:user_id/last-login')
  .get(validate, async(req, res) => {
    const userId = req.params.user_id as string
    try {
      const authLogs = await controller.getLastUserLogin({ userId })
      return res.status(http_status_codes.OK).json(authLogs)
    } catch (error) {
      console.log('Failed fetching auth logs', error.message)
    }
  })

router.route('/:user_id/scholamatch_completed')
  .get(optional, async(req, res) => {
    try {
      const {user_id} = req.params
      if (typeof user_id !== 'string' ) throw badRequestError
      const result = await controller.getScholaMatchCompleted(user_id)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:user_id/token')
  .get(validate, async(req, res) => {
    try {
      const {token} = req.query
      if (typeof token !== 'string' ) throw badRequestError
      const result = await controller.getNewToken(token)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:user_id/leads')
  .get(optional, async(req, res) => {
    try {
      const {user_id} = req.params
      if (typeof user_id !== 'string' ) throw badRequestError
      const result = await controller.getLeadsByUserId(user_id)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:user_id/info')
  .patch(validate, upload.none(), async(req: RequestAuth, res) => {
    try {
      const {user_id} = req.params
      if (typeof user_id !== 'string' ) throw badRequestError
      const {firstName: first_name, lastName:last_name,  ...rest} = req.body
      const params = {
        user_id,
        first_name,
        last_name,
        decoded_email: req.auth.credentials.email,
        token: req.headers['authorization'].split(' ')[1] ?? req.headers['authorization'],
        ...rest
      }
      const result = await controller.updateUserInfo(params)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:user_id/searches/:search_id')
  .patch(validate, async(req, res) => {
    try {
      const { user_id, search_id } = req.params
      if (typeof user_id !== 'string' ) throw badRequestError
      const { name, notifyFrequency, search } = req.body
      if (!name || !notifyFrequency || !search ) throw notFoundError
      const result = await controller.updateSearch(Number(search_id), user_id, name, notifyFrequency, search)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })
  .delete(validate, async(req, res) => {
    try {
      const { user_id, search_id } = req.params
      if (typeof user_id !== 'string' ) throw badRequestError
      const result = await controller.deleteSearch( Number(search_id), user_id )
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/usersbyrole')
  .post(validate, upload.none(), async(req, res) => {
    try {
      const body = req.body as unknown as GetUsersByRoleIdRequest
      const request =  {...body}
      if(request.role_id) request.role_id = Number(request.role_id)
      if(!request.role_id) throw badRequestError

      const result = await controller.getUsersByRoleId(request.role_id)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:user_id/status')
  .patch(validate, async (req, res) => {
    try {
      const { user_id } = req.params
      if (typeof user_id !== 'string') throw badRequestError
      const { status } = req.body
      if (typeof status !== 'string') throw badRequestError
      const result = await controller.updateUserStatus(user_id, status)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/spot-users')
  .get(validateSuperAdmin, async (req, res) => {
    try {
      const result = await controller.getSpotUsers()
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/spot-users/:user_id')
  .delete(validateSuperAdmin, async (req, res) => {
    try {
      const { user_id } = req.params
      if (typeof user_id !== 'string' ) throw badRequestError
      const resultAuth0 = await controller.removeSuperAdminRole(user_id)
      const result = await controller.deleteSpotUsers(user_id)
      res.status(http_status_codes.OK).send({ result, resultAuth0 })
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/spot-users/:user_id/reactivate')
  .post(validate, async (req, res) => {
    try {
      const { user_id } = req.params
      if (typeof user_id !== 'string' ) throw badRequestError
      const result = await controller.reactivateSpotUser(user_id)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:user_id/remove-superadmin')
  .patch(validateSuperAdmin, async (req, res) => {
    try {
      const { user_id } = req.params
      if (typeof user_id !== 'string' ) throw badRequestError
      const result = await controller.removeSuperAdminRole(user_id)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:user_id/add-cloudtalk-id/:cloudtalk_id')
  .patch(validateSuperAdmin, async (req, res) => {
    try {
      const { user_id, cloudtalk_id } = req.params
      if (typeof user_id !== 'string' ) throw badRequestError
      if (typeof cloudtalk_id !== 'string' ) throw badRequestError
      const result = await controller.addCloudtalkId(user_id, cloudtalk_id)
      res.status(http_status_codes.OK).send(result)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

export = router
