@import '../../../sass/color-palette';

.text {
  display: block;
  width: 100%;
}

.text-label {
  display: block;
  width: 100%;
  font-size: 0.875rem; // 14px
  font-weight: 600;
  margin-bottom: 0.5rem; // 8px
}

.text-input,
.text-area,
#claim-school--autocomplete {
  display: block;
  font-size: 1rem; // 16px
  line-height: 1.25rem; // 20px
  padding: 0.8125rem 1rem; // 13px 16px
  appearance: none;
}

.text-area {
  min-height: 12.5rem; // 200px
  resize: vertical;
}

// Disabled cursor on parent label and all children elements
.text--disabled,
.text--disabled * {
  cursor: not-allowed !important;
  opacity: 0.75;
}

.text--disabled .text-input,
.text--disabled .text-area {
  background: $navy-blue-25;
}

input[type='text'],
input[type='url'],
input[type='number'],
input[type='date'],
input[type='email'],
input[type='tel'],
input[type='password'],
textarea {
  color: $dark-blue-65;
  font-size: 0.875rem;
  width: 100%;
  min-height: 2.5rem !important; // 40px
  padding: 0 0.625rem !important; // 0 10px
  border-radius: 0.3rem;
  background-color: #fff;
  border: 1px solid #e2e8f0;

  &:focus {
    outline: none;
    border-radius: 0.1875rem; // 3px
  }
}
// textarea {
//   height: 100% !important;
// }

input[type='text'].transparent,
select.transparent,
textarea.transparent {
  background-color: transparent;
}

textarea {
  height: auto;
  min-height: 3.125rem; // 50px
  padding-top: 0.625rem; // 10px
  padding-bottom: 0.625rem; // 10px
  resize: vertical;
}
