import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { RelationshipManagerHoverCard } from './index';

// Mock window.open
const mockWindowOpen = jest.fn();
Object.defineProperty(window, 'open', {
  value: mockWindowOpen,
  writable: true,
});

describe('RelationshipManagerHoverCard', () => {
  const defaultProps = {
    rms: 2,
    leadId: '12370',
  };

  // Mock data that can be passed as props for testing
  const mockRelationshipManagers = [
    {
      scholaId: 'SCH-12370-1',
      schoolName: 'Test Elementary School',
      dateEntered: 'Jan 01, 2024 10:00',
      status: 'Active',
    },
    {
      scholaId: 'SCH-12370-2',
      schoolName: 'Test High School',
      dateEntered: 'Feb 15, 2024 14:30',
      status: 'Pending',
    },
  ];

  const propsWithMockData = {
    rms: 2,
    leadId: '12370',
    relationshipManagers: mockRelationshipManagers,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render the component with RMs count', () => {
      render(<RelationshipManagerHoverCard {...defaultProps} />);

      const trigger = screen.getByText('2');
      expect(trigger).toBeInTheDocument();
      expect(trigger).toHaveClass('trigger');
    });

    it('should render empty state when rms is 0', () => {
      render(<RelationshipManagerHoverCard rms={0} leadId="12370" />);

      const emptyState = screen.getByText('-');
      expect(emptyState).toBeInTheDocument();
      expect(emptyState).toHaveClass('emptyState');
    });

    it('should render with string leadId', () => {
      render(<RelationshipManagerHoverCard rms={1} leadId="test-lead" />);

      const trigger = screen.getByText('1');
      expect(trigger).toBeInTheDocument();
    });
  });

  describe('Hover Functionality', () => {
    it('should show popover content on hover', async () => {
      render(<RelationshipManagerHoverCard {...defaultProps} />);

      const trigger = screen.getByText('2');
      fireEvent.mouseEnter(trigger);

      await waitFor(() => {
        expect(screen.getByText('Current Relationship Managers')).toBeInTheDocument();
      });
    });

    it('should display popover structure and elements', async () => {
      render(<RelationshipManagerHoverCard {...defaultProps} />);

      const trigger = screen.getByText('2');
      fireEvent.mouseEnter(trigger);

      await waitFor(() => {
        // Test for header
        expect(screen.getByText('Current Relationship Managers')).toBeInTheDocument();

        // Test for presence of Schola IDs (pattern-based)
        const scholaIds = screen.getAllByText(/^SCH-\d+-\d+$/);
        expect(scholaIds).toHaveLength(2);

        // Test for external link icons (using anticon class)
        const externalIcons = document.querySelectorAll('.anticon-export');
        expect(externalIcons.length).toBeGreaterThan(0);
      });
    });

    it('should display school names and status information', async () => {
      render(<RelationshipManagerHoverCard {...defaultProps} />);

      const trigger = screen.getByText('2');
      fireEvent.mouseEnter(trigger);

      await waitFor(() => {
        // Test that the popover header is displayed
        expect(screen.getByText('Current Relationship Managers')).toBeInTheDocument();

        // Test that Schola IDs are present (this confirms content is rendered)
        const scholaIds = screen.getAllByText(/^SCH-\d+-\d+$/);
        expect(scholaIds.length).toBeGreaterThan(0);
      });
    });
  });

  describe('Click Functionality', () => {
    it('should open new window when Schola ID is clicked', async () => {
      render(<RelationshipManagerHoverCard {...defaultProps} />);

      const trigger = screen.getByText('2');
      fireEvent.mouseEnter(trigger);

      await waitFor(() => {
        // Find any Schola ID link (pattern-based)
        const scholaIdLinks = screen.getAllByText(/^SCH-\d+-\d+$/);
        expect(scholaIdLinks.length).toBeGreaterThan(0);

        const firstScholaId = scholaIdLinks[0];
        const scholaIdText = firstScholaId.textContent;

        fireEvent.click(firstScholaId);

        expect(mockWindowOpen).toHaveBeenCalledWith(`/rm/schola/${scholaIdText}`, '_blank');
      });
    });

    it('should prevent event propagation when Schola ID is clicked', async () => {
      const mockStopPropagation = jest.fn();
      render(<RelationshipManagerHoverCard {...defaultProps} />);

      const trigger = screen.getByText('2');
      fireEvent.mouseEnter(trigger);

      await waitFor(() => {
        const scholaIdLinks = screen.getAllByText(/^SCH-\d+-\d+$/);
        expect(scholaIdLinks.length).toBeGreaterThan(0);

        const firstScholaId = scholaIdLinks[0];

        // Create a mock event with stopPropagation
        const mockEvent = {
          stopPropagation: mockStopPropagation,
        };

        fireEvent.click(firstScholaId, mockEvent);
        expect(mockWindowOpen).toHaveBeenCalled();
      });
    });
  });

  describe('Dynamic Content', () => {
    it('should limit displayed RMs based on rms prop', async () => {
      render(<RelationshipManagerHoverCard rms={1} leadId="12370" />);

      const trigger = screen.getByText('1');
      fireEvent.mouseEnter(trigger);

      await waitFor(() => {
        // Should only show 1 RM entry
        const scholaIds = screen.getAllByText(/^SCH-\d+-\d+$/);
        expect(scholaIds).toHaveLength(1);
      });
    });

    it('should generate correct Schola IDs based on leadId', async () => {
      const customLeadId = '99999';
      render(<RelationshipManagerHoverCard rms={2} leadId={customLeadId} />);

      const trigger = screen.getByText('2');
      fireEvent.mouseEnter(trigger);

      await waitFor(() => {
        // Check that Schola IDs contain the correct leadId
        const scholaIds = screen.getAllByText(/^SCH-99999-\d+$/);
        expect(scholaIds).toHaveLength(2);

        // Verify the pattern matches expected format
        scholaIds.forEach((element, index) => {
          expect(element.textContent).toMatch(new RegExp(`^SCH-${customLeadId}-${index + 1}$`));
        });
      });
    });

    it('should display correct number of RM entries', async () => {
      render(<RelationshipManagerHoverCard rms={2} leadId="12370" />);

      const trigger = screen.getByText('2');
      fireEvent.mouseEnter(trigger);

      await waitFor(() => {
        // Verify by counting Schola IDs (each RM has one)
        const scholaIds = screen.getAllByText(/^SCH-\d+-\d+$/);
        expect(scholaIds).toHaveLength(2);
      });
    });
  });

  describe('Styling', () => {
    it('should apply correct CSS classes', () => {
      render(<RelationshipManagerHoverCard {...defaultProps} />);

      const trigger = screen.getByText('2');
      expect(trigger).toHaveClass('trigger');
    });

    it('should apply correct status styling classes', async () => {
      render(<RelationshipManagerHoverCard {...defaultProps} />);

      const trigger = screen.getByText('2');
      fireEvent.mouseEnter(trigger);

      await waitFor(() => {
        // Verify that the popover is displayed with proper structure
        expect(screen.getByText('Current Relationship Managers')).toBeInTheDocument();

        // Check that Schola IDs are present (confirms content structure)
        const scholaIds = screen.getAllByText(/^SCH-\d+-\d+$/);
        expect(scholaIds.length).toBeGreaterThan(0);
      });
    });

    it('should apply correct structural CSS classes', async () => {
      render(<RelationshipManagerHoverCard {...defaultProps} />);

      const trigger = screen.getByText('2');
      fireEvent.mouseEnter(trigger);

      await waitFor(() => {
        // Check that the popover structure is rendered correctly
        const header = screen.getByText('Current Relationship Managers');
        expect(header).toBeInTheDocument();

        // Check that the popover content container exists
        const popoverContent = header.closest('div');
        expect(popoverContent).toBeInTheDocument();

        // Check that Schola IDs are clickable (have proper structure)
        const scholaIds = screen.getAllByText(/^SCH-\d+-\d+$/);
        expect(scholaIds.length).toBeGreaterThan(0);
      });
    });
  });

  describe('PropTypes Validation', () => {
    it('should accept number leadId', () => {
      expect(() => {
        render(<RelationshipManagerHoverCard rms={2} leadId={12370} />);
      }).not.toThrow();
    });

    it('should accept string leadId', () => {
      expect(() => {
        render(<RelationshipManagerHoverCard rms={2} leadId="12370" />);
      }).not.toThrow();
    });
  });

  describe('Edge Cases', () => {
    it('should handle large rms numbers', async () => {
      render(<RelationshipManagerHoverCard rms={5} leadId="12370" />);

      const trigger = screen.getByText('5');
      fireEvent.mouseEnter(trigger);

      await waitFor(() => {
        // Should still only show limited items (based on mock data availability)
        const scholaIds = screen.getAllByText(/^SCH-\d+-\d+$/);
        expect(scholaIds.length).toBeLessThanOrEqual(5);
        expect(scholaIds.length).toBeGreaterThan(0);
      });
    });

    it('should handle empty leadId', async () => {
      render(<RelationshipManagerHoverCard rms={1} leadId="" />);

      const trigger = screen.getByText('1');
      fireEvent.mouseEnter(trigger);

      await waitFor(() => {
        // Should generate Schola ID with empty leadId
        const scholaIds = screen.getAllByText(/^SCH--\d+$/);
        expect(scholaIds).toHaveLength(1);
      });
    });

    it('should handle zero rms gracefully', () => {
      render(<RelationshipManagerHoverCard rms={0} leadId="12370" />);

      const emptyState = screen.getByText('-');
      expect(emptyState).toBeInTheDocument();
      expect(emptyState).toHaveClass('emptyState');
    });
  });
});
