import React from 'react';
import { Flex, Popover } from 'antd';
import { ExportOutlined } from '@ant-design/icons';
import PropTypes from 'prop-types';
import styles from './relationship-manager-hover-card.module.scss';

export const RelationshipManagerHoverCard = ({ rms, leadId }) => {
  const mockRelationshipManagers =
    rms > 0
      ? [
          {
            scholaId: `SCH-${leadId}-1`,
            schoolName: 'Riverside Elementary',
            dateEntered: 'Jul 15, 2023 00:00',
            status: 'Application Sent',
          },
          {
            scholaId: `SCH-${leadId}-2`,
            schoolName: 'Valley Montessori',
            dateEntered: 'Sep 22, 2023 00:00',
            status: 'Waitlisted',
          },
        ].slice(0, rms)
      : [];

  const handleScholaIdClick = (scholaId, event) => {
    event.stopPropagation();
    window.open(`/rm/schola/${scholaId}`, '_blank'); // temp route
  };

  if (rms === 0) {
    return <span className={styles.emptyState}>-</span>;
  }

  const content = (
    <div className={styles.hoverCard}>
      <div className={styles.header}>Current Relationship Managers</div>
      <div className={styles.content}>
        {mockRelationshipManagers.map((rm, index) => (
          <div key={index} className={styles.rmItem}>
            <div className={styles.rmHeader}>
              <div>
                <div className={styles.schoolName}>{rm.schoolName}</div>
                <div className={styles.dateEntered}>{rm.dateEntered}</div>
              </div>
              <div>
                <Flex align="center" gap="small" justify="flex-end">
                  <div className={styles.scholaId} onClick={(e) => handleScholaIdClick(rm.scholaId, e)}>
                    {rm.scholaId}
                  </div>
                  <ExportOutlined className={styles.externalIcon} />
                </Flex>
                <div
                  className={`${styles.status} ${
                    rm.status === 'Application Sent' ? styles.applicationSent : styles.waitlisted
                  }`}>
                  {rm.status}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <Popover content={content} trigger="hover" placement="bottomLeft" overlayStyle={{ zIndex: 1050 }}>
      <span className={styles.trigger}>{rms}</span>
    </Popover>
  );
};

RelationshipManagerHoverCard.propTypes = {
  rms: PropTypes.number,
  leadId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
};
