import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { RadioGroup } from './radio-card';

// Mock Ant Design components
jest.mock('antd', () => ({
  Radio: {
    Group: ({ children, value, onChange, className, options, block, buttonStyle }) => (
      <div
        className={className}
        data-testid="radio-group"
        data-value={value}
        data-block={block}
        data-button-style={buttonStyle}>
        {options.map((option) => (
          <label key={option.value} data-testid={`radio-option-${option.value}`}>
            <input
              type="radio"
              value={option.value}
              checked={value === option.value}
              onChange={(e) => onChange && onChange(e)}
              data-testid={`radio-input-${option.value}`}
            />
            {option.label}
          </label>
        ))}
      </div>
    ),
  },
}));

// <PERSON><PERSON> styles
jest.mock('./radio-card.module.scss', () => ({
  radioGroup: 'radioGroup',
  stageItem: 'stageItem',
  stageHeader: 'stageHeader',
  stageTitle: 'stageTitle',
  stageDescription: 'stageDescription',
  radioItem: 'radioItem',
}));

describe('RadioGroup Component', () => {
  const mockOptions = [
    {
      value: 'option1',
      label: 'Option 1',
      description: 'Description for option 1',
    },
    {
      value: 'option2',
      label: 'Option 2',
      description: 'Description for option 2',
    },
    {
      value: 'option3',
      label: 'Option 3',
      description: 'Description for option 3',
    },
  ];

  const defaultProps = {
    options: mockOptions,
    value: 'option1',
    setValue: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    test('renders radio group with correct props', () => {
      render(<RadioGroup {...defaultProps} />);

      const radioGroup = screen.getByTestId('radio-group');
      expect(radioGroup).toBeInTheDocument();
      expect(radioGroup).toHaveAttribute('data-block', 'true');
      expect(radioGroup).toHaveAttribute('data-button-style', 'solid');
      expect(radioGroup).toHaveAttribute('data-value', 'option1');
    });

    test('renders all radio options', () => {
      render(<RadioGroup {...defaultProps} />);

      mockOptions.forEach((option) => {
        expect(screen.getByTestId(`radio-option-${option.value}`)).toBeInTheDocument();
        expect(screen.getByTestId(`radio-input-${option.value}`)).toBeInTheDocument();
      });
    });

    test('renders radio cards with labels and descriptions', () => {
      render(<RadioGroup {...defaultProps} />);

      expect(screen.getByText('Option 1')).toBeInTheDocument();
      expect(screen.getByText('Description for option 1')).toBeInTheDocument();
      expect(screen.getByText('Option 2')).toBeInTheDocument();
      expect(screen.getByText('Description for option 2')).toBeInTheDocument();
      expect(screen.getByText('Option 3')).toBeInTheDocument();
      expect(screen.getByText('Description for option 3')).toBeInTheDocument();
    });

    test('shows correct selected value', () => {
      render(<RadioGroup {...defaultProps} />);

      const selectedInput = screen.getByTestId('radio-input-option1');
      const unselectedInput = screen.getByTestId('radio-input-option2');

      expect(selectedInput).toBeChecked();
      expect(unselectedInput).not.toBeChecked();
    });

    test('renders with different selected value', () => {
      render(<RadioGroup {...defaultProps} value="option2" />);

      const selectedInput = screen.getByTestId('radio-input-option2');
      const unselectedInput = screen.getByTestId('radio-input-option1');

      expect(selectedInput).toBeChecked();
      expect(unselectedInput).not.toBeChecked();
    });
  });

  describe('User Interactions', () => {
    test('passes setValue function to Radio.Group onChange', () => {
      const mockSetValue = jest.fn();
      render(<RadioGroup {...defaultProps} setValue={mockSetValue} />);

      // Verify that the Radio.Group receives the correct onChange handler
      const radioGroup = screen.getByTestId('radio-group');
      expect(radioGroup).toBeInTheDocument();
    });

    test('renders radio inputs with correct values', () => {
      const mockSetValue = jest.fn();
      render(<RadioGroup {...defaultProps} setValue={mockSetValue} />);

      const radioInput1 = screen.getByTestId('radio-input-option1');
      const radioInput2 = screen.getByTestId('radio-input-option2');
      const radioInput3 = screen.getByTestId('radio-input-option3');

      expect(radioInput1.value).toBe('option1');
      expect(radioInput2.value).toBe('option2');
      expect(radioInput3.value).toBe('option3');
    });

    test('shows correct checked state based on value prop', () => {
      render(<RadioGroup {...defaultProps} value="option2" />);

      const radioInput1 = screen.getByTestId('radio-input-option1');
      const radioInput2 = screen.getByTestId('radio-input-option2');
      const radioInput3 = screen.getByTestId('radio-input-option3');

      expect(radioInput1).not.toBeChecked();
      expect(radioInput2).toBeChecked();
      expect(radioInput3).not.toBeChecked();
    });

    test('updates checked state when value prop changes', () => {
      const { rerender } = render(<RadioGroup {...defaultProps} value="option1" />);

      let radioInput1 = screen.getByTestId('radio-input-option1');
      let radioInput2 = screen.getByTestId('radio-input-option2');

      expect(radioInput1).toBeChecked();
      expect(radioInput2).not.toBeChecked();

      // Re-render with different value
      rerender(<RadioGroup {...defaultProps} value="option2" />);

      radioInput1 = screen.getByTestId('radio-input-option1');
      radioInput2 = screen.getByTestId('radio-input-option2');

      expect(radioInput1).not.toBeChecked();
      expect(radioInput2).toBeChecked();
    });
  });

  describe('Options Handling', () => {
    test('handles options without descriptions', () => {
      const optionsWithoutDescription = [
        {
          value: 'option1',
          label: 'Option 1',
        },
        {
          value: 'option2',
          label: 'Option 2',
          description: 'Only this one has description',
        },
      ];

      render(<RadioGroup {...defaultProps} options={optionsWithoutDescription} />);

      expect(screen.getByText('Option 1')).toBeInTheDocument();
      expect(screen.getByText('Option 2')).toBeInTheDocument();
      expect(screen.getByText('Only this one has description')).toBeInTheDocument();
    });

    test('handles numeric values', () => {
      const numericOptions = [
        {
          value: 1,
          label: 'First Option',
          description: 'Numeric value 1',
        },
        {
          value: 2,
          label: 'Second Option',
          description: 'Numeric value 2',
        },
      ];

      render(<RadioGroup {...defaultProps} options={numericOptions} value={1} />);

      expect(screen.getByTestId('radio-input-1')).toBeChecked();
      expect(screen.getByTestId('radio-input-2')).not.toBeChecked();
    });

    test('handles empty options array', () => {
      render(<RadioGroup {...defaultProps} options={[]} />);

      const radioGroup = screen.getByTestId('radio-group');
      expect(radioGroup).toBeInTheDocument();
      expect(radioGroup.children).toHaveLength(0);
    });
  });

  describe('RadioCard Structure', () => {
    test('renders radio card with correct CSS classes', () => {
      render(<RadioGroup {...defaultProps} />);

      // Check that the radio cards are rendered with proper structure
      const option1Text = screen.getByText('Option 1');
      const option1Description = screen.getByText('Description for option 1');

      expect(option1Text).toBeInTheDocument();
      expect(option1Description).toBeInTheDocument();
    });

    test('renders radio card with value as key', () => {
      render(<RadioGroup {...defaultProps} />);

      // Verify that each option is rendered
      mockOptions.forEach((option) => {
        expect(screen.getByText(option.label)).toBeInTheDocument();
        if (option.description) {
          expect(screen.getByText(option.description)).toBeInTheDocument();
        }
      });
    });
  });

  describe('Edge Cases', () => {
    test('handles undefined description gracefully', () => {
      const optionsWithUndefinedDescription = [
        {
          value: 'option1',
          label: 'Option 1',
          description: undefined,
        },
      ];

      render(<RadioGroup {...defaultProps} options={optionsWithUndefinedDescription} />);

      expect(screen.getByText('Option 1')).toBeInTheDocument();
    });

    test('handles empty string description', () => {
      const optionsWithEmptyDescription = [
        {
          value: 'option1',
          label: 'Option 1',
          description: '',
        },
      ];

      render(<RadioGroup {...defaultProps} options={optionsWithEmptyDescription} />);

      expect(screen.getByText('Option 1')).toBeInTheDocument();
    });

    test('handles special characters in values and labels', () => {
      const specialOptions = [
        {
          value: 'option-with-dashes',
          label: 'Option with Special Characters !@#$%',
          description: 'Description with émojis 🎉 and ñ characters',
        },
      ];

      render(<RadioGroup {...defaultProps} options={specialOptions} value="option-with-dashes" />);

      expect(screen.getByText('Option with Special Characters !@#$%')).toBeInTheDocument();
      expect(screen.getByText('Description with émojis 🎉 and ñ characters')).toBeInTheDocument();
    });

    test('handles long labels and descriptions', () => {
      const longTextOptions = [
        {
          value: 'long-option',
          label: 'This is a very long label that might wrap to multiple lines in the UI',
          description:
            'This is a very long description that contains a lot of text and might also wrap to multiple lines when displayed in the user interface',
        },
      ];

      render(<RadioGroup {...defaultProps} options={longTextOptions} />);

      expect(
        screen.getByText('This is a very long label that might wrap to multiple lines in the UI')
      ).toBeInTheDocument();
      expect(
        screen.getByText(
          'This is a very long description that contains a lot of text and might also wrap to multiple lines when displayed in the user interface'
        )
      ).toBeInTheDocument();
    });
  });

  describe('PropTypes Validation', () => {
    test('works with string values', () => {
      const stringOptions = [
        { value: 'string1', label: 'String Option 1', description: 'String description' },
        { value: 'string2', label: 'String Option 2', description: 'String description' },
      ];

      render(<RadioGroup options={stringOptions} value="string1" setValue={jest.fn()} />);

      expect(screen.getByText('String Option 1')).toBeInTheDocument();
    });

    test('works with number values', () => {
      const numberOptions = [
        { value: 100, label: 'Number Option 1', description: 'Number description' },
        { value: 200, label: 'Number Option 2', description: 'Number description' },
      ];

      render(<RadioGroup options={numberOptions} value={100} setValue={jest.fn()} />);

      expect(screen.getByText('Number Option 1')).toBeInTheDocument();
    });
  });
});
