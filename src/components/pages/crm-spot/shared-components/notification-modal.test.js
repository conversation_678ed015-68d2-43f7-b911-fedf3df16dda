import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { NotificationModal } from './notification-modal';

// Mock the utilities and dependencies
jest.mock('util/time-ago', () => ({
  getTimeAgo: jest.fn(),
}));

jest.mock('date-fns', () => ({
  format: jest.fn(),
}));

// Mock Ant Design components
jest.mock('antd', () => ({
  ...jest.requireActual('antd'),
  Modal: ({ children, title, open, onCancel, footer, className, closable }) =>
    open ? (
      <div className={className} data-testid="notification-modal">
        <div data-testid="modal-header">{title}</div>
        <div data-testid="modal-body">{children}</div>
        {footer && <div data-testid="modal-footer">{footer}</div>}
      </div>
    ) : null,
  Button: ({ children, onClick, icon, type, className }) => (
    <button onClick={onClick} className={className} data-type={type} data-testid="button">
      {icon && <span data-testid="button-icon">{icon}</span>}
      {children}
    </button>
  ),
  Badge: ({ count, className, children }) => (
    <span className={className} data-testid="badge">
      {children}
      {count > 0 && <span data-testid="badge-count">{count}</span>}
    </span>
  ),
  Tabs: ({ activeKey, onChange, items, className, centered, type }) => (
    <div className={className} data-testid="tabs" data-active={activeKey}>
      {items.map((item) => (
        <button
          key={item.key}
          onClick={() => onChange(item.key)}
          data-testid={`tab-${item.key}`}
          className={activeKey === item.key ? 'active' : ''}>
          {item.label}
        </button>
      ))}
    </div>
  ),
  Flex: ({ children, justify, align }) => (
    <div data-testid="flex" data-justify={justify} data-align={align}>
      {children}
    </div>
  ),
}));

// Mock icons
jest.mock('@ant-design/icons', () => ({
  CheckOutlined: () => <span data-testid="check-icon">CheckIcon</span>,
  DeleteOutlined: () => <span data-testid="delete-icon">DeleteIcon</span>,
  CloseOutlined: () => <span data-testid="close-icon">CloseIcon</span>,
  InfoCircleOutlined: () => <span data-testid="info-icon">InfoIcon</span>,
  ClockCircleOutlined: () => <span data-testid="clock-icon">ClockIcon</span>,
  BellOutlined: () => <span data-testid="bell-icon">BellIcon</span>,
}));

// Mock styles
jest.mock('./notification-modal.module.scss', () => ({
  notificationModal: 'notificationModal',
  modalHeader: 'modalHeader',
  titleWithBadge: 'titleWithBadge',
  headerActions: 'headerActions',
  headerButton: 'headerButton',
  closeButton: 'closeButton',
  tabs: 'tabs',
  notificationList: 'notificationList',
  notificationItem: 'notificationItem',
  unread: 'unread',
  notificationContent: 'notificationContent',
  iconCircle: 'iconCircle',
  leadIcon: 'leadIcon',
  systemIcon: 'systemIcon',
  taskIcon: 'taskIcon',
  notificationDetails: 'notificationDetails',
  notificationHeader: 'notificationHeader',
  titleContainer: 'titleContainer',
  priorityBadge: 'priorityBadge',
  high: 'high',
  medium: 'medium',
  low: 'low',
  timeAgo: 'timeAgo',
  timestamp: 'timestamp',
  markAsReadButton: 'markAsReadButton',
  deleteButton: 'deleteButton',
  emptyState: 'emptyState',
  allBadge: 'allBadge',
  leadBadge: 'leadBadge',
  taskBadge: 'taskBadge',
  systemBadge: 'systemBadge',
}));

describe('NotificationModal Component', () => {
  const mockGetTimeAgo = require('util/time-ago').getTimeAgo;
  const mockFormat = require('date-fns').format;

  const defaultProps = {
    visible: true,
    onClose: jest.fn(),
    notifications: [],
    markAsRead: jest.fn(),
    batchNotificationEdit: jest.fn(),
    deleteNotification: jest.fn(),
  };

  const mockNotifications = [
    {
      id: 1,
      type: 'lead_assigned',
      message: 'New lead assigned to you',
      created_at: '2024-01-15T10:30:00Z',
      read_at: null,
      priority: 'high',
    },
    {
      id: 2,
      type: 'system',
      message: 'System maintenance scheduled',
      created_at: '2024-01-14T15:45:00Z',
      read_at: '2024-01-14T16:00:00Z',
      priority: 'medium',
    },
    {
      id: 3,
      type: 'task_reminder',
      message: 'Follow up with client',
      created_at: '2024-01-13T09:15:00Z',
      read_at: null,
      priority: 'low',
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    mockGetTimeAgo.mockReturnValue('2 hours ago');
    mockFormat.mockReturnValue('Jan 15, 10:30 AM');
  });

  describe('Modal Rendering', () => {
    test('renders modal when visible is true', () => {
      render(<NotificationModal {...defaultProps} />);

      expect(screen.getByTestId('notification-modal')).toBeInTheDocument();
    });

    test('does not render modal when visible is false', () => {
      render(<NotificationModal {...defaultProps} visible={false} />);

      expect(screen.queryByTestId('notification-modal')).not.toBeInTheDocument();
    });

    test('renders modal header with title', () => {
      render(<NotificationModal {...defaultProps} />);

      expect(screen.getByText('Notifications')).toBeInTheDocument();
    });

    test('renders close button in header', () => {
      render(<NotificationModal {...defaultProps} />);

      const closeButtons = screen.getAllByTestId('button');
      const closeButton = closeButtons.find((button) => button.querySelector('[data-testid="close-icon"]'));
      expect(closeButton).toBeInTheDocument();
    });
  });

  describe('Tabs Functionality', () => {
    test('renders all tab options', () => {
      render(<NotificationModal {...defaultProps} notifications={mockNotifications} />);

      expect(screen.getByTestId('tab-all')).toBeInTheDocument();
      expect(screen.getByTestId('tab-lead_assigned')).toBeInTheDocument();
      expect(screen.getByTestId('tab-task_reminder')).toBeInTheDocument();
      expect(screen.getByTestId('tab-system')).toBeInTheDocument();
    });

    test('shows correct badge counts for unread notifications', () => {
      render(<NotificationModal {...defaultProps} notifications={mockNotifications} />);

      const badges = screen.getAllByTestId('badge-count');
      // Should show count of 2 unread notifications (id 1 and 3)
      expect(badges.some((badge) => badge.textContent === '2')).toBe(true);
    });

    test('switches tabs when clicked', async () => {
      render(<NotificationModal {...defaultProps} notifications={mockNotifications} />);

      const leadTab = screen.getByTestId('tab-lead_assigned');
      fireEvent.click(leadTab);

      await waitFor(() => {
        expect(screen.getByTestId('tabs')).toHaveAttribute('data-active', 'lead_assigned');
      });
    });
  });

  describe('Notification List', () => {
    test('displays notifications when available', () => {
      render(<NotificationModal {...defaultProps} notifications={mockNotifications} />);

      expect(screen.getByText('New lead assigned to you')).toBeInTheDocument();
      expect(screen.getByText('System maintenance scheduled')).toBeInTheDocument();
      expect(screen.getByText('Follow up with client')).toBeInTheDocument();
    });

    test('shows empty state when no notifications', () => {
      render(<NotificationModal {...defaultProps} notifications={[]} />);

      expect(screen.getByText('No notifications')).toBeInTheDocument();
    });

    test('filters notifications by tab selection', async () => {
      render(<NotificationModal {...defaultProps} notifications={mockNotifications} />);

      const leadTab = screen.getByTestId('tab-lead_assigned');
      fireEvent.click(leadTab);

      await waitFor(() => {
        expect(screen.getByText('New lead assigned to you')).toBeInTheDocument();
        expect(screen.queryByText('System maintenance scheduled')).not.toBeInTheDocument();
      });
    });
  });

  describe('Notification Items', () => {
    test('displays correct icons for different notification types', () => {
      render(<NotificationModal {...defaultProps} notifications={mockNotifications} />);

      expect(screen.getByTestId('bell-icon')).toBeInTheDocument(); // lead_assigned
      expect(screen.getByTestId('info-icon')).toBeInTheDocument(); // system
      expect(screen.getByTestId('clock-icon')).toBeInTheDocument(); // task_reminder
    });

    test('shows priority badges when present', () => {
      render(<NotificationModal {...defaultProps} notifications={mockNotifications} />);

      expect(screen.getByText('high')).toBeInTheDocument();
      expect(screen.getByText('medium')).toBeInTheDocument();
      expect(screen.getByText('low')).toBeInTheDocument();
    });

    test('displays time ago and formatted timestamp', () => {
      render(<NotificationModal {...defaultProps} notifications={mockNotifications} />);

      expect(screen.getAllByText('2 hours ago')).toHaveLength(3);
      expect(screen.getAllByText('Jan 15, 10:30 AM')).toHaveLength(3);
    });

    test('shows mark as read button only for unread notifications', () => {
      render(<NotificationModal {...defaultProps} notifications={mockNotifications} />);

      const markAsReadButtons = screen.getAllByText('Mark as read');
      expect(markAsReadButtons).toHaveLength(2); // Only for unread notifications
    });

    test('shows delete button for all notifications', () => {
      render(<NotificationModal {...defaultProps} notifications={mockNotifications} />);

      const deleteIcons = screen.getAllByTestId('delete-icon');
      // Should have 5 delete icons: 2 in header (mark all, delete all) + 3 for notifications
      expect(deleteIcons.length).toBeGreaterThanOrEqual(3);
    });

    test('handles notifications without created_at gracefully', () => {
      const notificationWithoutDate = [
        {
          id: 4,
          type: 'system',
          message: 'Test notification',
          created_at: null,
          read_at: null,
        },
      ];

      render(<NotificationModal {...defaultProps} notifications={notificationWithoutDate} />);

      expect(screen.getByText('Test notification')).toBeInTheDocument();
    });

    test('handles notifications without priority gracefully', () => {
      const notificationWithoutPriority = [
        {
          id: 5,
          type: 'lead_assigned',
          message: 'Test notification without priority',
          created_at: '2024-01-15T10:30:00Z',
          read_at: null,
        },
      ];

      render(<NotificationModal {...defaultProps} notifications={notificationWithoutPriority} />);

      expect(screen.getByText('Test notification without priority')).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    test('calls onClose when close button is clicked', () => {
      const mockOnClose = jest.fn();
      render(<NotificationModal {...defaultProps} onClose={mockOnClose} />);

      const closeButtons = screen.getAllByTestId('button');
      const closeButton = closeButtons.find((button) => button.querySelector('[data-testid="close-icon"]'));

      fireEvent.click(closeButton);
      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    test('calls markAsRead when mark as read button is clicked', async () => {
      const mockMarkAsRead = jest.fn().mockResolvedValue();
      render(<NotificationModal {...defaultProps} markAsRead={mockMarkAsRead} notifications={mockNotifications} />);

      const markAsReadButton = screen.getAllByText('Mark as read')[0];
      fireEvent.click(markAsReadButton);

      await waitFor(() => {
        expect(mockMarkAsRead).toHaveBeenCalledWith(1);
      });
    });

    test('calls deleteNotification when delete button is clicked', async () => {
      const mockDeleteNotification = jest.fn().mockResolvedValue();
      render(
        <NotificationModal
          {...defaultProps}
          deleteNotification={mockDeleteNotification}
          notifications={mockNotifications}
        />
      );

      const deleteButtons = screen
        .getAllByTestId('button')
        .filter(
          (button) => button.querySelector('[data-testid="delete-icon"]') && button.className.includes('deleteButton')
        );

      fireEvent.click(deleteButtons[0]);

      await waitFor(() => {
        expect(mockDeleteNotification).toHaveBeenCalledWith(1);
      });
    });
  });

  describe('Batch Actions', () => {
    test('shows mark all as read button when there are unread notifications', () => {
      render(<NotificationModal {...defaultProps} notifications={mockNotifications} />);

      expect(screen.getByText('Mark all as read')).toBeInTheDocument();
    });

    test('shows delete all button when there are notifications', () => {
      render(<NotificationModal {...defaultProps} notifications={mockNotifications} />);

      expect(screen.getByText('Delete all')).toBeInTheDocument();
    });

    test('hides batch buttons when no applicable notifications', () => {
      const allReadNotifications = mockNotifications.map((n) => ({ ...n, read_at: '2024-01-15T12:00:00Z' }));
      render(<NotificationModal {...defaultProps} notifications={allReadNotifications} />);

      expect(screen.queryByText('Mark all as read')).not.toBeInTheDocument();
    });

    test('calls batchNotificationEdit when mark all as read is clicked', async () => {
      const mockBatchEdit = jest.fn().mockResolvedValue();
      render(
        <NotificationModal {...defaultProps} batchNotificationEdit={mockBatchEdit} notifications={mockNotifications} />
      );

      const markAllButton = screen.getByText('Mark all as read');
      fireEvent.click(markAllButton);

      await waitFor(() => {
        expect(mockBatchEdit).toHaveBeenCalledWith([1, 3], 'read_at');
      });
    });

    test('calls batchNotificationEdit when delete all is clicked', async () => {
      const mockBatchEdit = jest.fn().mockResolvedValue();
      render(
        <NotificationModal {...defaultProps} batchNotificationEdit={mockBatchEdit} notifications={mockNotifications} />
      );

      const deleteAllButton = screen.getByText('Delete all');
      fireEvent.click(deleteAllButton);

      await waitFor(() => {
        expect(mockBatchEdit).toHaveBeenCalledWith([1, 2, 3], 'deleted_at');
      });
    });
  });

  describe('Utility Functions', () => {
    test('calls getTimeAgo utility with correct parameters', () => {
      render(<NotificationModal {...defaultProps} notifications={mockNotifications} />);

      expect(mockGetTimeAgo).toHaveBeenCalledWith('2024-01-15T10:30:00Z');
      expect(mockGetTimeAgo).toHaveBeenCalledWith('2024-01-14T15:45:00Z');
      expect(mockGetTimeAgo).toHaveBeenCalledWith('2024-01-13T09:15:00Z');
    });

    test('calls date-fns format with correct parameters', () => {
      render(<NotificationModal {...defaultProps} notifications={mockNotifications} />);

      expect(mockFormat).toHaveBeenCalledWith(expect.any(Date), 'MMM d, h:mm a');
    });
  });

  describe('Edge Cases', () => {
    test('handles empty notification list gracefully', () => {
      render(<NotificationModal {...defaultProps} notifications={[]} />);

      expect(screen.getByText('No notifications')).toBeInTheDocument();
      expect(screen.queryByText('Mark all as read')).not.toBeInTheDocument();
      expect(screen.queryByText('Delete all')).not.toBeInTheDocument();
    });

    test('handles notifications with missing fields gracefully', () => {
      const incompleteNotifications = [
        {
          id: 1,
          type: 'lead_assigned',
          message: 'Incomplete notification',
          // missing created_at, read_at, priority
        },
      ];

      render(<NotificationModal {...defaultProps} notifications={incompleteNotifications} />);

      expect(screen.getByText('Incomplete notification')).toBeInTheDocument();
    });

    test('handles unknown notification type gracefully', () => {
      const unknownTypeNotification = [
        {
          id: 1,
          type: 'unknown_type',
          message: 'Unknown type notification',
          created_at: '2024-01-15T10:30:00Z',
          read_at: null,
        },
      ];

      render(<NotificationModal {...defaultProps} notifications={unknownTypeNotification} />);

      expect(screen.getByText('Unknown type notification')).toBeInTheDocument();
    });
  });
});
