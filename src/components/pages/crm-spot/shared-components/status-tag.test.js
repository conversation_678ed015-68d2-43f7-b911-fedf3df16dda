import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { StatusTag } from './status-tag';

// Mock Ant Design components
jest.mock('antd', () => ({
  Tag: ({ children, className, style }) => (
    <span className={className} style={style} data-testid="status-tag">
      {children}
    </span>
  ),
}));

// Mock styles
jest.mock('./status-tag.module.scss', () => ({
  statusTag: 'statusTag',
}));

describe('StatusTag Component', () => {
  describe('Rendering', () => {
    test('renders status tag with correct text', () => {
      render(<StatusTag status="new" />);

      const tag = screen.getByTestId('status-tag');
      expect(tag).toBeInTheDocument();
      expect(tag).toHaveTextContent('new');
    });

    test('applies correct CSS class', () => {
      render(<StatusTag status="new" />);

      const tag = screen.getByTestId('status-tag');
      expect(tag).toHaveClass('statusTag');
    });

    test('renders with different status values', () => {
      const { rerender } = render(<StatusTag status="attempting" />);
      expect(screen.getByText('attempting')).toBeInTheDocument();

      rerender(<StatusTag status="working" />);
      expect(screen.getByText('working')).toBeInTheDocument();

      rerender(<StatusTag status="application-sent" />);
      expect(screen.getByText('application-sent')).toBeInTheDocument();
    });
  });

  describe('Status Colors', () => {
    test('applies correct styles for "new" status', () => {
      render(<StatusTag status="new" />);

      const tag = screen.getByTestId('status-tag');
      expect(tag).toHaveStyle({
        color: '#1d4ed8',
        backgroundColor: '#eff6ff',
        border: '1px solid #1d4ed8',
        textTransform: 'capitalize',
      });
    });

    test('applies correct styles for "attempting" status', () => {
      render(<StatusTag status="attempting" />);

      const tag = screen.getByTestId('status-tag');
      expect(tag).toHaveStyle({
        color: '#a16207',
        backgroundColor: '#fefce8',
        border: '1px solid #a16207',
        textTransform: 'capitalize',
      });
    });

    test('applies correct styles for "working" status', () => {
      render(<StatusTag status="working" />);

      const tag = screen.getByTestId('status-tag');
      expect(tag).toHaveStyle({
        color: '#7e22ce',
        backgroundColor: '#faf5ff',
        border: '1px solid #7e22ce',
        textTransform: 'capitalize',
      });
    });

    test('applies correct styles for "application-sent" status', () => {
      render(<StatusTag status="application-sent" />);

      const tag = screen.getByTestId('status-tag');
      expect(tag).toHaveStyle({
        color: '#0f9d58',
        backgroundColor: '#e8f5e9',
        border: '1px solid #0f9d58',
        textTransform: 'capitalize',
      });
    });

    test('applies correct styles for "application-received" status', () => {
      render(<StatusTag status="application-received" />);

      const tag = screen.getByTestId('status-tag');
      expect(tag).toHaveStyle({
        color: '#db4437',
        backgroundColor: '#ffebee',
        border: '1px solid #db4437',
        textTransform: 'capitalize',
      });
    });

    test('applies default styles for unknown status', () => {
      render(<StatusTag status="unknown-status" />);

      const tag = screen.getByTestId('status-tag');
      expect(tag).toHaveStyle({
        color: '#44464B',
        backgroundColor: '#f1f3f4',
        border: '1px solid #44464B',
        textTransform: 'capitalize',
      });
    });
  });

  describe('Status Values', () => {
    test('displays exact status text without modification', () => {
      render(<StatusTag status="application-sent" />);
      expect(screen.getByText('application-sent')).toBeInTheDocument();
    });

    test('handles status with hyphens', () => {
      render(<StatusTag status="application-received" />);
      expect(screen.getByText('application-received')).toBeInTheDocument();
    });

    test('handles single word status', () => {
      render(<StatusTag status="new" />);
      expect(screen.getByText('new')).toBeInTheDocument();
    });

    test('handles status with multiple words', () => {
      render(<StatusTag status="in-progress" />);
      expect(screen.getByText('in-progress')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    test('handles empty string status', () => {
      render(<StatusTag status="" />);

      const tag = screen.getByTestId('status-tag');
      expect(tag).toBeInTheDocument();
      expect(tag).toHaveTextContent('');
      expect(tag).toHaveStyle({
        color: '#44464B',
        backgroundColor: '#f1f3f4',
        border: '1px solid #44464B',
        textTransform: 'capitalize',
      });
    });

    test('handles status with special characters', () => {
      render(<StatusTag status="status@#$%" />);

      const tag = screen.getByTestId('status-tag');
      expect(tag).toHaveTextContent('status@#$%');
      expect(tag).toHaveStyle({
        color: '#44464B',
        backgroundColor: '#f1f3f4',
        border: '1px solid #44464B',
        textTransform: 'capitalize',
      });
    });

    test('handles status with numbers', () => {
      render(<StatusTag status="status123" />);

      const tag = screen.getByTestId('status-tag');
      expect(tag).toHaveTextContent('status123');
    });

    test('handles status with uppercase letters', () => {
      render(<StatusTag status="NEW" />);

      const tag = screen.getByTestId('status-tag');
      expect(tag).toHaveTextContent('NEW');
      expect(tag).toHaveStyle({
        color: '#44464B',
        backgroundColor: '#f1f3f4',
        textTransform: 'capitalize',
      });
    });

    test('handles status with mixed case', () => {
      render(<StatusTag status="Application-Sent" />);

      const tag = screen.getByTestId('status-tag');
      expect(tag).toHaveTextContent('Application-Sent');
      expect(tag).toHaveStyle({
        color: '#44464B',
        backgroundColor: '#f1f3f4',
        textTransform: 'capitalize',
      });
    });

    test('handles very long status text', () => {
      const longStatus = 'this-is-a-very-long-status-name-that-might-cause-layout-issues';
      render(<StatusTag status={longStatus} />);

      const tag = screen.getByTestId('status-tag');
      expect(tag).toHaveTextContent(longStatus);
    });

    test('handles status with spaces (though not typical)', () => {
      render(<StatusTag status="status with spaces" />);

      const tag = screen.getByTestId('status-tag');
      expect(tag).toHaveTextContent('status with spaces');
    });
  });

  describe('Style Properties', () => {
    test('always applies textTransform capitalize', () => {
      const statuses = ['new', 'attempting', 'working', 'unknown'];

      statuses.forEach((status) => {
        const { unmount } = render(<StatusTag status={status} />);
        const tag = screen.getByTestId('status-tag');
        expect(tag).toHaveStyle({ textTransform: 'capitalize' });
        unmount();
      });
    });

    test('always applies border with matching color', () => {
      render(<StatusTag status="new" />);
      const tag = screen.getByTestId('status-tag');
      expect(tag).toHaveStyle({ border: '1px solid #1d4ed8' });
    });

    test('applies consistent style structure for all statuses', () => {
      const statuses = ['new', 'attempting', 'working', 'application-sent', 'application-received', 'unknown'];

      statuses.forEach((status) => {
        const { unmount } = render(<StatusTag status={status} />);
        const tag = screen.getByTestId('status-tag');

        // Check that all required style properties are present
        const computedStyle = window.getComputedStyle(tag);
        expect(tag.style.color).toBeTruthy();
        expect(tag.style.backgroundColor).toBeTruthy();
        expect(tag.style.border).toBeTruthy();
        expect(tag.style.textTransform).toBe('capitalize');

        unmount();
      });
    });
  });

  describe('Color Mapping', () => {
    test('maps all defined statuses to correct colors', () => {
      const statusColorMap = {
        new: '#1d4ed8',
        attempting: '#a16207',
        working: '#7e22ce',
        'application-sent': '#0f9d58',
        'application-received': '#db4437',
      };

      Object.entries(statusColorMap).forEach(([status, expectedColor]) => {
        const { unmount } = render(<StatusTag status={status} />);
        const tag = screen.getByTestId('status-tag');
        expect(tag).toHaveStyle({ color: expectedColor });
        unmount();
      });
    });

    test('uses default color for undefined statuses', () => {
      const undefinedStatuses = ['pending', 'completed', 'cancelled', 'draft'];

      undefinedStatuses.forEach((status) => {
        const { unmount } = render(<StatusTag status={status} />);
        const tag = screen.getByTestId('status-tag');
        expect(tag).toHaveStyle({
          color: '#44464B',
          backgroundColor: '#f1f3f4',
          border: '1px solid #44464B',
        });
        unmount();
      });
    });
  });

  describe('Component Integration', () => {
    test('renders multiple status tags correctly', () => {
      render(
        <div>
          <StatusTag status="new" />
          <StatusTag status="working" />
          <StatusTag status="application-sent" />
        </div>
      );

      expect(screen.getByText('new')).toBeInTheDocument();
      expect(screen.getByText('working')).toBeInTheDocument();
      expect(screen.getByText('application-sent')).toBeInTheDocument();
    });

    test('maintains independence between multiple instances', () => {
      render(
        <div>
          <StatusTag status="new" />
          <StatusTag status="attempting" />
        </div>
      );

      const tags = screen.getAllByTestId('status-tag');
      expect(tags).toHaveLength(2);
      expect(tags[0]).toHaveStyle({ color: '#1d4ed8' });
      expect(tags[1]).toHaveStyle({ color: '#a16207' });
    });
  });
});
