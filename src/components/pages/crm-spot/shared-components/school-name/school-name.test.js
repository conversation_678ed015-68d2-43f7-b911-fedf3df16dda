import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { SchoolName } from './school-name';

describe('SchoolName', () => {
  const defaultProps = {
    name: 'Test School',
    matchLevel: 1,
    showExportIcon: true,
    onClick: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render the component with school name', () => {
      render(<SchoolName {...defaultProps} />);

      expect(screen.getByText('Test School')).toBeInTheDocument();
    });

    it('should render with export icon by default', () => {
      render(<SchoolName {...defaultProps} />);

      const exportIcon = document.querySelector('.anticon-export');
      expect(exportIcon).toBeInTheDocument();
    });

    it('should render without export icon when showExportIcon is false', () => {
      render(<SchoolName {...defaultProps} showExportIcon={false} />);

      const exportIcon = document.querySelector('.anticon-export');
      expect(exportIcon).not.toBeInTheDocument();
    });

    it('should render with star icon for high match level', () => {
      render(<SchoolName {...defaultProps} matchLevel="high" />);

      const starIcon = document.querySelector('.anticon-star');
      expect(starIcon).toBeInTheDocument();
    });

    it('should not render star icon for medium match level', () => {
      render(<SchoolName {...defaultProps} matchLevel="medium" />);

      const starIcon = document.querySelector('.anticon-star');
      expect(starIcon).not.toBeInTheDocument();
    });

    it('should not render star icon for low match level', () => {
      render(<SchoolName {...defaultProps} matchLevel="low" />);

      const starIcon = document.querySelector('.anticon-star');
      expect(starIcon).not.toBeInTheDocument();
    });
  });

  describe('Match Level Styling', () => {
    it('should apply high match styling for matchLevel 1', () => {
      render(<SchoolName {...defaultProps} matchLevel={1} />);

      const container = document.querySelector('[class*="container"]');
      expect(container.className).toMatch(/highMatch/);
    });

    it('should apply medium match styling for matchLevel 2', () => {
      render(<SchoolName {...defaultProps} matchLevel={2} />);

      const container = document.querySelector('[class*="container"]');
      expect(container.className).toMatch(/mediumMatch/);
    });

    it('should apply low match styling for matchLevel 3', () => {
      render(<SchoolName {...defaultProps} matchLevel={3} />);

      const container = document.querySelector('[class*="container"]');
      expect(container.className).toMatch(/lowMatch/);
    });

    it('should default to high match styling for invalid matchLevel', () => {
      render(<SchoolName {...defaultProps} matchLevel={999} />);

      const container = document.querySelector('[class*="container"]');
      expect(container.className).toMatch(/highMatch/);
    });

    it('should apply high match styling for string "high"', () => {
      render(<SchoolName {...defaultProps} matchLevel="high" />);

      const container = document.querySelector('[class*="container"]');
      expect(container.className).toMatch(/highMatch/);
    });
  });

  describe('Click Functionality', () => {
    it('should call onClick when component is clicked', () => {
      const mockOnClick = jest.fn();
      render(<SchoolName {...defaultProps} onClick={mockOnClick} />);

      const container = document.querySelector('[class*="container"]');
      fireEvent.click(container);

      expect(mockOnClick).toHaveBeenCalledTimes(1);
    });

    it('should not crash when onClick is not provided', () => {
      expect(() => {
        render(<SchoolName name="Test School" />);
      }).not.toThrow();
    });

    it('should handle multiple clicks', () => {
      const mockOnClick = jest.fn();
      render(<SchoolName {...defaultProps} onClick={mockOnClick} />);

      const container = document.querySelector('[class*="container"]');
      fireEvent.click(container);
      fireEvent.click(container);
      fireEvent.click(container);

      expect(mockOnClick).toHaveBeenCalledTimes(3);
    });
  });

  describe('CSS Classes and Structure', () => {
    it('should apply correct CSS classes', () => {
      render(<SchoolName {...defaultProps} />);

      const container = document.querySelector('[class*="container"]');
      expect(container).toBeInTheDocument();
      expect(container.className).toMatch(/container/);
    });

    it('should have correct Flex layout structure', () => {
      render(<SchoolName {...defaultProps} />);

      // Check for Ant Design Flex components
      const flexElements = document.querySelectorAll('.ant-flex');
      expect(flexElements.length).toBeGreaterThan(0);
    });

    it('should apply font-600 class to school name', () => {
      render(<SchoolName {...defaultProps} />);

      const nameElement = screen.getByText('Test School');
      expect(nameElement).toHaveClass('font-600');
    });

    it('should apply star styling when star is present', () => {
      render(<SchoolName {...defaultProps} matchLevel="high" />);

      const starIcon = document.querySelector('.anticon-star');
      expect(starIcon.className).toMatch(/star/);
    });
  });

  describe('PropTypes Validation', () => {
    it('should accept string name', () => {
      expect(() => {
        render(<SchoolName name="Valid School Name" />);
      }).not.toThrow();
    });

    it('should accept boolean showExportIcon', () => {
      expect(() => {
        render(<SchoolName name="Test" showExportIcon={true} />);
        render(<SchoolName name="Test" showExportIcon={false} />);
      }).not.toThrow();
    });

    it('should accept function onClick', () => {
      const mockFunction = jest.fn();
      expect(() => {
        render(<SchoolName name="Test" onClick={mockFunction} />);
      }).not.toThrow();
    });
  });

  describe('Default Props', () => {
    it('should use default matchLevel when not provided', () => {
      render(<SchoolName name="Test School" />);

      const container = document.querySelector('[class*="container"]');
      expect(container.className).toMatch(/highMatch/);
    });

    it('should show export icon by default', () => {
      render(<SchoolName name="Test School" />);

      const exportIcon = document.querySelector('.anticon-export');
      expect(exportIcon).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty school name', () => {
      render(<SchoolName name="" />);

      const nameElement = document.querySelector('.font-600');
      expect(nameElement).toBeInTheDocument();
      expect(nameElement.textContent).toBe('');
    });

    it('should handle long school names', () => {
      const longName = 'Very Long School Name That Might Overflow The Container';
      render(<SchoolName name={longName} />);

      expect(screen.getByText(longName)).toBeInTheDocument();
    });

    it('should handle special characters in school name', () => {
      const specialName = "St. Mary's School & Academy (K-12)";
      render(<SchoolName name={specialName} />);

      expect(screen.getByText(specialName)).toBeInTheDocument();
    });

    it('should handle numeric matchLevel values', () => {
      render(<SchoolName name="Test" matchLevel={1} />);
      render(<SchoolName name="Test" matchLevel={2} />);
      render(<SchoolName name="Test" matchLevel={3} />);

      // Should not throw any errors
      expect(screen.getAllByText('Test')).toHaveLength(3);
    });

    it('should handle string matchLevel values', () => {
      render(<SchoolName name="Test1" matchLevel="high" />);
      render(<SchoolName name="Test2" matchLevel="medium" />);
      render(<SchoolName name="Test3" matchLevel="low" />);

      // Should not throw any errors
      expect(screen.getByText('Test1')).toBeInTheDocument();
      expect(screen.getByText('Test2')).toBeInTheDocument();
      expect(screen.getByText('Test3')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should be clickable and keyboard accessible', () => {
      const mockOnClick = jest.fn();
      render(<SchoolName {...defaultProps} onClick={mockOnClick} />);

      const container = document.querySelector('[class*="container"]');
      expect(container).toBeInTheDocument();

      // Component should be clickable
      fireEvent.click(container);
      expect(mockOnClick).toHaveBeenCalled();
    });

    it('should have proper cursor styling class', () => {
      render(<SchoolName {...defaultProps} />);

      const container = document.querySelector('[class*="container"]');
      expect(container).toBeInTheDocument();
      expect(container.className).toMatch(/container/);
    });
  });

  describe('Icon Rendering', () => {
    it('should render StarFilled icon for high match', () => {
      render(<SchoolName {...defaultProps} matchLevel="high" />);

      const starIcon = document.querySelector('.anticon-star');
      expect(starIcon).toBeInTheDocument();
    });

    it('should render ExportOutlined icon when showExportIcon is true', () => {
      render(<SchoolName {...defaultProps} showExportIcon={true} />);

      const exportIcon = document.querySelector('.anticon-export');
      expect(exportIcon).toBeInTheDocument();
    });

    it('should not render any star icon for non-high match levels', () => {
      render(<SchoolName name="Test1" matchLevel={2} />);
      render(<SchoolName name="Test2" matchLevel={3} />);
      render(<SchoolName name="Test3" matchLevel="medium" />);
      render(<SchoolName name="Test4" matchLevel="low" />);

      const starIcons = document.querySelectorAll('.anticon-star');
      expect(starIcons).toHaveLength(0);
    });
  });
});
