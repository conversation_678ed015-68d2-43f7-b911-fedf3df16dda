import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ConfigurationProgress } from './configuration-progress';
import { SyncOutlined, SettingOutlined } from '@ant-design/icons';

describe('ConfigurationProgress', () => {
  const defaultProps = {
    title: 'Test Configuration',
    status: 'Test Status',
    progress: 50,
    configuredSettings: 2,
    totalSettings: 4,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render the component with default props', () => {
      render(<ConfigurationProgress />);

      expect(screen.getByText('Configuration Progress')).toBeInTheDocument();
      expect(screen.getByText('undefined of 3 settings configured')).toBeInTheDocument();
    });

    it('should render with custom title and status', () => {
      render(<ConfigurationProgress {...defaultProps} />);

      expect(screen.getByText('Test Configuration')).toBeInTheDocument();
      expect(screen.getByText('Test Status')).toBeInTheDocument();
    });

    it('should render with custom icon', () => {
      const customIcon = <SettingOutlined data-testid="custom-icon" />;
      render(<ConfigurationProgress icon={customIcon} />);

      expect(screen.getByTestId('custom-icon')).toBeInTheDocument();
    });

    it('should render default SyncOutlined icon when no icon provided', () => {
      render(<ConfigurationProgress />);

      const syncIcon = document.querySelector('.anticon-sync');
      expect(syncIcon).toBeInTheDocument();
    });
  });

  describe('Progress Calculation', () => {
    it('should use provided progress value', () => {
      render(<ConfigurationProgress progress={75} />);

      const progressFill = document.querySelector('[class*="progressFill"]');
      expect(progressFill).toHaveStyle('width: 75%');
    });

    it('should calculate progress from configuredSettings and totalSettings', () => {
      render(<ConfigurationProgress configuredSettings={3} totalSettings={4} />);

      const progressFill = document.querySelector('[class*="progressFill"]');
      expect(progressFill).toHaveStyle('width: 75%'); // 3/4 * 100 = 75%
    });

    it('should prioritize explicit progress over calculated progress', () => {
      render(<ConfigurationProgress progress={60} configuredSettings={3} totalSettings={4} />);

      const progressFill = document.querySelector('[class*="progressFill"]');
      expect(progressFill).toHaveStyle('width: 60%');
    });

    it('should default to 0% when no progress data provided', () => {
      render(<ConfigurationProgress />);

      const progressFill = document.querySelector('[class*="progressFill"]');
      expect(progressFill).toHaveStyle('width: 0%');
    });

    it('should handle 100% progress correctly', () => {
      render(<ConfigurationProgress configuredSettings={4} totalSettings={4} />);

      const progressFill = document.querySelector('[class*="progressFill"]');
      expect(progressFill).toHaveStyle('width: 100%');
    });
  });

  describe('Status Display', () => {
    it('should use provided status when available', () => {
      render(<ConfigurationProgress status="Custom Status" />);

      expect(screen.getByText('Custom Status')).toBeInTheDocument();
    });

    it('should generate status from configuredSettings and totalSettings', () => {
      render(<ConfigurationProgress configuredSettings={2} totalSettings={5} />);

      expect(screen.getByText('2 of 5 settings configured')).toBeInTheDocument();
    });

    it('should prioritize explicit status over generated status', () => {
      render(<ConfigurationProgress status="Explicit Status" configuredSettings={2} totalSettings={5} />);

      expect(screen.getByText('Explicit Status')).toBeInTheDocument();
      expect(screen.queryByText('2 of 5 settings configured')).not.toBeInTheDocument();
    });

    it('should show disabled message when feature is disabled', () => {
      render(
        <ConfigurationProgress
          isEnabled={false}
          disabledMessage="Feature is disabled"
          configuredSettings={2}
          totalSettings={5}
        />
      );

      expect(screen.getByText('Feature is disabled')).toBeInTheDocument();
    });

    it('should use default disabled message when feature is disabled', () => {
      render(<ConfigurationProgress isEnabled={false} configuredSettings={2} totalSettings={5} />);

      expect(screen.getByText('Feature disabled')).toBeInTheDocument();
    });
  });

  describe('Color Styling', () => {
    it('should use active color when isActive and isEnabled are true', () => {
      render(<ConfigurationProgress isActive isEnabled activeColor="#ff0000" progress={50} />);

      const progressFill = document.querySelector('[class*="progressFill"]');
      expect(progressFill).toHaveStyle('background-color: #ff0000');
    });

    it('should use inactive color when isActive is false', () => {
      render(<ConfigurationProgress isActive={false} isEnabled inactiveColor="#cccccc" progress={50} />);

      const progressFill = document.querySelector('[class*="progressFill"]');
      expect(progressFill).toHaveStyle('background-color: #cccccc');
    });

    it('should use inactive color when isEnabled is false', () => {
      render(<ConfigurationProgress isActive isEnabled={false} inactiveColor="#cccccc" progress={50} />);

      const progressFill = document.querySelector('[class*="progressFill"]');
      expect(progressFill).toHaveStyle('background-color: #cccccc');
    });

    it('should use default colors when not specified', () => {
      render(<ConfigurationProgress progress={50} />);

      const progressFill = document.querySelector('[class*="progressFill"]');
      expect(progressFill).toHaveStyle('background-color: #00506C');
    });
  });

  describe('CSS Classes and Structure', () => {
    it('should apply correct CSS classes', () => {
      render(<ConfigurationProgress />);

      expect(document.querySelector('[class*="progressContainer"]')).toBeInTheDocument();
      expect(document.querySelector('[class*="progressHeader"]')).toBeInTheDocument();
      expect(document.querySelector('[class*="progressTitle"]')).toBeInTheDocument();
      expect(document.querySelector('[class*="progressStatus"]')).toBeInTheDocument();
      expect(document.querySelector('[class*="progressBar"]')).toBeInTheDocument();
      expect(document.querySelector('[class*="progressFill"]')).toBeInTheDocument();
    });

    it('should have correct DOM structure', () => {
      render(<ConfigurationProgress title="Test Title" />);

      const container = document.querySelector('[class*="progressContainer"]');
      const header = document.querySelector('[class*="progressHeader"]');
      const title = document.querySelector('[class*="progressTitle"]');
      const status = document.querySelector('[class*="progressStatus"]');
      const bar = document.querySelector('[class*="progressBar"]');
      const fill = document.querySelector('[class*="progressFill"]');

      expect(container).toContainElement(header);
      expect(container).toContainElement(bar);
      expect(header).toContainElement(title);
      expect(header).toContainElement(status);
      expect(bar).toContainElement(fill);
    });

    it('should apply progressIcon class to default icon', () => {
      render(<ConfigurationProgress />);

      const icon = document.querySelector('[class*="progressIcon"]');
      expect(icon).toBeInTheDocument();
    });
  });

  describe('PropTypes Validation', () => {
    it('should accept string title', () => {
      expect(() => {
        render(<ConfigurationProgress title="Valid Title" />);
      }).not.toThrow();
    });

    it('should accept React node icon', () => {
      const iconNode = <div>Custom Icon</div>;
      expect(() => {
        render(<ConfigurationProgress icon={iconNode} />);
      }).not.toThrow();
    });

    it('should accept number progress', () => {
      expect(() => {
        render(<ConfigurationProgress progress={75} />);
      }).not.toThrow();
    });

    it('should accept boolean isActive and isEnabled', () => {
      expect(() => {
        render(<ConfigurationProgress isActive isEnabled={false} />);
      }).not.toThrow();
    });
  });

  describe('Edge Cases', () => {
    it('should handle zero configuredSettings', () => {
      render(<ConfigurationProgress configuredSettings={0} totalSettings={5} />);

      expect(screen.getByText('0 of 5 settings configured')).toBeInTheDocument();
      const progressFill = document.querySelector('[class*="progressFill"]');
      expect(progressFill).toHaveStyle('width: 0%');
    });

    it('should handle undefined configuredSettings', () => {
      render(<ConfigurationProgress totalSettings={5} />);

      expect(screen.getByText('undefined of 5 settings configured')).toBeInTheDocument();
      const progressFill = document.querySelector('[class*="progressFill"]');
      expect(progressFill).toHaveStyle('width: 0%');
    });

    it('should handle progress over 100%', () => {
      render(<ConfigurationProgress progress={150} />);

      const progressFill = document.querySelector('[class*="progressFill"]');
      expect(progressFill).toHaveStyle('width: 150%');
    });

    it('should handle negative progress', () => {
      render(<ConfigurationProgress progress={-10} />);

      const progressFill = document.querySelector('[class*="progressFill"]');
      expect(progressFill).toHaveStyle('width: -10%');
    });

    it('should handle empty title', () => {
      render(<ConfigurationProgress title="" />);

      const titleElement = document.querySelector('[class*="progressTitle"]');
      expect(titleElement).toBeInTheDocument();
    });

    it('should handle null icon', () => {
      render(<ConfigurationProgress icon={null} />);

      const titleElement = document.querySelector('[class*="progressTitle"]');
      expect(titleElement).toBeInTheDocument();
    });
  });

  describe('Default Props', () => {
    it('should use default title when not provided', () => {
      render(<ConfigurationProgress />);

      expect(screen.getByText('Configuration Progress')).toBeInTheDocument();
    });

    it('should use default totalSettings when not provided', () => {
      render(<ConfigurationProgress configuredSettings={2} />);

      expect(screen.getByText('2 of 3 settings configured')).toBeInTheDocument();
    });

    it('should use default colors when not provided', () => {
      render(<ConfigurationProgress progress={50} />);

      const progressFill = document.querySelector('[class*="progressFill"]');
      expect(progressFill).toHaveStyle('background-color: #00506C');
    });

    it('should be active and enabled by default', () => {
      render(<ConfigurationProgress progress={50} />);

      const progressFill = document.querySelector('[class*="progressFill"]');
      expect(progressFill).toHaveStyle('background-color: #00506C');
    });
  });
});
