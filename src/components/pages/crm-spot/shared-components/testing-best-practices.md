# Mejores Prácticas para Pruebas Unitarias con Datos Dinámicos

## Problema Identificado

**Tu pregunta es muy acertada**: Las pruebas actuales fallarán cuando los datos vengan de la base de datos porque están buscando textos específicos hardcodeados como:

```javascript
// ❌ PROBLEMÁTICO - Fallará con datos reales
expect(screen.getByText('Riverside Elementary')).toBeInTheDocument();
expect(screen.getByText('Application Sent')).toBeInTheDocument();
```

## Solución Implementada

### 1. **Pruebas Basadas en Patrones en lugar de Textos Específicos**

```javascript
// ✅ ROBUSTO - Funciona con cualquier dato
// Buscar IDs de Schola por patrón
const scholaIds = screen.getAllByText(/^SCH-\d+-\d+$/);
expect(scholaIds).toHaveLength(2);

// Buscar fechas por patrón
const datePattern = /\w{3}\s\d{1,2},\s\d{4}\s\d{2}:\d{2}/;
const dateElements = Array.from(popoverContent.querySelectorAll('*')).filter(
  el => datePattern.test(el.textContent)
);
expect(dateElements.length).toBeGreaterThan(0);
```

### 2. **Pruebas Basadas en Estructura CSS en lugar de Contenido**

```javascript
// ✅ ROBUSTO - Verifica estructura, no contenido específico
const schoolElements = popoverContent.querySelectorAll('[class*="schoolName"]');
expect(schoolElements.length).toBeGreaterThan(0);

const statusElements = popoverContent.querySelectorAll('[class*="status"]');
expect(statusElements.length).toBeGreaterThan(0);
```

### 3. **Componente Preparado para Datos Dinámicos**

```javascript
// Componente actualizado para aceptar datos como props
export const RelationshipManagerHoverCard = ({ 
  rms, 
  leadId, 
  relationshipManagers // ← Nuevo prop para datos reales
}) => {
  // Usar datos proporcionados o fallback a mock data
  const rmData = relationshipManagers || (/* mock data */);
```

## Estrategias de Pruebas Robustas

### 1. **Usar Selectores por Atributos de Prueba**

```javascript
// ✅ MEJOR PRÁCTICA
<div data-testid="relationship-manager-item">
  <span data-testid="school-name">{rm.schoolName}</span>
  <span data-testid="schola-id">{rm.scholaId}</span>
</div>

// En las pruebas
const schoolNames = screen.getAllByTestId('school-name');
expect(schoolNames).toHaveLength(expectedCount);
```

### 2. **Verificar Comportamiento, No Contenido Específico**

```javascript
// ✅ ROBUSTO - Verifica que el click funciona
const scholaIdLinks = screen.getAllByText(/^SCH-\d+-\d+$/);
fireEvent.click(scholaIdLinks[0]);
expect(mockWindowOpen).toHaveBeenCalledWith(
  expect.stringMatching(/^\/rm\/schola\/SCH-\d+-\d+$/), 
  '_blank'
);
```

### 3. **Usar Mocks de Datos Controlados en Pruebas**

```javascript
// ✅ CONTROLADO - Datos conocidos para pruebas
const mockRelationshipManagers = [
  {
    scholaId: 'SCH-TEST-1',
    schoolName: 'Test School 1',
    dateEntered: 'Jan 01, 2024 10:00',
    status: 'Active'
  }
];

render(
  <RelationshipManagerHoverCard 
    rms={1} 
    leadId="test" 
    relationshipManagers={mockRelationshipManagers}
  />
);
```

## Beneficios de Esta Aproximación

### ✅ **Ventajas**

1. **Resistente a Cambios de Datos**: Las pruebas no fallan cuando cambian los datos reales
2. **Enfoque en Funcionalidad**: Verifica que el componente funciona, no contenido específico
3. **Mantenimiento Reducido**: Menos actualizaciones de pruebas cuando cambian los datos
4. **Flexibilidad**: Funciona tanto con datos mock como reales

### ⚠️ **Consideraciones**

1. **Algunas pruebas pueden ser menos específicas**: Pero esto es aceptable para robustez
2. **Requiere estructura CSS consistente**: Los selectores dependen de clases CSS
3. **Necesita documentación clara**: Para que otros desarrolladores entiendan los patrones

## Implementación Recomendada

### Para Nuevos Componentes:

1. **Diseñar con `data-testid`** desde el inicio
2. **Usar props para datos** en lugar de hardcodear
3. **Escribir pruebas basadas en comportamiento**

### Para Componentes Existentes:

1. **Refactorizar gradualmente** las pruebas más frágiles
2. **Mantener algunas pruebas específicas** para casos críticos
3. **Agregar props opcionales** para datos dinámicos

## Ejemplo de Migración

```javascript
// ANTES - Frágil
it('should display school information', () => {
  render(<Component />);
  expect(screen.getByText('Riverside Elementary')).toBeInTheDocument();
});

// DESPUÉS - Robusto
it('should display school information', () => {
  const mockData = [{ schoolName: 'Test School', /* ... */ }];
  render(<Component relationshipManagers={mockData} />);
  
  const schoolElements = screen.getAllByTestId('school-name');
  expect(schoolElements).toHaveLength(1);
  expect(schoolElements[0]).toHaveTextContent('Test School');
});
```

## Conclusión

Esta aproximación hace que las pruebas sean **más robustas y mantenibles** cuando se integren datos reales de la base de datos. Las pruebas se enfocan en verificar que la funcionalidad del componente funciona correctamente, independientemente del contenido específico de los datos.
