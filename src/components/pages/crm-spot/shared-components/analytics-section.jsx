import { Tooltip, Skeleton } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import { AnalyticsCard } from './analytics-card';
import styles from './analytics-section.module.scss';

/**
 * Analytics data structure for the CRM system
 * @typedef {Object} AnalyticsData
 * @property {number} total - Total number of leads in the system
 * @property {number} unassigned_plan - Number of unassigned leads with plan
 * @property {number} unassigned_noplan - Number of unassigned leads without plan
 * @property {number} assigned_plan - Number of assigned leads with plan
 * @property {number} assigned_noplan - Number of assigned leads without plan
 * @property {number} matched_noplan - Number of leads sent to RMs (no plan)
 * @property {number} matched_plan - Number of leads sent to partner RMs
 * @property {number} conversion_noplan - RM conversion rate percentage
 * @property {number} conversion_plan - Partner conversion rate percentage
 * @property {number} conversion_total - Overall conversion rate percentage
 */

/**
 * AnalyticsSection Component
 *
 * @component
 * @param {Object} props - Component props
 * @param {AnalyticsData} [props.analytics] - Analytics data object containing metrics
 * @param {boolean} [props.loading=false] - Loading state indicator
 *
 * @returns {JSX.Element} Analytics section with 8 metric cards or loading skeletons
 *
 */
export const AnalyticsSection = ({ analytics, loading }) => {
  if (loading) {
    return (
      <div className={styles.analyticsGrid}>
        {Array.from({ length: 8 }).map((_, index) => (
          <Skeleton key={index} block active size="large" />
        ))}
      </div>
    );
  }

  return (
    <div className={styles.analyticsGrid}>
      {/* Total Leads Card - Shows the complete count of leads in the system */}
      <Tooltip title="Total number of leads currently in the system">
        <AnalyticsCard
          title={
            <div className={styles.cardTitle}>
              Total Leads <InfoCircleOutlined />
            </div>
          }
          value={analytics?.total}
          description="Total leads in the queue"
        />
      </Tooltip>

      <Tooltip title="Leads that haven't been assigned to any team member yet">
        <AnalyticsCard
          title={
            <div className={styles.cardTitle}>
              Unassigned <InfoCircleOutlined />
            </div>
          }
          value={analytics?.unassigned_plan + analytics?.unassigned_noplan}
          description="Awaiting assignment"
        />
      </Tooltip>

      <Tooltip title="Leads currently assigned to team members">
        <AnalyticsCard
          title={
            <div className={styles.cardTitle}>
              Assigned <InfoCircleOutlined />
            </div>
          }
          value={analytics?.assigned_plan + analytics?.assigned_noplan}
          description={`<div style="display: flex; justify-content: center; align-items: center; font-size: 10px;">(Basic)<b style="color: #000000; font-weight: 700;">${analytics?.assigned_noplan}&nbsp;&nbsp;/&nbsp;&nbsp;${analytics?.assigned_plan}</b>(Partner)</div>
            <div>Currently being worked on.</div>`}
        />
      </Tooltip>

      <Tooltip title="Leads that have been successfully sent to relationship managers">
        <AnalyticsCard
          title={
            <div className={styles.cardTitle}>
              Sent to RMs <InfoCircleOutlined />
            </div>
          }
          value={analytics?.matched_noplan}
          description={`Successfully passed to RMs.`}
        />
      </Tooltip>

      <Tooltip title="Leads that have been passed to partner relationship managers">
        <AnalyticsCard
          title={
            <div className={styles.cardTitle}>
              To Partner RMs <InfoCircleOutlined />
            </div>
          }
          value={analytics?.matched_plan}
          description="Passed to partner RMs"
        />
      </Tooltip>

      <Tooltip title="Percentage of leads successfully converted to RM assignments">
        <AnalyticsCard
          title={
            <div className={styles.cardTitle}>
              RM Conversion <InfoCircleOutlined />
            </div>
          }
          value={`${analytics?.conversion_noplan}%`}
          description="Lead-to-RM conversion rate"
        />
      </Tooltip>

      <Tooltip title="Percentage of leads successfully converted to partner assignments">
        <AnalyticsCard
          title={
            <div className={styles.cardTitle}>
              Partner Conversion <InfoCircleOutlined />
            </div>
          }
          value={`${analytics?.conversion_plan}%`}
          description="Lead-to-Partner conversion"
        />
      </Tooltip>

      <Tooltip title="Overall conversion performance for the current month">
        <AnalyticsCard
          title={
            <div className={styles.cardTitle}>
              MTD Performance <InfoCircleOutlined />
            </div>
          }
          value={`${analytics?.conversion_total}%`}
          description="Month-to-date conversion"
        />
      </Tooltip>
    </div>
  );
};
