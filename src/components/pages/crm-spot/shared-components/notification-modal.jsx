import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Modal, Button, Badge, Ta<PERSON>, Flex } from 'antd';
import {
  CheckOutlined,
  DeleteOutlined,
  CloseOutlined,
  InfoCircleOutlined,
  ClockCircleOutlined,
  BellOutlined,
} from '@ant-design/icons';
import { format } from 'date-fns';
import styles from './notification-modal.module.scss';
import { getTimeAgo } from 'util/time-ago';

const NotificationItem = ({ notification, handleMarkAsRead, handleDelete }) => {
  const getIcon = () => {
    switch (notification.type) {
      case 'lead_assigned':
        return <BellOutlined className={styles.leadIcon} />;
      case 'system':
        return <InfoCircleOutlined className={styles.systemIcon} />;
      case 'tasks':
        return <ClockCircleOutlined className={styles.taskIcon} />;
      default:
        return <div className={`${styles.iconCircle} ${styles.leadIcon}`} />;
    }
  };

  return (
    <div className={`${styles.notificationItem} ${notification.read_at ? '' : styles.unread}`}>
      <div className={`${styles.notificationContent} ${notification.type}`}>
        {getIcon()}
        <div className={styles.notificationDetails}>
          <div className={styles.notificationHeader}>
            <div className={styles.titleContainer}>
              <h4>{notification.message}</h4>
              {notification.priority && (
                <span className={`${styles.priorityBadge} ${styles[notification.priority]}`}>
                  {notification.priority}
                </span>
              )}
            </div>
            <span className={styles.timeAgo}>{notification.created_at ? getTimeAgo(notification.created_at) : ''}</span>
          </div>
          <Flex justify="space-between" align="center">
            <p className={styles.timestamp}>
              {notification.created_at ? format(new Date(notification.created_at), 'MMM d, h:mm a') : ''}
            </p>
            {!notification.read_at && (
              <Button
                type="text"
                className={styles.markAsReadButton}
                icon={<CheckOutlined />}
                onClick={() => handleMarkAsRead(notification.id)}>
                Mark as read
              </Button>
            )}
            <Button
              type="text"
              className={styles.deleteButton}
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(notification.id)}
            />
          </Flex>
        </div>
      </div>
    </div>
  );
};

NotificationItem.propTypes = {
  notification: PropTypes.shape({
    id: PropTypes.number.isRequired,
    type: PropTypes.oneOf(['lead', 'system', 'task']),
    title: PropTypes.string.isRequired,
    timestamp: PropTypes.string.isRequired,
    timeAgo: PropTypes.string.isRequired,
    priority: PropTypes.string,
    canMarkAsRead: PropTypes.bool,
  }).isRequired,
  handleMarkAsRead: PropTypes.func.isRequired,
  handleDelete: PropTypes.func.isRequired,
};

/**
 * NotificationModal
 *
 * Modal that displays notifications grouped by type, with actions to mark as read or delete.
 *
 * @param {Object} props - Component properties
 * @param {boolean} props.visible - Whether the modal is visible
 * @param {function} props.onClose - Handler to close the modal
 */
export const NotificationModal = ({
  visible,
  onClose,
  notifications,
  markAsRead,
  batchNotificationEdit,
  deleteNotification,
}) => {
  const [activeTab, setActiveTab] = useState('all');

  const getFilteredNotifications = () => {
    if (activeTab === 'all') return notifications;
    return notifications.filter((notification) => notification.type === activeTab);
  };

  const getCounts = () => {
    const counts = {
      all: notifications.filter((n) => !n.read_at).length,
      leads: notifications.filter((n) => n.type === 'lead_assigned' && !n.read_at).length,
      tasks: notifications.filter((n) => n.type === 'task_reminder' && !n.read_at).length,
      system: notifications.filter((n) => n.type === 'system' && !n.read_at).length,
    };
    return counts;
  };

  const handleMarkAsRead = async (id) => {
    await markAsRead(id);
  };

  const handleDelete = async (id) => {
    await deleteNotification(id);
  };

  const getIdsAvailable = (key) => {
    return notifications.filter((notification) => !notification[key]).map((notification) => notification.id);
  };

  const handleBatchEdit = async (key) => {
    const ids = getIdsAvailable(key);

    if (ids.length === 0) return;
    await batchNotificationEdit(ids, key);
  };

  const counts = getCounts();
  const items = [
    {
      key: 'all',
      label: (
        <span>
          All <Badge count={counts.all} className={styles.allBadge} />
        </span>
      ),
    },
    {
      key: 'lead_assigned',
      label: (
        <span>
          Leads <Badge count={counts.leads} className={styles.leadBadge} />
        </span>
      ),
    },
    {
      key: 'task_reminder',
      label: (
        <span>
          Tasks <Badge count={counts.tasks} className={styles.taskBadge} />
        </span>
      ),
    },
    {
      key: 'system',
      label: (
        <span>
          System <Badge count={counts.system} className={styles.systemBadge} />
        </span>
      ),
    },
  ];

  return (
    <Modal
      title={
        <div className={styles.modalHeader}>
          <div className={styles.titleWithBadge}>
            <span>Notifications</span>
          </div>
          <div className={styles.headerActions}>
            {getIdsAvailable('read_at').length > 0 && (
              <Button
                icon={<CheckOutlined />}
                onClick={() => handleBatchEdit('read_at')}
                type="text"
                className={styles.headerButton}>
                Mark all as read
              </Button>
            )}
            {getIdsAvailable('deleted_at').length > 0 && (
              <Button
                icon={<DeleteOutlined />}
                onClick={() => handleBatchEdit('deleted_at')}
                type="text"
                className={styles.headerButton}>
                Delete all
              </Button>
            )}
            <Button type="text" icon={<CloseOutlined />} onClick={onClose} className={styles.closeButton} />
          </div>
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      className={styles.notificationModal}
      closable={false}>
      <Tabs activeKey={activeTab} onChange={setActiveTab} items={items} className={styles.tabs} centered type="card" />
      <div className={styles.notificationList}>
        {getFilteredNotifications().length > 0 ? (
          getFilteredNotifications().map((notification) => (
            <NotificationItem
              key={notification.id}
              notification={notification}
              handleMarkAsRead={handleMarkAsRead}
              handleDelete={handleDelete}
            />
          ))
        ) : (
          <div className={styles.emptyState}>No notifications</div>
        )}
      </div>
    </Modal>
  );
};

NotificationModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
};
