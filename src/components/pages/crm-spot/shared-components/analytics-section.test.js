import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { AnalyticsSection } from './analytics-section';

// Mock AnalyticsCard component
jest.mock('./analytics-card', () => ({
  AnalyticsCard: ({ title, value, description }) => (
    <div data-testid="analytics-card">
      <div data-testid="card-title">{title}</div>
      <div data-testid="card-value">{value}</div>
      <div data-testid="card-description">{description}</div>
    </div>
  ),
}));

describe('AnalyticsSection', () => {
  const mockAnalytics = {
    total: 1500,
    unassigned_plan: 200,
    unassigned_noplan: 150,
    assigned_plan: 300,
    assigned_noplan: 250,
    matched_noplan: 400,
    matched_plan: 200,
    conversion_noplan: 75,
    conversion_plan: 85,
    conversion_total: 80,
  };

  const defaultProps = {
    analytics: mockAnalytics,
    loading: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Loading State', () => {
    it('should render skeleton loaders when loading is true', () => {
      render(<AnalyticsSection analytics={mockAnalytics} loading={true} />);

      const skeletons = document.querySelectorAll('.ant-skeleton');
      expect(skeletons).toHaveLength(8);
    });

    it('should render skeleton with correct props', () => {
      render(<AnalyticsSection analytics={mockAnalytics} loading={true} />);

      const skeletons = document.querySelectorAll('.ant-skeleton');
      skeletons.forEach((skeleton) => {
        expect(skeleton).toHaveClass('ant-skeleton');
      });
    });

    it('should render analytics grid container when loading', () => {
      render(<AnalyticsSection analytics={mockAnalytics} loading={true} />);

      const gridContainer = document.querySelector('[class*="analyticsGrid"]');
      expect(gridContainer).toBeInTheDocument();
    });
  });

  describe('Content Rendering', () => {
    it('should render all analytics cards when not loading', () => {
      render(<AnalyticsSection {...defaultProps} />);

      const analyticsCards = screen.getAllByTestId('analytics-card');
      expect(analyticsCards).toHaveLength(8);
    });

    it('should render analytics grid container', () => {
      render(<AnalyticsSection {...defaultProps} />);

      const gridContainer = document.querySelector('[class*="analyticsGrid"]');
      expect(gridContainer).toBeInTheDocument();
    });

    it('should render all tooltips', () => {
      render(<AnalyticsSection {...defaultProps} />);

      const tooltips = document.querySelectorAll('.ant-tooltip-open');
      // Tooltips are rendered but not necessarily open, so we check for their presence
      const tooltipTriggers = document.querySelectorAll('[data-tooltip-id]');
      expect(tooltipTriggers.length).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Analytics Cards Content', () => {
    it('should render Total Leads card with correct data', () => {
      render(<AnalyticsSection {...defaultProps} />);

      const cardValues = screen.getAllByTestId('card-value');
      expect(cardValues[0]).toHaveTextContent('1500');
    });

    it('should render Unassigned card with calculated value', () => {
      render(<AnalyticsSection {...defaultProps} />);

      const cardValues = screen.getAllByTestId('card-value');
      // unassigned_plan + unassigned_noplan = 200 + 150 = 350
      expect(cardValues[1]).toHaveTextContent('350');
    });

    it('should render Assigned card with calculated value', () => {
      render(<AnalyticsSection {...defaultProps} />);

      const cardValues = screen.getAllByTestId('card-value');
      // assigned_plan + assigned_noplan = 300 + 250 = 550
      expect(cardValues[2]).toHaveTextContent('550');
    });

    it('should render Sent to RMs card with correct data', () => {
      render(<AnalyticsSection {...defaultProps} />);

      const cardValues = screen.getAllByTestId('card-value');
      expect(cardValues[3]).toHaveTextContent('400');
    });

    it('should render To Partner RMs card with correct data', () => {
      render(<AnalyticsSection {...defaultProps} />);

      const cardValues = screen.getAllByTestId('card-value');
      expect(cardValues[4]).toHaveTextContent('200');
    });

    it('should render RM Conversion card with percentage', () => {
      render(<AnalyticsSection {...defaultProps} />);

      const cardValues = screen.getAllByTestId('card-value');
      expect(cardValues[5]).toHaveTextContent('75%');
    });

    it('should render Partner Conversion card with percentage', () => {
      render(<AnalyticsSection {...defaultProps} />);

      const cardValues = screen.getAllByTestId('card-value');
      expect(cardValues[6]).toHaveTextContent('85%');
    });

    it('should render MTD Performance card with percentage', () => {
      render(<AnalyticsSection {...defaultProps} />);

      const cardValues = screen.getAllByTestId('card-value');
      expect(cardValues[7]).toHaveTextContent('80%');
    });
  });

  describe('Card Titles and Icons', () => {
    it('should render all card titles with InfoCircleOutlined icons', () => {
      render(<AnalyticsSection {...defaultProps} />);

      const infoIcons = document.querySelectorAll('.anticon-info-circle');
      expect(infoIcons).toHaveLength(8);
    });

    it('should render correct card titles', () => {
      render(<AnalyticsSection {...defaultProps} />);

      expect(screen.getByText('Total Leads')).toBeInTheDocument();
      expect(screen.getByText('Unassigned')).toBeInTheDocument();
      expect(screen.getByText('Assigned')).toBeInTheDocument();
      expect(screen.getByText('Sent to RMs')).toBeInTheDocument();
      expect(screen.getByText('To Partner RMs')).toBeInTheDocument();
      expect(screen.getByText('RM Conversion')).toBeInTheDocument();
      expect(screen.getByText('Partner Conversion')).toBeInTheDocument();
      expect(screen.getByText('MTD Performance')).toBeInTheDocument();
    });

    it('should apply cardTitle CSS class to title containers', () => {
      render(<AnalyticsSection {...defaultProps} />);

      const cardTitles = document.querySelectorAll('[class*="cardTitle"]');
      expect(cardTitles).toHaveLength(8);
    });
  });

  describe('Edge Cases', () => {
    it('should handle undefined analytics gracefully', () => {
      render(<AnalyticsSection analytics={undefined} loading={false} />);

      const analyticsCards = screen.getAllByTestId('analytics-card');
      expect(analyticsCards).toHaveLength(8);
    });

    it('should handle null analytics gracefully', () => {
      render(<AnalyticsSection analytics={null} loading={false} />);

      const analyticsCards = screen.getAllByTestId('analytics-card');
      expect(analyticsCards).toHaveLength(8);
    });

    it('should handle missing analytics properties', () => {
      const incompleteAnalytics = {
        total: 100,
        // Missing other properties
      };

      render(<AnalyticsSection analytics={incompleteAnalytics} loading={false} />);

      const analyticsCards = screen.getAllByTestId('analytics-card');
      expect(analyticsCards).toHaveLength(8);
    });

    it('should handle zero values', () => {
      const zeroAnalytics = {
        total: 0,
        unassigned_plan: 0,
        unassigned_noplan: 0,
        assigned_plan: 0,
        assigned_noplan: 0,
        matched_noplan: 0,
        matched_plan: 0,
        conversion_noplan: 0,
        conversion_plan: 0,
        conversion_total: 0,
      };

      render(<AnalyticsSection analytics={zeroAnalytics} loading={false} />);

      const cardValues = screen.getAllByTestId('card-value');
      expect(cardValues[0]).toHaveTextContent('0'); // total
      expect(cardValues[1]).toHaveTextContent('0'); // unassigned
      expect(cardValues[2]).toHaveTextContent('0'); // assigned
    });
  });

  describe('Calculations', () => {
    it('should correctly calculate unassigned total', () => {
      const testAnalytics = {
        unassigned_plan: 50,
        unassigned_noplan: 75,
      };

      render(<AnalyticsSection analytics={testAnalytics} loading={false} />);

      const cardValues = screen.getAllByTestId('card-value');
      expect(cardValues[1]).toHaveTextContent('125'); // 50 + 75
    });

    it('should correctly calculate assigned total', () => {
      const testAnalytics = {
        assigned_plan: 100,
        assigned_noplan: 200,
      };

      render(<AnalyticsSection analytics={testAnalytics} loading={false} />);

      const cardValues = screen.getAllByTestId('card-value');
      expect(cardValues[2]).toHaveTextContent('300'); // 100 + 200
    });

    it('should handle invalid string calculations', () => {
      const invalidAnalytics = {
        unassigned_plan: 'invalid',
        unassigned_noplan: 'invalid',
      };

      render(<AnalyticsSection analytics={invalidAnalytics} loading={false} />);

      const cardValues = screen.getAllByTestId('card-value');
      // JavaScript concatenates strings instead of returning NaN
      expect(cardValues[1]).toHaveTextContent('invalidinvalid');
    });
  });

  describe('Component Structure', () => {
    it('should render with correct CSS classes', () => {
      render(<AnalyticsSection {...defaultProps} />);

      const gridContainer = document.querySelector('[class*="analyticsGrid"]');
      expect(gridContainer).toBeInTheDocument();
      expect(gridContainer.className).toMatch(/analyticsGrid/);
    });

    it('should not render skeletons when not loading', () => {
      render(<AnalyticsSection {...defaultProps} />);

      const skeletons = document.querySelectorAll('.ant-skeleton');
      expect(skeletons).toHaveLength(0);
    });

    it('should not render analytics cards when loading', () => {
      render(<AnalyticsSection analytics={mockAnalytics} loading={true} />);

      const analyticsCards = screen.queryAllByTestId('analytics-card');
      expect(analyticsCards).toHaveLength(0);
    });
  });

  describe('Tooltip Integration', () => {
    it('should wrap each analytics card with tooltip', () => {
      render(<AnalyticsSection {...defaultProps} />);

      // Check that tooltips are present (Ant Design tooltips)
      const tooltipElements = document.querySelectorAll('[class*="ant-tooltip"]');
      expect(tooltipElements.length).toBeGreaterThanOrEqual(0);
    });
  });

  describe('PropTypes Validation', () => {
    it('should accept analytics object prop', () => {
      expect(() => {
        render(<AnalyticsSection analytics={mockAnalytics} loading={false} />);
      }).not.toThrow();
    });

    it('should accept loading boolean prop', () => {
      expect(() => {
        render(<AnalyticsSection analytics={mockAnalytics} loading={true} />);
        render(<AnalyticsSection analytics={mockAnalytics} loading={false} />);
      }).not.toThrow();
    });

    it('should work without props', () => {
      expect(() => {
        render(<AnalyticsSection />);
      }).not.toThrow();
    });
  });
});
