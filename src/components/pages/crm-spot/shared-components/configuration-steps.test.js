import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ConfigurationSteps } from './configuration-steps';

describe('ConfigurationSteps', () => {
  const mockSteps = [
    { id: '1', label: 'Step 1', isCompleted: true },
    { id: '2', label: 'Step 2', isCompleted: false },
    { id: '3', label: 'Step 3', isCompleted: true },
  ];

  const defaultProps = {
    steps: mockSteps,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render the component with default props', () => {
      render(<ConfigurationSteps />);

      const container = document.querySelector('[class*="stepsContainer"]');
      expect(container).toBeInTheDocument();
    });

    it('should render all steps', () => {
      render(<ConfigurationSteps {...defaultProps} />);

      expect(screen.getByText('Step 1')).toBeInTheDocument();
      expect(screen.getByText('Step 2')).toBeInTheDocument();
      expect(screen.getByText('Step 3')).toBeInTheDocument();
    });

    it('should render with custom className', () => {
      render(<ConfigurationSteps {...defaultProps} className="custom-class" />);

      const container = document.querySelector('[class*="stepsContainer"]');
      expect(container).toHaveClass('custom-class');
    });

    it('should render empty container when no steps provided', () => {
      render(<ConfigurationSteps steps={[]} />);

      const container = document.querySelector('[class*="stepsContainer"]');
      expect(container).toBeInTheDocument();
      expect(container).toBeEmptyDOMElement();
    });
  });

  describe('Step Indicators', () => {
    it('should render step indicators for all steps', () => {
      render(<ConfigurationSteps {...defaultProps} />);

      const indicators = document.querySelectorAll('[class*="stepIndicator"]');
      expect(indicators).toHaveLength(3);
    });

    it('should show check icon for completed steps', () => {
      render(<ConfigurationSteps {...defaultProps} />);

      const checkIcons = document.querySelectorAll('.anticon-check-circle');
      expect(checkIcons).toHaveLength(2); // Steps 1 and 3 are completed
    });

    it('should not show check icon for incomplete steps', () => {
      const incompleteSteps = [
        { id: '1', label: 'Step 1', isCompleted: false },
        { id: '2', label: 'Step 2', isCompleted: false },
      ];

      render(<ConfigurationSteps steps={incompleteSteps} />);

      const checkIcons = document.querySelectorAll('.anticon-check-circle');
      expect(checkIcons).toHaveLength(0);
    });

    it('should apply completed class to completed step indicators', () => {
      render(<ConfigurationSteps {...defaultProps} />);

      const indicators = document.querySelectorAll('[class*="stepIndicator"]');
      expect(indicators[0]).toHaveClass('completed'); // Step 1 is completed
      expect(indicators[1]).not.toHaveClass('completed'); // Step 2 is not completed
      expect(indicators[2]).toHaveClass('completed'); // Step 3 is completed
    });
  });

  describe('Step Labels', () => {
    it('should render all step labels', () => {
      render(<ConfigurationSteps {...defaultProps} />);

      const labels = document.querySelectorAll('[class*="stepLabel"]');
      expect(labels).toHaveLength(3);
      expect(labels[0]).toHaveTextContent('Step 1');
      expect(labels[1]).toHaveTextContent('Step 2');
      expect(labels[2]).toHaveTextContent('Step 3');
    });

    it('should handle empty labels', () => {
      const stepsWithEmptyLabel = [{ id: '1', label: '', isCompleted: true }];

      render(<ConfigurationSteps steps={stepsWithEmptyLabel} />);

      const label = document.querySelector('[class*="stepLabel"]');
      expect(label).toBeInTheDocument();
      expect(label).toHaveTextContent('');
    });

    it('should handle long labels', () => {
      const stepsWithLongLabel = [
        { id: '1', label: 'This is a very long step label that might wrap', isCompleted: true },
      ];

      render(<ConfigurationSteps steps={stepsWithLongLabel} />);

      const label = document.querySelector('[class*="stepLabel"]');
      expect(label).toHaveTextContent('This is a very long step label that might wrap');
    });
  });

  describe('CSS Classes and Structure', () => {
    it('should apply correct CSS classes', () => {
      render(<ConfigurationSteps {...defaultProps} />);

      expect(document.querySelector('[class*="stepsContainer"]')).toBeInTheDocument();
      expect(document.querySelectorAll('[class*="stepItem"]')).toHaveLength(3);
      expect(document.querySelectorAll('[class*="stepIndicator"]')).toHaveLength(3);
      expect(document.querySelectorAll('[class*="stepLabel"]')).toHaveLength(3);
    });

    it('should have correct DOM structure', () => {
      render(<ConfigurationSteps {...defaultProps} />);

      const container = document.querySelector('[class*="stepsContainer"]');
      const stepItems = document.querySelectorAll('[class*="stepItem"]');

      expect(container).toContainElement(stepItems[0]);
      expect(container).toContainElement(stepItems[1]);
      expect(container).toContainElement(stepItems[2]);

      stepItems.forEach((stepItem) => {
        const indicator = stepItem.querySelector('[class*="stepIndicator"]');
        const label = stepItem.querySelector('[class*="stepLabel"]');
        expect(stepItem).toContainElement(indicator);
        expect(stepItem).toContainElement(label);
      });
    });

    it('should combine custom className with default classes', () => {
      render(<ConfigurationSteps {...defaultProps} className="custom-steps" />);

      const container = document.querySelector('[class*="stepsContainer"]');
      expect(container.className).toMatch(/stepsContainer/);
      expect(container).toHaveClass('custom-steps');
    });
  });

  describe('PropTypes Validation', () => {
    it('should accept array of steps', () => {
      expect(() => {
        render(<ConfigurationSteps steps={mockSteps} />);
      }).not.toThrow();
    });

    it('should accept string className', () => {
      expect(() => {
        render(<ConfigurationSteps className="test-class" />);
      }).not.toThrow();
    });

    it('should work without props', () => {
      expect(() => {
        render(<ConfigurationSteps />);
      }).not.toThrow();
    });
  });

  describe('Edge Cases', () => {
    it('should handle single step', () => {
      const singleStep = [{ id: '1', label: 'Only Step', isCompleted: true }];

      render(<ConfigurationSteps steps={singleStep} />);

      expect(screen.getByText('Only Step')).toBeInTheDocument();
      const indicators = document.querySelectorAll('[class*="stepIndicator"]');
      expect(indicators).toHaveLength(1);
      expect(indicators[0]).toHaveClass('completed');
    });

    it('should handle all completed steps', () => {
      const allCompletedSteps = [
        { id: '1', label: 'Step 1', isCompleted: true },
        { id: '2', label: 'Step 2', isCompleted: true },
        { id: '3', label: 'Step 3', isCompleted: true },
      ];

      render(<ConfigurationSteps steps={allCompletedSteps} />);

      const checkIcons = document.querySelectorAll('.anticon-check-circle');
      expect(checkIcons).toHaveLength(3);

      const indicators = document.querySelectorAll('[class*="stepIndicator"]');
      indicators.forEach((indicator) => {
        expect(indicator).toHaveClass('completed');
      });
    });

    it('should handle all incomplete steps', () => {
      const allIncompleteSteps = [
        { id: '1', label: 'Step 1', isCompleted: false },
        { id: '2', label: 'Step 2', isCompleted: false },
        { id: '3', label: 'Step 3', isCompleted: false },
      ];

      render(<ConfigurationSteps steps={allIncompleteSteps} />);

      const checkIcons = document.querySelectorAll('.anticon-check-circle');
      expect(checkIcons).toHaveLength(0);

      const indicators = document.querySelectorAll('[class*="stepIndicator"]');
      indicators.forEach((indicator) => {
        expect(indicator).not.toHaveClass('completed');
      });
    });

    it('should handle steps with special characters in labels', () => {
      const specialCharSteps = [
        { id: '1', label: 'Step & Configuration', isCompleted: true },
        { id: '2', label: 'Step <2>', isCompleted: false },
        { id: '3', label: 'Step "3"', isCompleted: true },
      ];

      render(<ConfigurationSteps steps={specialCharSteps} />);

      expect(screen.getByText('Step & Configuration')).toBeInTheDocument();
      expect(screen.getByText('Step <2>')).toBeInTheDocument();
      expect(screen.getByText('Step "3"')).toBeInTheDocument();
    });

    it('should handle numeric IDs', () => {
      const numericIdSteps = [
        { id: '1', label: 'Step 1', isCompleted: true },
        { id: '2', label: 'Step 2', isCompleted: false },
      ];

      render(<ConfigurationSteps steps={numericIdSteps} />);

      const stepItems = document.querySelectorAll('[class*="stepItem"]');
      expect(stepItems).toHaveLength(2);
    });

    it('should handle undefined className', () => {
      render(<ConfigurationSteps steps={mockSteps} className={undefined} />);

      const container = document.querySelector('[class*="stepsContainer"]');
      expect(container).toBeInTheDocument();
    });

    it('should handle steps array with invalid structure gracefully', () => {
      // Test with steps that have missing required properties
      const invalidSteps = [
        { id: '1', label: 'Valid Step', isCompleted: true },
        { id: '2' }, // Missing label and isCompleted
      ];

      expect(() => {
        render(<ConfigurationSteps steps={invalidSteps} />);
      }).not.toThrow();
    });
  });

  describe('Default Props', () => {
    it('should use empty array as default for steps', () => {
      render(<ConfigurationSteps />);

      const stepItems = document.querySelectorAll('[class*="stepItem"]');
      expect(stepItems).toHaveLength(0);
    });

    it('should use empty string as default for className', () => {
      render(<ConfigurationSteps />);

      const container = document.querySelector('[class*="stepsContainer"]');
      expect(container.className).not.toContain('undefined');
    });
  });

  describe('Step Completion States', () => {
    it('should correctly display mixed completion states', () => {
      const mixedSteps = [
        { id: '1', label: 'Completed Step', isCompleted: true },
        { id: '2', label: 'Incomplete Step', isCompleted: false },
        { id: '3', label: 'Another Completed', isCompleted: true },
        { id: '4', label: 'Another Incomplete', isCompleted: false },
      ];

      render(<ConfigurationSteps steps={mixedSteps} />);

      const indicators = document.querySelectorAll('[class*="stepIndicator"]');
      expect(indicators[0]).toHaveClass('completed');
      expect(indicators[1]).not.toHaveClass('completed');
      expect(indicators[2]).toHaveClass('completed');
      expect(indicators[3]).not.toHaveClass('completed');

      const checkIcons = document.querySelectorAll('.anticon-check-circle');
      expect(checkIcons).toHaveLength(2);
    });

    it('should handle boolean completion values correctly', () => {
      const booleanSteps = [
        { id: '1', label: 'True Step', isCompleted: true },
        { id: '2', label: 'False Step', isCompleted: false },
      ];

      render(<ConfigurationSteps steps={booleanSteps} />);

      const indicators = document.querySelectorAll('[class*="stepIndicator"]');
      expect(indicators[0]).toHaveClass('completed');
      expect(indicators[1]).not.toHaveClass('completed');
    });
  });

  describe('Accessibility', () => {
    it('should render semantic structure', () => {
      render(<ConfigurationSteps {...defaultProps} />);

      const container = document.querySelector('[class*="stepsContainer"]');
      expect(container).toBeInTheDocument();

      const stepItems = document.querySelectorAll('[class*="stepItem"]');
      stepItems.forEach((stepItem) => {
        expect(stepItem).toBeInTheDocument();
      });
    });

    it('should have proper text content for screen readers', () => {
      render(<ConfigurationSteps {...defaultProps} />);

      expect(screen.getByText('Step 1')).toBeInTheDocument();
      expect(screen.getByText('Step 2')).toBeInTheDocument();
      expect(screen.getByText('Step 3')).toBeInTheDocument();
    });
  });
});
