import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { TeamBalance } from './team-balance';

// Mock the TeamBalanceCard component
jest.mock('../views/leads-management/components/team-balance-card', () => ({
  TeamBalanceCard: ({ title, Icon, onRefresh, loading, children }) => (
    <div data-testid="team-balance-card">
      <div data-testid="card-header">
        {Icon && <Icon data-testid="card-icon" />}
        <h3 data-testid="card-title">{title}</h3>
        <button data-testid="refresh-button" onClick={onRefresh} disabled={loading} data-loading={loading}>
          Refresh
        </button>
      </div>
      <div data-testid="card-content">{children}</div>
    </div>
  ),
}));

// Mock Ant Design icons
jest.mock('@ant-design/icons', () => ({
  BarChartOutlined: (props) => <span data-testid="bar-chart-icon" {...props} />,
}));

// Mo<PERSON> styles
jest.mock('./team-balance.module.scss', () => ({
  empty_state: 'empty_state',
  member_row: 'member_row',
  member_name: 'member_name',
  bar_container: 'bar_container',
  bar_filled: 'bar_filled',
  leads_count: 'leads_count',
}));

describe('TeamBalance Component', () => {
  const mockFetchTeamBalance = jest.fn();

  const defaultProps = {
    teamBalance: [],
    fetchTeamBalance: mockFetchTeamBalance,
    loading: false,
  };

  const mockTeamData = [
    {
      first_name: 'John',
      last_name: 'Doe',
      total: 15,
    },
    {
      first_name: 'Jane',
      last_name: 'Smith',
      total: 8,
    },
    {
      first_name: 'Bob',
      last_name: 'Johnson',
      total: 20,
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    test('renders TeamBalanceCard with correct props', () => {
      render(<TeamBalance {...defaultProps} />);

      expect(screen.getByTestId('team-balance-card')).toBeInTheDocument();
      expect(screen.getByTestId('card-title')).toHaveTextContent('Current Team Balance');
      expect(screen.getByTestId('card-icon')).toBeInTheDocument();
    });

    test('passes loading state to TeamBalanceCard', () => {
      render(<TeamBalance {...defaultProps} loading />);

      const refreshButton = screen.getByTestId('refresh-button');
      expect(refreshButton).toHaveAttribute('data-loading', 'true');
    });

    test('passes fetchTeamBalance function to onRefresh', () => {
      render(<TeamBalance {...defaultProps} />);

      const refreshButton = screen.getByTestId('refresh-button');
      fireEvent.click(refreshButton);

      expect(mockFetchTeamBalance).toHaveBeenCalledTimes(1);
    });
  });

  describe('Empty State', () => {
    test('displays empty state when teamBalance is null', () => {
      render(<TeamBalance {...defaultProps} teamBalance={null} />);

      expect(screen.getByText('No team balance data available.')).toBeInTheDocument();
    });

    test('displays empty state when teamBalance is undefined', () => {
      render(<TeamBalance {...defaultProps} teamBalance={undefined} />);

      expect(screen.getByText('No team balance data available.')).toBeInTheDocument();
    });

    test('displays empty state when teamBalance is empty array', () => {
      render(<TeamBalance {...defaultProps} teamBalance={[]} />);

      expect(screen.getByText('No team balance data available.')).toBeInTheDocument();
    });

    test('applies correct CSS class to empty state', () => {
      render(<TeamBalance {...defaultProps} teamBalance={[]} />);

      const emptyState = screen.getByText('No team balance data available.');
      expect(emptyState).toHaveClass('empty_state');
    });
  });

  describe('Team Members Display', () => {
    test('renders team members when data is available', () => {
      render(<TeamBalance {...defaultProps} teamBalance={mockTeamData} />);

      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      expect(screen.getByText('Bob Johnson')).toBeInTheDocument();
    });

    test('displays correct lead counts', () => {
      render(<TeamBalance {...defaultProps} teamBalance={mockTeamData} />);

      expect(screen.getByText('15 leads')).toBeInTheDocument();
      expect(screen.getByText('8 leads')).toBeInTheDocument();
      expect(screen.getByText('20 leads')).toBeInTheDocument();
    });

    test('applies correct CSS classes to member elements', () => {
      render(<TeamBalance {...defaultProps} teamBalance={mockTeamData} />);

      const memberRows = document.querySelectorAll('.member_row');
      expect(memberRows).toHaveLength(3);

      const memberNames = document.querySelectorAll('.member_name');
      expect(memberNames).toHaveLength(3);

      const barContainers = document.querySelectorAll('.bar_container');
      expect(barContainers).toHaveLength(3);

      const leadsCount = document.querySelectorAll('.leads_count');
      expect(leadsCount).toHaveLength(3);
    });

    test('calculates correct bar widths based on lead count', () => {
      render(<TeamBalance {...defaultProps} teamBalance={mockTeamData} />);

      const barsFilled = document.querySelectorAll('.bar_filled');

      // John: 15/20 * 100 = 75%
      expect(barsFilled[0]).toHaveStyle({ width: '75%' });

      // Jane: 8/20 * 100 = 40%
      expect(barsFilled[1]).toHaveStyle({ width: '40%' });

      // Bob: 20/20 * 100 = 100%
      expect(barsFilled[2]).toHaveStyle({ width: '100%' });
    });
  });

  describe('Edge Cases', () => {
    test('handles members with null total', () => {
      const dataWithNullTotal = [
        {
          first_name: 'John',
          last_name: 'Doe',
          total: null,
        },
      ];

      render(<TeamBalance {...defaultProps} teamBalance={dataWithNullTotal} />);

      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('0 leads')).toBeInTheDocument();

      const barFilled = document.querySelector('.bar_filled');
      expect(barFilled).toHaveStyle({ width: '0%' });
    });

    test('handles members with undefined total', () => {
      const dataWithUndefinedTotal = [
        {
          first_name: 'Jane',
          last_name: 'Smith',
          // total is undefined
        },
      ];

      render(<TeamBalance {...defaultProps} teamBalance={dataWithUndefinedTotal} />);

      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      expect(screen.getByText('0 leads')).toBeInTheDocument();

      const barFilled = document.querySelector('.bar_filled');
      expect(barFilled).toHaveStyle({ width: '0%' });
    });

    test('handles members with zero total', () => {
      const dataWithZeroTotal = [
        {
          first_name: 'Bob',
          last_name: 'Johnson',
          total: 0,
        },
      ];

      render(<TeamBalance {...defaultProps} teamBalance={dataWithZeroTotal} />);

      expect(screen.getByText('Bob Johnson')).toBeInTheDocument();
      expect(screen.getByText('0 leads')).toBeInTheDocument();

      const barFilled = document.querySelector('.bar_filled');
      expect(barFilled).toHaveStyle({ width: '0%' });
    });

    test('handles members with very high lead counts', () => {
      const dataWithHighTotal = [
        {
          first_name: 'Super',
          last_name: 'Agent',
          total: 50,
        },
      ];

      render(<TeamBalance {...defaultProps} teamBalance={dataWithHighTotal} />);

      expect(screen.getByText('Super Agent')).toBeInTheDocument();
      expect(screen.getByText('50 leads')).toBeInTheDocument();

      const barFilled = document.querySelector('.bar_filled');
      // 50/20 * 100 = 250% (will be capped by CSS)
      expect(barFilled).toHaveStyle({ width: '250%' });
    });

    test('handles members with missing first_name', () => {
      const dataWithMissingFirstName = [
        {
          last_name: 'Doe',
          total: 10,
        },
      ];

      render(<TeamBalance {...defaultProps} teamBalance={dataWithMissingFirstName} />);

      expect(
        screen.getByText((content, element) => {
          return element?.textContent === ' Doe';
        })
      ).toBeInTheDocument();
      expect(screen.getByText('10 leads')).toBeInTheDocument();
    });

    test('handles members with missing last_name', () => {
      const dataWithMissingLastName = [
        {
          first_name: 'John',
          total: 10,
        },
      ];

      render(<TeamBalance {...defaultProps} teamBalance={dataWithMissingLastName} />);

      expect(
        screen.getByText((content, element) => {
          return element?.textContent === 'John ';
        })
      ).toBeInTheDocument();
      expect(screen.getByText('10 leads')).toBeInTheDocument();
    });

    test('handles members with empty string names', () => {
      const dataWithEmptyNames = [
        {
          first_name: '',
          last_name: '',
          total: 5,
        },
      ];

      render(<TeamBalance {...defaultProps} teamBalance={dataWithEmptyNames} />);

      expect(
        screen.getByText((content, element) => {
          return element?.textContent === ' ';
        })
      ).toBeInTheDocument();
      expect(screen.getByText('5 leads')).toBeInTheDocument();
    });

    test('handles single member', () => {
      const singleMemberData = [mockTeamData[0]];

      render(<TeamBalance {...defaultProps} teamBalance={singleMemberData} />);

      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('15 leads')).toBeInTheDocument();
      expect(document.querySelectorAll('.member_row')).toHaveLength(1);
    });

    test('handles large team data', () => {
      const largeTeamData = Array.from({ length: 10 }, (_, index) => ({
        first_name: `Member${index + 1}`,
        last_name: `Last${index + 1}`,
        total: index + 1,
      }));

      render(<TeamBalance {...defaultProps} teamBalance={largeTeamData} />);

      expect(document.querySelectorAll('.member_row')).toHaveLength(10);
      expect(screen.getByText('Member1 Last1')).toBeInTheDocument();
      expect(screen.getByText('Member10 Last10')).toBeInTheDocument();
    });
  });

  describe('Refresh Functionality', () => {
    test('calls fetchTeamBalance when refresh button is clicked', () => {
      render(<TeamBalance {...defaultProps} teamBalance={mockTeamData} />);

      const refreshButton = screen.getByTestId('refresh-button');
      fireEvent.click(refreshButton);

      expect(mockFetchTeamBalance).toHaveBeenCalledTimes(1);
    });

    test('refresh button is disabled when loading', () => {
      render(<TeamBalance {...defaultProps} loading />);

      const refreshButton = screen.getByTestId('refresh-button');
      expect(refreshButton).toBeDisabled();
    });

    test('refresh button is enabled when not loading', () => {
      render(<TeamBalance {...defaultProps} loading={false} />);

      const refreshButton = screen.getByTestId('refresh-button');
      expect(refreshButton).not.toBeDisabled();
    });
  });

  describe('Component Integration', () => {
    test('renders correctly with all props', () => {
      render(<TeamBalance teamBalance={mockTeamData} fetchTeamBalance={mockFetchTeamBalance} loading={false} />);

      expect(screen.getByTestId('team-balance-card')).toBeInTheDocument();
      expect(screen.getByText('Current Team Balance')).toBeInTheDocument();
      expect(screen.getByTestId('card-icon')).toBeInTheDocument();
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      expect(screen.getByText('Bob Johnson')).toBeInTheDocument();
    });

    test('updates when teamBalance prop changes', () => {
      const { rerender } = render(<TeamBalance {...defaultProps} teamBalance={[]} />);

      expect(screen.getByText('No team balance data available.')).toBeInTheDocument();

      rerender(<TeamBalance {...defaultProps} teamBalance={mockTeamData} />);

      expect(screen.queryByText('No team balance data available.')).not.toBeInTheDocument();
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
  });
});
