import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { LeadHeader } from './lead-header';

// Mock the hooks and utilities
jest.mock('../hooks/useTeamBalance', () => ({
  useTeamAnalytics: jest.fn(),
}));

jest.mock('../hooks/useAssignmentsLeads', () => ({
  useAssignmentsCount: jest.fn(),
}));

jest.mock('../utils/get-date-range', () => ({
  getDateRange: jest.fn(),
}));

jest.mock('./analytics-section', () => ({
  AnalyticsSection: ({ analytics, loading }) => (
    <div data-testid="analytics-section">
      {loading ? 'Loading analytics...' : `Analytics: ${JSON.stringify(analytics)}`}
    </div>
  ),
}));

// Mock Ant Design components
jest.mock('antd', () => ({
  ...jest.requireActual('antd'),
  Typography: {
    Title: ({ children, level, className }) => (
      <h1 className={className} data-level={level}>
        {children}
      </h1>
    ),
  },
  Button: ({ children, onClick, icon, size }) => (
    <button onClick={onClick} data-size={size} data-testid="analytics-button">
      {icon && <span data-testid="button-icon">{icon}</span>}
      {children}
    </button>
  ),
  Divider: ({ className }) => <hr className={className} data-testid="divider" />,
}));

// Mock icons
jest.mock('@ant-design/icons', () => ({
  DownOutlined: () => <span data-testid="down-icon">DownIcon</span>,
  UpOutlined: () => <span data-testid="up-icon">UpIcon</span>,
  BarChartOutlined: () => <span data-testid="bar-chart-icon">BarChartIcon</span>,
}));

// Mock styles
jest.mock('./lead-header.module.scss', () => ({
  headerContainer: 'headerContainer',
  container: 'container',
  headerRow: 'headerRow',
  leftSection: 'leftSection',
  title: 'title',
  badgeContainer: 'badgeContainer',
  badge: 'badge',
  buttonContainer: 'buttonContainer',
  divider: 'divider',
  dividerBottom: 'dividerBottom',
}));

describe('LeadHeader Component', () => {
  const mockUseTeamAnalytics = require('../hooks/useTeamBalance').useTeamAnalytics;
  const mockUseAssignmentsCount = require('../hooks/useAssignmentsLeads').useAssignmentsCount;
  const mockGetDateRange = require('../utils/get-date-range').getDateRange;

  const defaultProps = {
    user_id: 'test-user-123',
  };

  const mockAnalytics = {
    total: 150,
    assigned: 45,
    unassigned: 105,
    conversion_rate: 12.5,
  };

  const mockCounts = {
    general: 8,
    assigned: 12,
  };

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Setup default mock implementations
    mockGetDateRange.mockReturnValue({
      from: '2024-01-01 00:00',
      to: '2024-01-31 23:59',
    });

    mockUseTeamAnalytics.mockReturnValue({
      analytics: mockAnalytics,
      loading: false,
    });

    mockUseAssignmentsCount.mockReturnValue({
      counts: mockCounts,
      loading: false,
    });
  });

  describe('Rendering', () => {
    test('renders the component with correct title', () => {
      render(<LeadHeader {...defaultProps} />);
      
      expect(screen.getByText('Lead Queue & Analytics')).toBeInTheDocument();
    });

    test('renders badges with correct counts when not loading', () => {
      render(<LeadHeader {...defaultProps} />);
      
      expect(screen.getByText('8 General Leads Assigned to You')).toBeInTheDocument();
      expect(screen.getByText('12 School Leads Assigned to You')).toBeInTheDocument();
    });

    test('renders badges with loading state', () => {
      mockUseAssignmentsCount.mockReturnValue({
        counts: null,
        loading: true,
      });

      render(<LeadHeader {...defaultProps} />);
      
      expect(screen.getByText('- General Leads Assigned to You')).toBeInTheDocument();
      expect(screen.getByText('- School Leads Assigned to You')).toBeInTheDocument();
    });

    test('renders badges with zero counts when counts is null', () => {
      mockUseAssignmentsCount.mockReturnValue({
        counts: null,
        loading: false,
      });

      render(<LeadHeader {...defaultProps} />);
      
      expect(screen.getByText('0 General Leads Assigned to You')).toBeInTheDocument();
      expect(screen.getByText('0 School Leads Assigned to You')).toBeInTheDocument();
    });

    test('renders analytics button with correct icon', () => {
      render(<LeadHeader {...defaultProps} />);
      
      const button = screen.getByTestId('analytics-button');
      expect(button).toBeInTheDocument();
      expect(screen.getByTestId('bar-chart-icon')).toBeInTheDocument();
      expect(screen.getByTestId('down-icon')).toBeInTheDocument();
    });
  });

  describe('Analytics Toggle Functionality', () => {
    test('analytics section is hidden by default', () => {
      render(<LeadHeader {...defaultProps} />);
      
      expect(screen.queryByTestId('analytics-section')).not.toBeInTheDocument();
    });

    test('clicking analytics button shows analytics section', async () => {
      render(<LeadHeader {...defaultProps} />);
      
      const button = screen.getByTestId('analytics-button');
      fireEvent.click(button);
      
      await waitFor(() => {
        expect(screen.getByTestId('analytics-section')).toBeInTheDocument();
      });
    });

    test('clicking analytics button twice hides analytics section', async () => {
      render(<LeadHeader {...defaultProps} />);
      
      const button = screen.getByTestId('analytics-button');
      
      // First click - show analytics
      fireEvent.click(button);
      await waitFor(() => {
        expect(screen.getByTestId('analytics-section')).toBeInTheDocument();
      });
      
      // Second click - hide analytics
      fireEvent.click(button);
      await waitFor(() => {
        expect(screen.queryByTestId('analytics-section')).not.toBeInTheDocument();
      });
    });

    test('clicking header row toggles analytics section', async () => {
      render(<LeadHeader {...defaultProps} />);
      
      const headerRow = screen.getByText('Lead Queue & Analytics').closest('.headerRow');
      fireEvent.click(headerRow);
      
      await waitFor(() => {
        expect(screen.getByTestId('analytics-section')).toBeInTheDocument();
      });
    });

    test('button click stops propagation', async () => {
      render(<LeadHeader {...defaultProps} />);
      
      const button = screen.getByTestId('analytics-button');
      const headerRow = screen.getByText('Lead Queue & Analytics').closest('.headerRow');
      
      // Mock the stopPropagation method
      const mockStopPropagation = jest.fn();
      
      // Create a custom event with stopPropagation
      const clickEvent = new MouseEvent('click', { bubbles: true });
      clickEvent.stopPropagation = mockStopPropagation;
      
      fireEvent(button, clickEvent);
      
      expect(mockStopPropagation).toHaveBeenCalled();
    });

    test('shows up icon when analytics is open', async () => {
      render(<LeadHeader {...defaultProps} />);
      
      const button = screen.getByTestId('analytics-button');
      fireEvent.click(button);
      
      await waitFor(() => {
        expect(screen.getByTestId('up-icon')).toBeInTheDocument();
        expect(screen.queryByTestId('down-icon')).not.toBeInTheDocument();
      });
    });
  });

  describe('Analytics Section Integration', () => {
    test('passes correct props to AnalyticsSection when open', async () => {
      render(<LeadHeader {...defaultProps} />);
      
      const button = screen.getByTestId('analytics-button');
      fireEvent.click(button);
      
      await waitFor(() => {
        const analyticsSection = screen.getByTestId('analytics-section');
        expect(analyticsSection).toHaveTextContent(`Analytics: ${JSON.stringify(mockAnalytics)}`);
      });
    });

    test('shows loading state in AnalyticsSection', async () => {
      mockUseTeamAnalytics.mockReturnValue({
        analytics: null,
        loading: true,
      });

      render(<LeadHeader {...defaultProps} />);
      
      const button = screen.getByTestId('analytics-button');
      fireEvent.click(button);
      
      await waitFor(() => {
        expect(screen.getByText('Loading analytics...')).toBeInTheDocument();
      });
    });

    test('renders dividers when analytics section is open', async () => {
      render(<LeadHeader {...defaultProps} />);
      
      const button = screen.getByTestId('analytics-button');
      fireEvent.click(button);
      
      await waitFor(() => {
        const dividers = screen.getAllByTestId('divider');
        expect(dividers).toHaveLength(2);
      });
    });
  });

  describe('Hook Integration', () => {
    test('calls getDateRange with correct parameter', () => {
      render(<LeadHeader {...defaultProps} />);
      
      expect(mockGetDateRange).toHaveBeenCalledWith('month');
    });

    test('calls useTeamAnalytics with date range', () => {
      render(<LeadHeader {...defaultProps} />);
      
      expect(mockUseTeamAnalytics).toHaveBeenCalledWith({
        from: '2024-01-01 00:00',
        to: '2024-01-31 23:59',
      });
    });

    test('calls useAssignmentsCount with user_id', () => {
      render(<LeadHeader {...defaultProps} />);
      
      expect(mockUseAssignmentsCount).toHaveBeenCalledWith({
        user_id: 'test-user-123',
      });
    });
  });

  describe('Error Handling', () => {
    test('handles missing counts gracefully', () => {
      mockUseAssignmentsCount.mockReturnValue({
        counts: { general: undefined, assigned: undefined },
        loading: false,
      });

      render(<LeadHeader {...defaultProps} />);
      
      expect(screen.getByText('0 General Leads Assigned to You')).toBeInTheDocument();
      expect(screen.getByText('0 School Leads Assigned to You')).toBeInTheDocument();
    });

    test('handles null analytics data', async () => {
      mockUseTeamAnalytics.mockReturnValue({
        analytics: null,
        loading: false,
      });

      render(<LeadHeader {...defaultProps} />);
      
      const button = screen.getByTestId('analytics-button');
      fireEvent.click(button);
      
      await waitFor(() => {
        expect(screen.getByTestId('analytics-section')).toBeInTheDocument();
      });
    });
  });
});
