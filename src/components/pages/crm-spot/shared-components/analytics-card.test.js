import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { AnalyticsCard } from './analytics-card';

// Mock DOMPurify
jest.mock('dompurify', () => ({
  sanitize: jest.fn((html) => html),
}));

describe('AnalyticsCard', () => {
  const defaultProps = {
    title: 'Test Title',
    value: '123',
    description: 'Test description',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render the component with all props', () => {
      const DOMPurify = require('dompurify');
      DOMPurify.sanitize.mockReturnValue('Test description');

      render(<AnalyticsCard {...defaultProps} />);

      expect(screen.getByText('Test Title')).toBeInTheDocument();
      expect(screen.getByText('123')).toBeInTheDocument();

      // Check that DOMPurify.sanitize was called with the description
      expect(DOMPurify.sanitize).toHaveBeenCalledWith('Test description');

      // Check description via innerHTML since it uses dangerouslySetInnerHTML
      const descriptionElement = document.querySelector('[class*="description"]');
      expect(descriptionElement.innerHTML).toBe('Test description');
    });

    it('should render without description when not provided', () => {
      const propsWithoutDescription = {
        title: 'Test Title',
        value: '456',
      };

      render(<AnalyticsCard {...propsWithoutDescription} />);

      expect(screen.getByText('Test Title')).toBeInTheDocument();
      expect(screen.getByText('456')).toBeInTheDocument();

      // Description paragraph should exist but be empty
      const descriptionElement = document.querySelector('[class*="description"]');
      expect(descriptionElement).toBeInTheDocument();
      expect(descriptionElement).toBeEmptyDOMElement();
    });

    it('should render with React nodes as title and value', () => {
      const titleNode = <span data-testid="custom-title">Custom Title</span>;
      const valueNode = <div data-testid="custom-value">999</div>;

      render(<AnalyticsCard title={titleNode} value={valueNode} description="Node test" />);

      expect(screen.getByTestId('custom-title')).toBeInTheDocument();
      expect(screen.getByTestId('custom-value')).toBeInTheDocument();
      expect(screen.getByText('Custom Title')).toBeInTheDocument();
      expect(screen.getByText('999')).toBeInTheDocument();
    });
  });

  describe('HTML Sanitization', () => {
    it('should sanitize HTML content in description', () => {
      const DOMPurify = require('dompurify');
      const htmlDescription = '<script>alert("xss")</script><p>Safe content</p>';

      render(<AnalyticsCard title="Security Test" value="100" description={htmlDescription} />);

      expect(DOMPurify.sanitize).toHaveBeenCalledWith(htmlDescription);
    });

    it('should render sanitized HTML in description', () => {
      const DOMPurify = require('dompurify');
      const htmlDescription = '<strong>Bold text</strong> and <em>italic text</em>';
      const sanitizedHtml = '<strong>Bold text</strong> and <em>italic text</em>';

      DOMPurify.sanitize.mockReturnValue(sanitizedHtml);

      render(<AnalyticsCard title="HTML Test" value="200" description={htmlDescription} />);

      const descriptionElement = document.querySelector('[class*="description"]');
      expect(descriptionElement.innerHTML).toBe(sanitizedHtml);
    });

    it('should handle empty description gracefully', () => {
      render(<AnalyticsCard title="Empty Test" value="300" description="" />);

      const descriptionElement = document.querySelector('[class*="description"]');
      expect(descriptionElement).toBeInTheDocument();
      expect(descriptionElement).toBeEmptyDOMElement();
    });
  });

  describe('CSS Classes and Styling', () => {
    it('should apply correct CSS classes', () => {
      render(<AnalyticsCard {...defaultProps} />);

      // Check for main card class
      const cardElement = document.querySelector('[class*="card"]');
      expect(cardElement).toBeInTheDocument();
      expect(cardElement.className).toMatch(/card/);

      // Check for header class
      const headerElement = document.querySelector('[class*="cardHeader"]');
      expect(headerElement).toBeInTheDocument();

      // Check for title class
      const titleElement = document.querySelector('[class*="cardTitle"]');
      expect(titleElement).toBeInTheDocument();

      // Check for content class
      const contentElement = document.querySelector('[class*="cardContent"]');
      expect(contentElement).toBeInTheDocument();

      // Check for value class
      const valueElement = document.querySelector('[class*="value"]');
      expect(valueElement).toBeInTheDocument();

      // Check for description class
      const descriptionElement = document.querySelector('[class*="description"]');
      expect(descriptionElement).toBeInTheDocument();
    });

    it('should render Ant Design Card component', () => {
      render(<AnalyticsCard {...defaultProps} />);

      // Check for Ant Design Card class
      const antCardElement = document.querySelector('.ant-card');
      expect(antCardElement).toBeInTheDocument();
    });

    it('should render Ant Design Divider component', () => {
      render(<AnalyticsCard {...defaultProps} />);

      // Check for Ant Design Divider class
      const antDividerElement = document.querySelector('.ant-divider');
      expect(antDividerElement).toBeInTheDocument();
    });
  });

  describe('Content Structure', () => {
    it('should have correct DOM structure', () => {
      render(<AnalyticsCard {...defaultProps} />);

      const cardElement = document.querySelector('[class*="card"]');
      const headerElement = document.querySelector('[class*="cardHeader"]');
      const dividerElement = document.querySelector('.ant-divider');
      const contentElement = document.querySelector('[class*="cardContent"]');

      // Check hierarchy
      expect(cardElement).toContainElement(headerElement);
      expect(cardElement).toContainElement(dividerElement);
      expect(cardElement).toContainElement(contentElement);

      // Check that header comes before divider, and divider comes before content
      // Since Ant Design Card wraps content in ant-card-body, we need to check that
      const cardBodyElement = document.querySelector('.ant-card-body');
      const cardBodyChildren = Array.from(cardBodyElement.children);
      const headerIndex = cardBodyChildren.findIndex((child) => child === headerElement);
      const dividerIndex = cardBodyChildren.findIndex((child) => child === dividerElement);
      const contentIndex = cardBodyChildren.findIndex((child) => child === contentElement);

      expect(headerIndex).toBeLessThan(dividerIndex);
      expect(dividerIndex).toBeLessThan(contentIndex);
    });

    it('should contain value and description in content section', () => {
      render(<AnalyticsCard {...defaultProps} />);

      const contentElement = document.querySelector('[class*="cardContent"]');
      const valueElement = document.querySelector('[class*="value"]');
      const descriptionElement = document.querySelector('[class*="description"]');

      expect(contentElement).toContainElement(valueElement);
      expect(contentElement).toContainElement(descriptionElement);
    });
  });

  describe('PropTypes Validation', () => {
    it('should accept string title and value', () => {
      expect(() => {
        render(<AnalyticsCard title="String Title" value="String Value" description="String Description" />);
      }).not.toThrow();
    });

    it('should accept React node title and value', () => {
      const titleNode = <span>Node Title</span>;
      const valueNode = <div>Node Value</div>;

      expect(() => {
        render(<AnalyticsCard title={titleNode} value={valueNode} description="Description" />);
      }).not.toThrow();
    });

    it('should work without description prop', () => {
      expect(() => {
        render(<AnalyticsCard title="Title Only" value="Value Only" />);
      }).not.toThrow();
    });
  });

  describe('Edge Cases', () => {
    it('should handle null description', () => {
      render(<AnalyticsCard title="Null Test" value="400" description={null} />);

      const descriptionElement = document.querySelector('[class*="description"]');
      expect(descriptionElement).toBeInTheDocument();
      expect(descriptionElement).toBeEmptyDOMElement();
    });

    it('should handle undefined description', () => {
      render(<AnalyticsCard title="Undefined Test" value="500" description={undefined} />);

      const descriptionElement = document.querySelector('[class*="description"]');
      expect(descriptionElement).toBeInTheDocument();
      expect(descriptionElement).toBeEmptyDOMElement();
    });

    it('should handle numeric values', () => {
      render(<AnalyticsCard title="Numeric Test" value={42} description="Numeric value test" />);

      expect(screen.getByText('42')).toBeInTheDocument();
    });

    it('should handle complex React nodes', () => {
      const complexTitle = (
        <div>
          <span>Complex</span> <strong>Title</strong>
        </div>
      );
      const complexValue = (
        <div>
          <span style={{ color: 'red' }}>1,234</span>
        </div>
      );

      render(<AnalyticsCard title={complexTitle} value={complexValue} description="Complex nodes test" />);

      expect(screen.getByText('Complex')).toBeInTheDocument();
      expect(screen.getByText('Title')).toBeInTheDocument();
      expect(screen.getByText('1,234')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper semantic structure', () => {
      render(<AnalyticsCard {...defaultProps} />);

      // Check that description is a paragraph element
      const descriptionElement = document.querySelector('[class*="description"]');
      expect(descriptionElement.tagName.toLowerCase()).toBe('p');
    });

    it('should be keyboard accessible', () => {
      render(<AnalyticsCard {...defaultProps} />);

      const cardElement = document.querySelector('[class*="card"]');
      expect(cardElement).toBeInTheDocument();

      // Card should be focusable if needed (Ant Design Card handles this)
      expect(cardElement).not.toHaveAttribute('tabindex', '-1');
    });
  });
});
