import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { SettingOutlined, HomeOutlined, UserOutlined } from '@ant-design/icons';
import { HeaderTitleButtons } from './header-title-buttons';

describe('HeaderTitleButtons', () => {
  const defaultProps = {
    title: 'Test Title',
    isButtonsShowed: true,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render the component with title', () => {
      render(<HeaderTitleButtons {...defaultProps} />);

      expect(screen.getByText('Test Title')).toBeInTheDocument();
    });

    it('should render without title when title prop is not provided', () => {
      render(<HeaderTitleButtons isButtonsShowed={true} />);

      expect(screen.queryByRole('heading')).not.toBeInTheDocument();
    });

    it('should render with custom title level', () => {
      render(<HeaderTitleButtons title="Custom Title" titleLevel={3} />);

      const heading = screen.getByRole('heading', { level: 3 });
      expect(heading).toBeInTheDocument();
      expect(heading).toHaveTextContent('Custom Title');
    });

    it('should render title with icon', () => {
      render(<HeaderTitleButtons title="Title with Icon" titleIcon={<UserOutlined data-testid="title-icon" />} />);

      expect(screen.getByText('Title with Icon')).toBeInTheDocument();
      expect(screen.getByTestId('title-icon')).toBeInTheDocument();
    });
  });

  describe('Buttons Visibility', () => {
    it('should show buttons when isButtonsShowed is true', () => {
      render(<HeaderTitleButtons {...defaultProps} />);

      expect(screen.getByText('Title Header')).toBeInTheDocument();
      expect(screen.getByText('Button Left')).toBeInTheDocument();
    });

    it('should hide buttons when isButtonsShowed is false', () => {
      render(<HeaderTitleButtons title="Test Title" isButtonsShowed={false} />);

      expect(screen.queryByText('Title Header')).not.toBeInTheDocument();
      expect(screen.queryByText('Button Left')).not.toBeInTheDocument();
    });

    it('should hide buttons when isButtonsShowed is not provided', () => {
      render(<HeaderTitleButtons title="Test Title" />);

      expect(screen.queryByText('Title Header')).not.toBeInTheDocument();
      expect(screen.queryByText('Button Left')).not.toBeInTheDocument();
    });
  });

  describe('Button Configuration', () => {
    it('should render buttons with custom text', () => {
      render(<HeaderTitleButtons {...defaultProps} button1Text="Custom Button 1" button2Text="Custom Button 2" />);

      expect(screen.getByText('Custom Button 1')).toBeInTheDocument();
      expect(screen.getByText('Custom Button 2')).toBeInTheDocument();
    });

    it('should render buttons with custom icons', () => {
      render(
        <HeaderTitleButtons
          {...defaultProps}
          button1Icon={<UserOutlined data-testid="button1-icon" />}
          button2Icon={<SettingOutlined data-testid="button2-icon" />}
        />
      );

      expect(screen.getByTestId('button1-icon')).toBeInTheDocument();
      expect(screen.getByTestId('button2-icon')).toBeInTheDocument();
    });

    it('should render buttons with custom types', () => {
      render(<HeaderTitleButtons {...defaultProps} button1Type="primary" button2Type="dashed" />);

      const button1 = screen.getByText('Title Header').closest('button');
      const button2 = screen.getByText('Button Left').closest('button');

      expect(button1).toHaveClass('ant-btn-primary');
      expect(button2).toHaveClass('ant-btn-dashed');
    });

    it('should use default button type when not specified', () => {
      render(<HeaderTitleButtons {...defaultProps} />);

      const button1 = screen.getByText('Title Header').closest('button');
      const button2 = screen.getByText('Button Left').closest('button');

      expect(button1).toHaveClass('ant-btn-default');
      expect(button2).toHaveClass('ant-btn-default');
    });

    it('should use default button type when button type is null or undefined', () => {
      render(<HeaderTitleButtons {...defaultProps} button1Type={null} button2Type={undefined} />);

      const button1 = screen.getByText('Title Header').closest('button');
      const button2 = screen.getByText('Button Left').closest('button');

      expect(button1).toHaveClass('ant-btn-default');
      expect(button2).toHaveClass('ant-btn-default');
    });
  });

  describe('Button Click Events', () => {
    it('should call button1OnClick when first button is clicked', () => {
      const mockButton1Click = jest.fn();

      render(<HeaderTitleButtons {...defaultProps} button1OnClick={mockButton1Click} />);

      fireEvent.click(screen.getByText('Title Header'));
      expect(mockButton1Click).toHaveBeenCalledTimes(1);
    });

    it('should call button2OnClick when second button is clicked', () => {
      const mockButton2Click = jest.fn();

      render(<HeaderTitleButtons {...defaultProps} button2OnClick={mockButton2Click} />);

      fireEvent.click(screen.getByText('Button Left'));
      expect(mockButton2Click).toHaveBeenCalledTimes(1);
    });

    it('should handle multiple clicks on buttons', () => {
      const mockButton1Click = jest.fn();
      const mockButton2Click = jest.fn();

      render(
        <HeaderTitleButtons {...defaultProps} button1OnClick={mockButton1Click} button2OnClick={mockButton2Click} />
      );

      fireEvent.click(screen.getByText('Title Header'));
      fireEvent.click(screen.getByText('Button Left'));
      fireEvent.click(screen.getByText('Title Header'));

      expect(mockButton1Click).toHaveBeenCalledTimes(2);
      expect(mockButton2Click).toHaveBeenCalledTimes(1);
    });
  });

  describe('Layout and Styling', () => {
    it('should render main flex container when title is present', () => {
      render(<HeaderTitleButtons {...defaultProps} />);

      const flexContainer = screen.getByText('Test Title').closest('.ant-flex');
      expect(flexContainer).toBeInTheDocument();
    });

    it('should render main flex container when title is not present', () => {
      render(<HeaderTitleButtons isButtonsShowed={true} />);

      const flexContainer = screen.getByText('Title Header').closest('.ant-flex');
      expect(flexContainer).toBeInTheDocument();
    });

    it('should render buttons with large size', () => {
      render(<HeaderTitleButtons {...defaultProps} />);

      const button1 = screen.getByText('Title Header').closest('button');
      const button2 = screen.getByText('Button Left').closest('button');

      expect(button1).toHaveClass('ant-btn-lg');
      expect(button2).toHaveClass('ant-btn-lg');
    });

    it('should render buttons container with proper flex layout', () => {
      render(<HeaderTitleButtons {...defaultProps} />);

      const buttonsContainer = screen.getByText('Title Header').closest('.ant-flex');
      expect(buttonsContainer).toBeInTheDocument();
      expect(buttonsContainer).toContainElement(screen.getByText('Title Header'));
      expect(buttonsContainer).toContainElement(screen.getByText('Button Left'));
    });
  });

  describe('Default Props', () => {
    it('should use default props when not specified', () => {
      render(<HeaderTitleButtons isButtonsShowed={true} />);

      expect(screen.getByText('Title Header')).toBeInTheDocument();
      expect(screen.getByText('Button Left')).toBeInTheDocument();
    });

    it('should use default title level when not specified', () => {
      render(<HeaderTitleButtons title="Default Level Title" />);

      const heading = screen.getByRole('heading', { level: 1 });
      expect(heading).toBeInTheDocument();
    });

    it('should not crash when default onClick functions are called', () => {
      render(<HeaderTitleButtons isButtonsShowed={true} />);

      expect(() => {
        fireEvent.click(screen.getByText('Title Header'));
        fireEvent.click(screen.getByText('Button Left'));
      }).not.toThrow();
    });
  });

  describe('PropTypes Validation', () => {
    it('should accept all valid button types', () => {
      const buttonTypes = ['default', 'primary', 'dashed', 'text', 'link'];

      buttonTypes.forEach((type) => {
        expect(() => {
          render(<HeaderTitleButtons isButtonsShowed={true} button1Type={type} button2Type={type} />);
        }).not.toThrow();
      });
    });

    it('should accept React nodes as icons', () => {
      expect(() => {
        render(
          <HeaderTitleButtons
            title="Test"
            titleIcon={<div>Custom Icon</div>}
            isButtonsShowed={true}
            button1Icon={<span>Icon 1</span>}
            button2Icon={<span>Icon 2</span>}
          />
        );
      }).not.toThrow();
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty string title', () => {
      render(<HeaderTitleButtons title="" isButtonsShowed={true} />);

      expect(screen.queryByRole('heading')).not.toBeInTheDocument();
    });

    it('should handle null title icon', () => {
      render(<HeaderTitleButtons title="Test" titleIcon={null} />);

      expect(screen.getByText('Test')).toBeInTheDocument();
    });

    it('should handle undefined button text', () => {
      render(<HeaderTitleButtons isButtonsShowed={true} button1Text={undefined} button2Text={undefined} />);

      const buttons = screen.getAllByRole('button');
      expect(buttons).toHaveLength(2);
    });
  });
});
