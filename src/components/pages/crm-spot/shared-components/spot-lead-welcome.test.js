import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { SpotLeadsWelcome } from './spot-lead-welcome';

// Mock Ant Design components
jest.mock('antd', () => ({
  Card: ({ children, className, ...props }) => (
    <div className={className} data-testid="card" {...props}>
      {children}
    </div>
  ),
}));

// Mock styles
jest.mock('./spot-lead-welcome.module.scss', () => ({
  card: 'card',
  header: 'header',
  icon: 'icon',
  text: 'text',
  title: 'title',
  description: 'description',
  footer: 'footer',
  divider: 'divider',
  highlight: 'highlight',
}));

describe('SpotLeadsWelcome Component', () => {
  describe('Main Component', () => {
    test('renders card with correct className and children', () => {
      render(
        <SpotLeadsWelcome>
          <div>Test content</div>
        </SpotLeadsWelcome>
      );

      const card = screen.getByTestId('card');
      expect(card).toBeInTheDocument();
      expect(card).toHaveClass('spot-welcome-card', 'card');
      expect(screen.getByText('Test content')).toBeInTheDocument();
    });

    test('renders without children', () => {
      render(<SpotLeadsWelcome />);

      const card = screen.getByTestId('card');
      expect(card).toBeInTheDocument();
      expect(card).toHaveClass('spot-welcome-card', 'card');
    });

    test('passes additional props to Card component', () => {
      render(
        <SpotLeadsWelcome data-custom="test-value" id="custom-id">
          <div>Content</div>
        </SpotLeadsWelcome>
      );

      const card = screen.getByTestId('card');
      expect(card).toHaveAttribute('data-custom', 'test-value');
      expect(card).toHaveAttribute('id', 'custom-id');
    });

    test('renders multiple children correctly', () => {
      render(
        <SpotLeadsWelcome>
          <div>First child</div>
          <div>Second child</div>
          <span>Third child</span>
        </SpotLeadsWelcome>
      );

      expect(screen.getByText('First child')).toBeInTheDocument();
      expect(screen.getByText('Second child')).toBeInTheDocument();
      expect(screen.getByText('Third child')).toBeInTheDocument();
    });
  });

  describe('Header Component', () => {
    test('renders header with title and description', () => {
      render(<SpotLeadsWelcome.Header title="Welcome Title" description="Welcome description text" />);

      expect(screen.getByText('Welcome Title')).toBeInTheDocument();
      expect(screen.getByText('Welcome description text')).toBeInTheDocument();
    });

    test('renders header with icon', () => {
      const TestIcon = () => <span data-testid="test-icon">🎉</span>;

      render(
        <SpotLeadsWelcome.Header title="Welcome Title" description="Welcome description text" icon={<TestIcon />} />
      );

      expect(screen.getByTestId('test-icon')).toBeInTheDocument();
      expect(screen.getByText('🎉')).toBeInTheDocument();
    });

    test('renders header without icon', () => {
      render(<SpotLeadsWelcome.Header title="Welcome Title" description="Welcome description text" />);

      const headerDiv = screen.getByText('Welcome Title').closest('.header');
      expect(headerDiv).toBeInTheDocument();
    });

    test('applies correct CSS classes to header elements', () => {
      render(
        <SpotLeadsWelcome.Header
          title="Welcome Title"
          description="Welcome description text"
          icon={<span>Icon</span>}
        />
      );

      const headerDiv = screen.getByText('Welcome Title').closest('.header');
      const titleElement = screen.getByText('Welcome Title');
      const descriptionElement = screen.getByText('Welcome description text');

      expect(headerDiv).toHaveClass('header');
      expect(titleElement).toHaveClass('title');
      expect(descriptionElement).toHaveClass('description');
    });

    test('passes additional props to header div', () => {
      render(
        <SpotLeadsWelcome.Header
          title="Welcome Title"
          description="Welcome description text"
          data-custom="header-value"
          id="header-id"
        />
      );

      const headerDiv = screen.getByText('Welcome Title').closest('.header');
      expect(headerDiv).toHaveAttribute('data-custom', 'header-value');
      expect(headerDiv).toHaveAttribute('id', 'header-id');
    });

    test('renders with long title and description', () => {
      const longTitle = 'This is a very long title that might wrap to multiple lines in the user interface';
      const longDescription =
        'This is a very long description that contains a lot of text and might also wrap to multiple lines when displayed in the user interface component';

      render(<SpotLeadsWelcome.Header title={longTitle} description={longDescription} />);

      expect(screen.getByText(longTitle)).toBeInTheDocument();
      expect(screen.getByText(longDescription)).toBeInTheDocument();
    });

    test('renders with special characters in title and description', () => {
      render(
        <SpotLeadsWelcome.Header
          title="Welcome! 🎉 Special Characters: @#$%"
          description="Description with émojis 🚀 and ñ characters & symbols"
        />
      );

      expect(screen.getByText('Welcome! 🎉 Special Characters: @#$%')).toBeInTheDocument();
      expect(screen.getByText('Description with émojis 🚀 and ñ characters & symbols')).toBeInTheDocument();
    });
  });

  describe('Footer Component', () => {
    test('renders footer with children', () => {
      render(
        <SpotLeadsWelcome.Footer>
          <div>Footer content</div>
        </SpotLeadsWelcome.Footer>
      );

      const footerDiv = screen.getByText('Footer content').closest('.footer');
      expect(footerDiv).toBeInTheDocument();
      expect(footerDiv).toHaveClass('footer');
      expect(screen.getByText('Footer content')).toBeInTheDocument();
    });

    test('renders footer without children', () => {
      render(<SpotLeadsWelcome.Footer />);

      const footerDiv = document.querySelector('.footer');
      expect(footerDiv).toBeInTheDocument();
      expect(footerDiv).toHaveClass('footer');
    });

    test('renders footer with multiple children', () => {
      render(
        <SpotLeadsWelcome.Footer>
          <span>First footer item</span>
          <button>Footer button</button>
          <div>Footer div</div>
        </SpotLeadsWelcome.Footer>
      );

      expect(screen.getByText('First footer item')).toBeInTheDocument();
      expect(screen.getByText('Footer button')).toBeInTheDocument();
      expect(screen.getByText('Footer div')).toBeInTheDocument();
    });

    test('passes additional props to footer div', () => {
      render(
        <SpotLeadsWelcome.Footer data-custom="footer-value" id="footer-id">
          <div>Footer content</div>
        </SpotLeadsWelcome.Footer>
      );

      const footerDiv = screen.getByText('Footer content').closest('.footer');
      expect(footerDiv).toHaveAttribute('data-custom', 'footer-value');
      expect(footerDiv).toHaveAttribute('id', 'footer-id');
    });

    test('renders footer with complex content', () => {
      render(
        <SpotLeadsWelcome.Footer>
          <div>
            <p>Complex footer content</p>
            <button>Action Button</button>
            <a href="#test">Link</a>
          </div>
        </SpotLeadsWelcome.Footer>
      );

      expect(screen.getByText('Complex footer content')).toBeInTheDocument();
      expect(screen.getByText('Action Button')).toBeInTheDocument();
      expect(screen.getByText('Link')).toBeInTheDocument();
    });
  });

  describe('Compound Component Integration', () => {
    test('renders complete welcome card with header and footer', () => {
      const TestIcon = () => <span data-testid="welcome-icon">🎯</span>;

      render(
        <SpotLeadsWelcome>
          <SpotLeadsWelcome.Header
            title="Welcome to CRM Spot"
            description="Manage your leads effectively"
            icon={<TestIcon />}
          />
          <div>Main content area</div>
          <SpotLeadsWelcome.Footer>
            <button>Get Started</button>
          </SpotLeadsWelcome.Footer>
        </SpotLeadsWelcome>
      );

      // Check main card
      const card = screen.getByTestId('card');
      expect(card).toBeInTheDocument();

      // Check header
      expect(screen.getByText('Welcome to CRM Spot')).toBeInTheDocument();
      expect(screen.getByText('Manage your leads effectively')).toBeInTheDocument();
      expect(screen.getByTestId('welcome-icon')).toBeInTheDocument();

      // Check main content
      expect(screen.getByText('Main content area')).toBeInTheDocument();

      // Check footer
      expect(screen.getByText('Get Started')).toBeInTheDocument();
    });

    test('renders with only header', () => {
      render(
        <SpotLeadsWelcome>
          <SpotLeadsWelcome.Header title="Header Only" description="No footer in this case" />
        </SpotLeadsWelcome>
      );

      expect(screen.getByText('Header Only')).toBeInTheDocument();
      expect(screen.getByText('No footer in this case')).toBeInTheDocument();
    });

    test('renders with only footer', () => {
      render(
        <SpotLeadsWelcome>
          <SpotLeadsWelcome.Footer>
            <div>Footer only content</div>
          </SpotLeadsWelcome.Footer>
        </SpotLeadsWelcome>
      );

      expect(screen.getByText('Footer only content')).toBeInTheDocument();
    });

    test('renders with custom content between header and footer', () => {
      render(
        <SpotLeadsWelcome>
          <SpotLeadsWelcome.Header title="Title" description="Description" />
          <div className="custom-content">
            <h3>Custom Section</h3>
            <p>Custom paragraph</p>
            <ul>
              <li>Item 1</li>
              <li>Item 2</li>
            </ul>
          </div>
          <SpotLeadsWelcome.Footer>
            <span>Footer text</span>
          </SpotLeadsWelcome.Footer>
        </SpotLeadsWelcome>
      );

      expect(screen.getByText('Title')).toBeInTheDocument();
      expect(screen.getByText('Custom Section')).toBeInTheDocument();
      expect(screen.getByText('Custom paragraph')).toBeInTheDocument();
      expect(screen.getByText('Item 1')).toBeInTheDocument();
      expect(screen.getByText('Item 2')).toBeInTheDocument();
      expect(screen.getByText('Footer text')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    test('handles empty strings in header props', () => {
      render(<SpotLeadsWelcome.Header title="" description="" />);

      const headerDiv = document.querySelector('.header');
      expect(headerDiv).toBeInTheDocument();
      expect(headerDiv).toHaveClass('header');
    });

    test('handles null and undefined children', () => {
      render(
        <SpotLeadsWelcome>
          {null}
          {undefined}
          <div>Valid content</div>
        </SpotLeadsWelcome>
      );

      expect(screen.getByText('Valid content')).toBeInTheDocument();
    });

    test('handles React fragments as children', () => {
      render(
        <SpotLeadsWelcome>
          <>
            <div>Fragment child 1</div>
            <div>Fragment child 2</div>
          </>
        </SpotLeadsWelcome>
      );

      expect(screen.getByText('Fragment child 1')).toBeInTheDocument();
      expect(screen.getByText('Fragment child 2')).toBeInTheDocument();
    });

    test('handles numeric and boolean children', () => {
      render(
        <SpotLeadsWelcome>
          {42}
          {true && <div>Conditional content</div>}
          {false && <div>Hidden content</div>}
        </SpotLeadsWelcome>
      );

      expect(screen.getByText('42')).toBeInTheDocument();
      expect(screen.getByText('Conditional content')).toBeInTheDocument();
      expect(screen.queryByText('Hidden content')).not.toBeInTheDocument();
    });
  });
});
