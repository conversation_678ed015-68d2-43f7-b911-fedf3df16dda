import React from 'react';
import { Table, Checkbox, Tag, Pagination, Select } from 'antd';
import { StatusTag } from 'components/pages/crm-spot/shared-components/status-tag';
import styles from './leads-table.module.scss';
import { format, parseISO } from 'date-fns';

export const ManagementLeadsTable = ({
  leads = [],
  loading = false,
  selectedRowKey = null,
  setSelectedRowKey,
  currentPage,
  pageSize,
  handlePageChange,
  handlePageSizeChange,
}) => {
  const getGradeTag = (grade) => {
    return (
      <Tag
        className={styles.gradeTag}
        style={{
          color: '#44464B',
          backgroundColor: '#F1F3F4',
          border: '1px solid #44464B',
        }}>
        {grade}
      </Tag>
    );
  };

  const getInitials = (name) => {
    if (!name || name === 'Unassigned') return '';

    const nameParts = name.split(' ');
    if (nameParts.length === 1) return nameParts[0].charAt(0).toUpperCase();
    return nameParts[0].charAt(0).toUpperCase();
  };

  const columns = [
    // {
    //   title: (
    //     <Checkbox
    //       indeterminate={selectedRowKeys.length > 0 && selectedRowKeys.length < leads.length}
    //       checked={selectedRowKeys.length === (leads ? leads.length : 0) && (leads ? leads.length : 0) > 0}
    //       onChange={(e) => {
    //         const newSelectedRowKeys = e.target.checked ? leads.map((lead) => lead) : [];
    //         if (onSelectChange) onSelectChange(newSelectedRowKeys);
    //       }}
    //     />
    //   ),
    //   dataIndex: 'checkbox',
    //   key: 'checkbox',
    //   width: 50,
    //   render: (_, record) => (
    //     <Checkbox
    //       checked={selectedRowKeys.some((item) => item.id === record.id)}
    //       onChange={(e) => {
    //         const exists = selectedRowKeys.some((item) => item.id === record.id);
    //         let newSelectedRowKeys;
    //         if (e.target.checked && !exists) {
    //           newSelectedRowKeys = [...selectedRowKeys, record];
    //         } else if (!e.target.checked && exists) {
    //           newSelectedRowKeys = selectedRowKeys.filter((item) => item.id !== record.id);
    //         } else {
    //           newSelectedRowKeys = selectedRowKeys;
    //         }
    //         if (onSelectChange) onSelectChange(newSelectedRowKeys);
    //       }}
    //     />
    //   ),
    // },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (_, record) => (
        <div>
          {record.parent_first_name} {record.parent_last_name}
        </div>
      ),
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: 'Phone',
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status, record) => {
        return <StatusTag status={record.sub_status ? record.sub_status : status} />;
      },
    },
    {
      title: 'Date Added',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (created_at) => {
        if (!created_at) return '';
        let dateObj;
        try {
          dateObj = typeof created_at === 'string' ? parseISO(created_at) : new Date(created_at);
        } catch {
          dateObj = new Date(created_at);
        }
        return format(dateObj, 'MM-dd-yyyy');
      },
    },
    {
      title: 'Assigned To',
      dataIndex: 'user_id_assigned',
      key: 'user_id_assigned',
      render: (user_id_assigned, record) => {
        if (!user_id_assigned) {
          return 'Unassigned';
        }

        const initial = getInitials(record.assigned_to);

        return (
          <div className={styles.assignedUser}>
            <span className={styles.userInitial}>{initial}</span>
            <span>{record.assigned_to}</span>
          </div>
        );
      },
    },
    {
      title: 'Grade',
      dataIndex: 'grade',
      key: 'grade',
      align: 'center',
      render: (grade) => {
        return getGradeTag(grade);
      },
    },
  ];

  return (
    <div className={styles.tableContainer}>
      <Table
        rowKey="id"
        dataSource={leads}
        columns={columns}
        loading={loading}
        className={styles.leadsTable}
        pagination={false}
        rowClassName={(record) => record.id === selectedRowKey?.id && styles.selectedRow}
        onRow={(record) => ({
          onClick: () => {
            // set state to the row clicked
            setSelectedRowKey(record);
          },
        })}
      />
      <div className={styles.paginationContainer}>
        <Select
          value={pageSize}
          onChange={handlePageSizeChange}
          // className={styles.pageSizeSelect}
          style={{ width: 140 }}
          options={[
            { value: 10, label: '10 per page' },
            { value: 50, label: '50 per page' },
            { value: 100, label: '100 per page' },
            { value: 200, label: '200 per page' },
          ]}
        />
        <Pagination
          current={currentPage}
          pageSize={pageSize}
          // total={100} // Replace with actual total count
          onChange={(page, pageSize) => handlePageChange(page, pageSize)}
          showSizeChanger={false}
          className={styles.pagination}
          itemRender={(page, type) => {
            if (type === 'prev') {
              return <span className={styles.paginationArrow}>← Previous</span>;
            }
            if (type === 'next') {
              return <span className={styles.paginationArrow}>Next →</span>;
            }
            return page;
          }}
        />
      </div>
    </div>
  );
};
