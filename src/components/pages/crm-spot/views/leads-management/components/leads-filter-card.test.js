import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { LeadsFilterCard } from './leads-filter-card';

// Mock Ant Design components
jest.mock('antd', () => ({
  Input: ({ placeholder, prefix, value, onChange, className }) => (
    <div className={className}>
      {prefix}
      <input
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        data-testid={placeholder?.includes('school') ? 'school-search-input' : 'search-input'}
      />
    </div>
  ),
  Button: ({ type, icon, className, disabled, onClick, children }) => (
    <button
      type="button"
      className={className}
      disabled={disabled}
      onClick={onClick}
      data-testid="reassign-button"
      data-type={type}>
      {icon}
      {children}
    </button>
  ),
  Space: ({ className, children }) => (
    <div className={className} data-testid="filter-actions">
      {children}
    </div>
  ),
  Select: ({ value, onChange, className, options }) => (
    <select
      value={value || ''}
      onChange={(e) => onChange(e.target.value)}
      className={className}
      data-testid="status-select">
      <option value="">Select status...</option>
      {options.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  ),
}));

// Mock Ant Design icons
jest.mock('@ant-design/icons', () => ({
  SearchOutlined: () => <span data-testid="search-icon" />,
  UserOutlined: () => <span data-testid="user-icon" />,
  DownOutlined: () => <span data-testid="down-icon" />,
}));

// Mock styles
jest.mock('./leads-filter-card.module.scss', () => ({
  filterCard: 'filterCard',
  filterHeader: 'filterHeader',
  searchContainer: 'searchContainer',
  searchInput: 'searchInput',
  filterActions: 'filterActions',
  statusDropdownContainer: 'statusDropdownContainer',
  statusDropdown: 'statusDropdown',
  reassignButton: 'reassignButton',
  tabsContainer: 'tabsContainer',
  customTabs: 'customTabs',
  tabButton: 'tabButton',
  activeTab: 'activeTab',
}));

describe('LeadsFilterCard Component', () => {
  const mockProps = {
    activeTab: 'all',
    setActiveTab: jest.fn(),
    searchText: '',
    setSearchText: jest.fn(),
    statusFilter: 'new',
    setStatusFilter: jest.fn(),
    selectedRowKeys: null,
    toggleReassignModal: jest.fn(),
    schoolId: '',
    setSchoolId: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    test('renders filter card with all components', () => {
      render(<LeadsFilterCard {...mockProps} />);

      expect(screen.getByTestId('search-input')).toBeInTheDocument();
      expect(screen.getByTestId('school-search-input')).toBeInTheDocument();
      expect(screen.getByTestId('status-select')).toBeInTheDocument();
      expect(screen.getByTestId('reassign-button')).toBeInTheDocument();
    });

    test('renders all tab buttons', () => {
      render(<LeadsFilterCard {...mockProps} />);

      expect(screen.getByText('All Leads')).toBeInTheDocument();
      expect(screen.getByText('Unassigned')).toBeInTheDocument();
      expect(screen.getByText('Assigned')).toBeInTheDocument();
    });

    test('applies correct CSS classes', () => {
      render(<LeadsFilterCard {...mockProps} />);

      const filterCard = document.querySelector('.filterCard');
      expect(filterCard).toBeInTheDocument();
      expect(filterCard).toHaveClass('spacing-mt-16');
    });

    test('renders search icons', () => {
      render(<LeadsFilterCard {...mockProps} />);

      const searchIcons = screen.getAllByTestId('search-icon');
      expect(searchIcons).toHaveLength(2);
    });

    test('renders user icon in reassign button', () => {
      render(<LeadsFilterCard {...mockProps} />);

      expect(screen.getByTestId('user-icon')).toBeInTheDocument();
    });
  });

  describe('Search Functionality', () => {
    test('displays search text value', () => {
      render(<LeadsFilterCard {...mockProps} searchText="test search" />);

      const searchInput = screen.getByTestId('search-input');
      expect(searchInput).toHaveValue('test search');
    });

    test('calls setSearchText when search input changes', () => {
      render(<LeadsFilterCard {...mockProps} />);

      const searchInput = screen.getByTestId('search-input');
      fireEvent.change(searchInput, { target: { value: 'new search' } });

      expect(mockProps.setSearchText).toHaveBeenCalledWith('new search');
    });

    test('displays school ID value', () => {
      render(<LeadsFilterCard {...mockProps} schoolId="12345" />);

      const schoolInput = screen.getByTestId('school-search-input');
      expect(schoolInput).toHaveValue('12345');
    });

    test('calls setSchoolId when school search input changes', () => {
      render(<LeadsFilterCard {...mockProps} />);

      const schoolInput = screen.getByTestId('school-search-input');
      fireEvent.change(schoolInput, { target: { value: '67890' } });

      expect(mockProps.setSchoolId).toHaveBeenCalledWith('67890');
    });

    test('renders correct placeholders', () => {
      render(<LeadsFilterCard {...mockProps} />);

      expect(screen.getByPlaceholderText('Search leads by name, email, phone...')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Search by school ID')).toBeInTheDocument();
    });
  });

  describe('Status Filter', () => {
    test('displays current status filter value', () => {
      render(<LeadsFilterCard {...mockProps} statusFilter="working" />);

      const statusSelect = screen.getByTestId('status-select');
      expect(statusSelect).toHaveValue('working');
    });

    test('calls setStatusFilter when status changes', () => {
      render(<LeadsFilterCard {...mockProps} />);

      const statusSelect = screen.getByTestId('status-select');
      fireEvent.change(statusSelect, { target: { value: 'application-sent' } });

      expect(mockProps.setStatusFilter).toHaveBeenCalledWith('application-sent');
    });

    test('renders all status options', () => {
      render(<LeadsFilterCard {...mockProps} />);

      expect(screen.getByText('New')).toBeInTheDocument();
      expect(screen.getByText('Attempting')).toBeInTheDocument();
      expect(screen.getByText('Working')).toBeInTheDocument();
      expect(screen.getByText('Application Sent')).toBeInTheDocument();
      expect(screen.getByText('Application Received')).toBeInTheDocument();
    });

    test('does not render "All Status" option', () => {
      render(<LeadsFilterCard {...mockProps} />);

      expect(screen.queryByText('All Status')).not.toBeInTheDocument();
    });
  });

  describe('Tab Navigation', () => {
    test('highlights active tab', () => {
      render(<LeadsFilterCard {...mockProps} activeTab="unassigned" />);

      const unassignedTab = screen.getByText('Unassigned');
      expect(unassignedTab).toHaveClass('activeTab');
    });

    test('calls setActiveTab when tab is clicked', () => {
      render(<LeadsFilterCard {...mockProps} />);

      const assignedTab = screen.getByText('Assigned');
      fireEvent.click(assignedTab);

      expect(mockProps.setActiveTab).toHaveBeenCalledWith('assigned');
    });

    test('applies correct classes to tab buttons', () => {
      render(<LeadsFilterCard {...mockProps} activeTab="all" />);

      const allTab = screen.getByText('All Leads');
      const unassignedTab = screen.getByText('Unassigned');

      expect(allTab).toHaveClass('tabButton', 'activeTab');
      expect(unassignedTab).toHaveClass('tabButton');
      expect(unassignedTab).not.toHaveClass('activeTab');
    });

    test('renders all three tabs', () => {
      render(<LeadsFilterCard {...mockProps} />);

      const tabButtons = document.querySelectorAll('.tabButton');
      expect(tabButtons).toHaveLength(3);
    });
  });

  describe('Reassign Button', () => {
    test('is disabled when no rows are selected', () => {
      render(<LeadsFilterCard {...mockProps} selectedRowKeys={null} />);

      const reassignButton = screen.getByTestId('reassign-button');
      expect(reassignButton).toBeDisabled();
    });

    test('is enabled when rows are selected', () => {
      render(<LeadsFilterCard {...mockProps} selectedRowKeys={{ id: 1 }} />);

      const reassignButton = screen.getByTestId('reassign-button');
      expect(reassignButton).not.toBeDisabled();
    });

    test('shows "Assign Lead" when no user is assigned', () => {
      render(<LeadsFilterCard {...mockProps} selectedRowKeys={{ id: 1 }} />);

      expect(screen.getByText('Assign Lead')).toBeInTheDocument();
    });

    test('shows "Reassign Lead" when user is already assigned', () => {
      render(<LeadsFilterCard {...mockProps} selectedRowKeys={{ id: 1, user_id_assigned: 123 }} />);

      expect(screen.getByText('Reassign Lead')).toBeInTheDocument();
    });

    test('calls toggleReassignModal when clicked', () => {
      render(<LeadsFilterCard {...mockProps} selectedRowKeys={{ id: 1 }} />);

      const reassignButton = screen.getByTestId('reassign-button');
      fireEvent.click(reassignButton);

      expect(mockProps.toggleReassignModal).toHaveBeenCalledTimes(1);
    });

    test('has primary type', () => {
      render(<LeadsFilterCard {...mockProps} />);

      const reassignButton = screen.getByTestId('reassign-button');
      expect(reassignButton).toHaveAttribute('data-type', 'primary');
    });
  });

  describe('Edge Cases', () => {
    test('handles empty string values', () => {
      render(<LeadsFilterCard {...mockProps} searchText="" schoolId="" statusFilter="" />);

      const searchInput = screen.getByTestId('search-input');
      const schoolInput = screen.getByTestId('school-search-input');
      const statusSelect = screen.getByTestId('status-select');

      expect(searchInput).toHaveValue('');
      expect(schoolInput).toHaveValue('');
      // Status select will have empty value when statusFilter is empty string
      expect(statusSelect.value).toBe('');
    });

    test('handles undefined selectedRowKeys', () => {
      render(<LeadsFilterCard {...mockProps} selectedRowKeys={undefined} />);

      const reassignButton = screen.getByTestId('reassign-button');
      expect(reassignButton).toBeDisabled();
      expect(screen.getByText('Assign Lead')).toBeInTheDocument();
    });

    test('handles selectedRowKeys with falsy user_id_assigned', () => {
      render(<LeadsFilterCard {...mockProps} selectedRowKeys={{ id: 1, user_id_assigned: null }} />);

      expect(screen.getByText('Assign Lead')).toBeInTheDocument();
    });

    test('handles selectedRowKeys with truthy user_id_assigned', () => {
      render(<LeadsFilterCard {...mockProps} selectedRowKeys={{ id: 1, user_id_assigned: 0 }} />);

      expect(screen.getByText('Assign Lead')).toBeInTheDocument();
    });

    test('handles long search text', () => {
      const longText = 'This is a very long search text that might cause layout issues in the component';
      render(<LeadsFilterCard {...mockProps} searchText={longText} />);

      const searchInput = screen.getByTestId('search-input');
      expect(searchInput).toHaveValue(longText);
    });

    test('handles special characters in search', () => {
      const specialText = '<EMAIL> +1-234-567-8900 #special';
      render(<LeadsFilterCard {...mockProps} searchText={specialText} />);

      const searchInput = screen.getByTestId('search-input');
      expect(searchInput).toHaveValue(specialText);
    });
  });

  describe('Component Integration', () => {
    test('renders with all props provided', () => {
      const fullProps = {
        activeTab: 'assigned',
        setActiveTab: jest.fn(),
        searchText: 'John Doe',
        setSearchText: jest.fn(),
        statusFilter: 'working',
        setStatusFilter: jest.fn(),
        selectedRowKeys: { id: 1, user_id_assigned: 123 },
        toggleReassignModal: jest.fn(),
        schoolId: '12345',
        setSchoolId: jest.fn(),
      };

      render(<LeadsFilterCard {...fullProps} />);

      expect(screen.getByTestId('search-input')).toHaveValue('John Doe');
      expect(screen.getByTestId('school-search-input')).toHaveValue('12345');
      expect(screen.getByTestId('status-select')).toHaveValue('working');
      expect(screen.getByText('Assigned')).toHaveClass('activeTab');
      expect(screen.getByText('Reassign Lead')).toBeInTheDocument();
    });

    test('updates when props change', () => {
      const { rerender } = render(<LeadsFilterCard {...mockProps} activeTab="all" />);

      expect(screen.getByText('All Leads')).toHaveClass('activeTab');

      rerender(<LeadsFilterCard {...mockProps} activeTab="unassigned" />);

      expect(screen.getByText('All Leads')).not.toHaveClass('activeTab');
      expect(screen.getByText('Unassigned')).toHaveClass('activeTab');
    });

    test('maintains state independence', () => {
      render(<LeadsFilterCard {...mockProps} />);

      const searchInput = screen.getByTestId('search-input');
      const schoolInput = screen.getByTestId('school-search-input');

      fireEvent.change(searchInput, { target: { value: 'search1' } });
      fireEvent.change(schoolInput, { target: { value: 'school1' } });

      expect(mockProps.setSearchText).toHaveBeenCalledWith('search1');
      expect(mockProps.setSchoolId).toHaveBeenCalledWith('school1');
      expect(mockProps.setSearchText).not.toHaveBeenCalledWith('school1');
      expect(mockProps.setSchoolId).not.toHaveBeenCalledWith('search1');
    });
  });
});
