import React from 'react';
import { Input, Button, Space, Select } from 'antd';
import { SearchOutlined, UserOutlined, DownOutlined } from '@ant-design/icons';
import styles from './leads-filter-card.module.scss';

export const LeadsFilterCard = ({
  activeTab,
  setActiveTab,
  searchText,
  setSearchText,
  statusFilter,
  setStatusFilter,
  selectedRowKeys = null,
  toggleReassignModal,
  schoolId,
  setSchoolId,
}) => {
  const tabs = [
    { key: 'all', label: 'All Leads' },
    { key: 'unassigned', label: 'Unassigned' },
    { key: 'assigned', label: 'Assigned' },
  ];

  return (
    <div className={`spacing-mt-16 ${styles.filterCard}`}>
      <div className={styles.filterHeader}>
        <div className={styles.searchContainer}>
          <Input
            placeholder="Search leads by name, email, phone..."
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            className={styles.searchInput}
          />
        </div>
        <Space className={styles.filterActions}>
          <Input
            placeholder="Search by school ID"
            prefix={<SearchOutlined />}
            value={schoolId}
            onChange={(e) => setSchoolId(e.target.value)}
            className={styles.searchInput}
          />
          <div className={styles.statusDropdownContainer}>
            <Select
              value={statusFilter}
              onChange={setStatusFilter}
              className={styles.statusDropdown}
              options={[
                // { value: 'all', label: 'All Status' },
                { value: 'new', label: 'New' },
                { value: 'attempting-contact', label: 'Attempting' },
                { value: 'working', label: 'Working' },
                { value: 'application-sent', label: 'Application Sent' },
                { value: 'application-received', label: 'Application Received' },
              ]}
            />
          </div>
          <Button
            type="primary"
            icon={<UserOutlined />}
            className={styles.reassignButton}
            disabled={!selectedRowKeys}
            onClick={toggleReassignModal}>
            {selectedRowKeys?.user_id_assigned ? 'Reassign' : 'Assign'} Lead
          </Button>
        </Space>
      </div>
      <div className={styles.tabsContainer}>
        <div className={styles.customTabs}>
          {tabs.map((tab) => (
            <button
              key={tab.key}
              className={`${styles.tabButton} ${activeTab === tab.key ? styles.activeTab : ''}`}
              onClick={() => setActiveTab(tab.key)}>
              {tab.label}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};
