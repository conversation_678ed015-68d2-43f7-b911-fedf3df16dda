import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ParentCardContainer } from './parent-card-container';

// Mock Ant Design icons
jest.mock('@ant-design/icons', () => ({
  BarChartOutlined: (props) => <span data-testid="bar-chart-icon" {...props} />,
  ExportOutlined: (props) => <span data-testid="export-icon" {...props} />,
  TeamOutlined: (props) => <span data-testid="team-icon" {...props} />,
}));

// Mock Card component
jest.mock('components/card/card', () => ({
  Card: ({ children, className }) => (
    <div className={className} data-testid="card">
      {children}
    </div>
  ),
}));

// Mock HeaderTitleButtons component
jest.mock('../../../shared-components/header-title-buttons', () => ({
  HeaderTitleButtons: ({
    title,
    titleIcon,
    titleLevel,
    button1Text,
    button1Icon,
    button1Type,
    button1OnClick,
    button2Text,
    button2Icon,
    button2Type,
    button2OnClick,
    isButtonsShowed,
  }) => (
    <div data-testid="header-title-buttons">
      <div data-testid="title-section">
        {titleIcon}
        <h4 data-testid="title" data-level={titleLevel}>
          {title}
        </h4>
      </div>
      {isButtonsShowed && (
        <div data-testid="buttons-section">
          <button type="button" data-testid="button1" data-type={button1Type} onClick={button1OnClick}>
            {button1Icon}
            {button1Text}
          </button>
          <button type="button" data-testid="button2" data-type={button2Type} onClick={button2OnClick}>
            {button2Icon}
            {button2Text}
          </button>
        </div>
      )}
    </div>
  ),
}));

// Mock styles
jest.mock('./parent-card-container.module.scss', () => ({
  card_container: 'card_container',
  card_header: 'card_header',
  header_content: 'header_content',
  header_icon: 'header_icon',
  card_content: 'card_content',
  card_footer: 'card_footer',
  footer_text: 'footer_text',
}));

describe('ParentCardContainer Component', () => {
  describe('Main Component', () => {
    test('renders card container with children', () => {
      render(
        <ParentCardContainer>
          <div>Test content</div>
        </ParentCardContainer>
      );

      const card = screen.getByTestId('card');
      expect(card).toBeInTheDocument();
      expect(card).toHaveClass('card_container');
      expect(screen.getByText('Test content')).toBeInTheDocument();
    });

    test('renders without children', () => {
      render(<ParentCardContainer />);

      const card = screen.getByTestId('card');
      expect(card).toBeInTheDocument();
      expect(card).toHaveClass('card_container');
    });

    test('renders multiple children correctly', () => {
      render(
        <ParentCardContainer>
          <div>First child</div>
          <div>Second child</div>
          <span>Third child</span>
        </ParentCardContainer>
      );

      expect(screen.getByText('First child')).toBeInTheDocument();
      expect(screen.getByText('Second child')).toBeInTheDocument();
      expect(screen.getByText('Third child')).toBeInTheDocument();
    });
  });

  describe('Header Component', () => {
    const mockProps = {
      title: 'Test Analytics',
      isVisible: true,
      onToggleVisibility: jest.fn(),
      onExportData: jest.fn(),
    };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    test('renders header with default title', () => {
      render(<ParentCardContainer.Header />);

      expect(screen.getByTestId('title')).toHaveTextContent('Department Analytics');
    });

    test('renders header with custom title', () => {
      render(<ParentCardContainer.Header title="Custom Analytics" />);

      expect(screen.getByTestId('title')).toHaveTextContent('Custom Analytics');
    });

    test('renders header with correct CSS classes', () => {
      render(<ParentCardContainer.Header {...mockProps} />);

      const header = document.querySelector('.card_header');
      const headerContent = document.querySelector('.header_content');

      expect(header).toBeInTheDocument();
      expect(headerContent).toBeInTheDocument();
    });

    test('renders bar chart icon with correct classes', () => {
      render(<ParentCardContainer.Header {...mockProps} />);

      const icon = screen.getByTestId('bar-chart-icon');
      expect(icon).toBeInTheDocument();
      expect(icon).toHaveClass('header_icon', 'text-navy-blue-100');
    });

    test('passes correct props to HeaderTitleButtons', () => {
      render(<ParentCardContainer.Header {...mockProps} />);

      const headerTitleButtons = screen.getByTestId('header-title-buttons');
      expect(headerTitleButtons).toBeInTheDocument();

      const title = screen.getByTestId('title');
      expect(title).toHaveTextContent('Test Analytics');
      expect(title).toHaveAttribute('data-level', '4');

      expect(screen.getByTestId('bar-chart-icon')).toBeInTheDocument();
    });

    test('shows "Hide Team Analytics" when isVisible is true', () => {
      render(<ParentCardContainer.Header {...mockProps} isVisible />);

      const button1 = screen.getByTestId('button1');
      expect(button1).toHaveTextContent('Hide Team Analytics');
    });

    test('shows "Show Team Analytics" when isVisible is false', () => {
      render(<ParentCardContainer.Header {...mockProps} isVisible={false} />);

      const button1 = screen.getByTestId('button1');
      expect(button1).toHaveTextContent('Show Team Analytics');
    });

    test('calls onToggleVisibility when first button is clicked', () => {
      render(<ParentCardContainer.Header {...mockProps} />);

      const button1 = screen.getByTestId('button1');
      fireEvent.click(button1);

      expect(mockProps.onToggleVisibility).toHaveBeenCalledTimes(1);
    });

    test('calls onExportData when second button is clicked', () => {
      render(<ParentCardContainer.Header {...mockProps} />);

      const button2 = screen.getByTestId('button2');
      fireEvent.click(button2);

      expect(mockProps.onExportData).toHaveBeenCalledTimes(1);
    });

    test('renders export button with correct text and icon', () => {
      render(<ParentCardContainer.Header {...mockProps} />);

      const button2 = screen.getByTestId('button2');
      expect(button2).toHaveTextContent('Export Data');
      expect(screen.getByTestId('export-icon')).toBeInTheDocument();
    });

    test('renders team icon in first button', () => {
      render(<ParentCardContainer.Header {...mockProps} />);

      expect(screen.getByTestId('team-icon')).toBeInTheDocument();
    });

    test('sets correct button types', () => {
      render(<ParentCardContainer.Header {...mockProps} />);

      const button1 = screen.getByTestId('button1');
      const button2 = screen.getByTestId('button2');

      expect(button1).toHaveAttribute('data-type', 'default');
      expect(button2).toHaveAttribute('data-type', 'default');
    });
  });

  describe('Content Component', () => {
    test('renders content with children', () => {
      render(
        <ParentCardContainer.Content>
          <div>Content area</div>
        </ParentCardContainer.Content>
      );

      const content = document.querySelector('.card_content');
      expect(content).toBeInTheDocument();
      expect(screen.getByText('Content area')).toBeInTheDocument();
    });

    test('renders content without children', () => {
      render(<ParentCardContainer.Content />);

      const content = document.querySelector('.card_content');
      expect(content).toBeInTheDocument();
    });

    test('renders multiple children in content', () => {
      render(
        <ParentCardContainer.Content>
          <div>First content</div>
          <p>Second content</p>
          <span>Third content</span>
        </ParentCardContainer.Content>
      );

      expect(screen.getByText('First content')).toBeInTheDocument();
      expect(screen.getByText('Second content')).toBeInTheDocument();
      expect(screen.getByText('Third content')).toBeInTheDocument();
    });

    test('applies correct CSS class', () => {
      render(
        <ParentCardContainer.Content>
          <div>Test content</div>
        </ParentCardContainer.Content>
      );

      const content = document.querySelector('.card_content');
      expect(content).toHaveClass('card_content');
    });
  });

  describe('Footer Component', () => {
    test('renders footer with default text', () => {
      render(<ParentCardContainer.Footer />);

      expect(screen.getByText('Managing SPOT Team leads and workload distribution')).toBeInTheDocument();
    });

    test('renders footer with custom text', () => {
      render(<ParentCardContainer.Footer text="Custom footer text" />);

      expect(screen.getByText('Custom footer text')).toBeInTheDocument();
    });

    test('applies correct CSS classes', () => {
      render(<ParentCardContainer.Footer />);

      const footer = document.querySelector('.card_footer');
      const footerText = document.querySelector('.footer_text');

      expect(footer).toBeInTheDocument();
      expect(footerText).toBeInTheDocument();
      expect(footer).toHaveClass('card_footer');
      expect(footerText).toHaveClass('footer_text');
    });

    test('renders with empty string text', () => {
      render(<ParentCardContainer.Footer text="" />);

      const footerText = document.querySelector('.footer_text');
      expect(footerText).toBeInTheDocument();
      expect(footerText).toHaveTextContent('');
    });

    test('renders with long text', () => {
      const longText =
        'This is a very long footer text that might wrap to multiple lines and should be handled gracefully by the component';
      render(<ParentCardContainer.Footer text={longText} />);

      expect(screen.getByText(longText)).toBeInTheDocument();
    });
  });

  describe('Compound Component Integration', () => {
    const mockHandlers = {
      onToggleVisibility: jest.fn(),
      onExportData: jest.fn(),
    };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    test('renders complete card with header, content, and footer', () => {
      render(
        <ParentCardContainer>
          <ParentCardContainer.Header
            title="Analytics Dashboard"
            isVisible
            onToggleVisibility={mockHandlers.onToggleVisibility}
            onExportData={mockHandlers.onExportData}
          />
          <ParentCardContainer.Content>
            <div>Main content area</div>
            <p>Additional content</p>
          </ParentCardContainer.Content>
          <ParentCardContainer.Footer text="Custom footer message" />
        </ParentCardContainer>
      );

      // Check main card
      expect(screen.getByTestId('card')).toBeInTheDocument();

      // Check header
      expect(screen.getByTestId('header-title-buttons')).toBeInTheDocument();
      expect(screen.getByText('Analytics Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Hide Team Analytics')).toBeInTheDocument();
      expect(screen.getByText('Export Data')).toBeInTheDocument();

      // Check content
      expect(screen.getByText('Main content area')).toBeInTheDocument();
      expect(screen.getByText('Additional content')).toBeInTheDocument();

      // Check footer
      expect(screen.getByText('Custom footer message')).toBeInTheDocument();
    });

    test('renders with only header', () => {
      render(
        <ParentCardContainer>
          <ParentCardContainer.Header title="Header Only" />
        </ParentCardContainer>
      );

      expect(screen.getByText('Header Only')).toBeInTheDocument();
      expect(screen.queryByText('Managing SPOT Team leads and workload distribution')).not.toBeInTheDocument();
    });

    test('renders with only content', () => {
      render(
        <ParentCardContainer>
          <ParentCardContainer.Content>
            <div>Content only</div>
          </ParentCardContainer.Content>
        </ParentCardContainer>
      );

      expect(screen.getByText('Content only')).toBeInTheDocument();
      expect(screen.queryByTestId('header-title-buttons')).not.toBeInTheDocument();
    });

    test('renders with only footer', () => {
      render(
        <ParentCardContainer>
          <ParentCardContainer.Footer text="Footer only" />
        </ParentCardContainer>
      );

      expect(screen.getByText('Footer only')).toBeInTheDocument();
      expect(screen.queryByTestId('header-title-buttons')).not.toBeInTheDocument();
    });

    test('renders with header and content only', () => {
      render(
        <ParentCardContainer>
          <ParentCardContainer.Header title="Header and Content" />
          <ParentCardContainer.Content>
            <div>Content area</div>
          </ParentCardContainer.Content>
        </ParentCardContainer>
      );

      expect(screen.getByText('Header and Content')).toBeInTheDocument();
      expect(screen.getByText('Content area')).toBeInTheDocument();
      expect(screen.queryByText('Managing SPOT Team leads and workload distribution')).not.toBeInTheDocument();
    });

    test('handles button interactions in complete component', () => {
      render(
        <ParentCardContainer>
          <ParentCardContainer.Header
            title="Interactive Dashboard"
            isVisible={false}
            onToggleVisibility={mockHandlers.onToggleVisibility}
            onExportData={mockHandlers.onExportData}
          />
          <ParentCardContainer.Content>
            <div>Dashboard content</div>
          </ParentCardContainer.Content>
        </ParentCardContainer>
      );

      const toggleButton = screen.getByTestId('button1');
      const exportButton = screen.getByTestId('button2');

      fireEvent.click(toggleButton);
      fireEvent.click(exportButton);

      expect(mockHandlers.onToggleVisibility).toHaveBeenCalledTimes(1);
      expect(mockHandlers.onExportData).toHaveBeenCalledTimes(1);
    });

    test('renders with mixed content types', () => {
      render(
        <ParentCardContainer>
          <ParentCardContainer.Header title="Mixed Content" />
          <div>Custom content outside subcomponents</div>
          <ParentCardContainer.Content>
            <div>Structured content</div>
          </ParentCardContainer.Content>
          <span>Another custom element</span>
          <ParentCardContainer.Footer text="Mixed footer" />
        </ParentCardContainer>
      );

      expect(screen.getByText('Mixed Content')).toBeInTheDocument();
      expect(screen.getByText('Custom content outside subcomponents')).toBeInTheDocument();
      expect(screen.getByText('Structured content')).toBeInTheDocument();
      expect(screen.getByText('Another custom element')).toBeInTheDocument();
      expect(screen.getByText('Mixed footer')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    test('handles null children in main component', () => {
      render(
        <ParentCardContainer>
          {null}
          {undefined}
          <div>Valid content</div>
        </ParentCardContainer>
      );

      expect(screen.getByText('Valid content')).toBeInTheDocument();
    });

    test('handles React fragments as children', () => {
      render(
        <ParentCardContainer>
          <>
            <div>Fragment child 1</div>
            <div>Fragment child 2</div>
          </>
        </ParentCardContainer>
      );

      expect(screen.getByText('Fragment child 1')).toBeInTheDocument();
      expect(screen.getByText('Fragment child 2')).toBeInTheDocument();
    });

    test('handles header with undefined callback functions', () => {
      render(
        <ParentCardContainer.Header
          title="Test Header"
          isVisible
          onToggleVisibility={undefined}
          onExportData={undefined}
        />
      );

      const button1 = screen.getByTestId('button1');
      const button2 = screen.getByTestId('button2');

      // Should not throw errors when clicking
      expect(() => {
        fireEvent.click(button1);
        fireEvent.click(button2);
      }).not.toThrow();
    });

    test('handles header with null title', () => {
      render(<ParentCardContainer.Header title={null} />);

      // Should render with empty title
      const title = screen.getByTestId('title');
      expect(title).toBeInTheDocument();
    });

    test('handles footer with null text', () => {
      render(<ParentCardContainer.Footer text={null} />);

      const footerText = document.querySelector('.footer_text');
      expect(footerText).toBeInTheDocument();
    });

    test('handles content with conditional children', () => {
      const showContent = true;
      render(
        <ParentCardContainer.Content>
          {showContent && <div>Conditional content</div>}
          {!showContent && <div>Alternative content</div>}
        </ParentCardContainer.Content>
      );

      expect(screen.getByText('Conditional content')).toBeInTheDocument();
      expect(screen.queryByText('Alternative content')).not.toBeInTheDocument();
    });

    test('handles header visibility toggle state changes', () => {
      const { rerender } = render(
        <ParentCardContainer.Header
          title="Toggle Test"
          isVisible
          onToggleVisibility={jest.fn()}
          onExportData={jest.fn()}
        />
      );

      expect(screen.getByText('Hide Team Analytics')).toBeInTheDocument();

      rerender(
        <ParentCardContainer.Header
          title="Toggle Test"
          isVisible={false}
          onToggleVisibility={jest.fn()}
          onExportData={jest.fn()}
        />
      );

      expect(screen.getByText('Show Team Analytics')).toBeInTheDocument();
    });
  });

  describe('Component Props and Defaults', () => {
    test('header uses default title when not provided', () => {
      render(<ParentCardContainer.Header />);

      expect(screen.getByText('Department Analytics')).toBeInTheDocument();
    });

    test('footer uses default text when not provided', () => {
      render(<ParentCardContainer.Footer />);

      expect(screen.getByText('Managing SPOT Team leads and workload distribution')).toBeInTheDocument();
    });

    test('header passes all required props to HeaderTitleButtons', () => {
      const mockProps = {
        title: 'Test Title',
        isVisible: true,
        onToggleVisibility: jest.fn(),
        onExportData: jest.fn(),
      };

      render(<ParentCardContainer.Header {...mockProps} />);

      // Verify HeaderTitleButtons receives correct props
      expect(screen.getByTestId('header-title-buttons')).toBeInTheDocument();
      expect(screen.getByTestId('title')).toHaveAttribute('data-level', '4');
      expect(screen.getByTestId('buttons-section')).toBeInTheDocument();
    });

    test('main component passes className to Card', () => {
      render(<ParentCardContainer />);

      const card = screen.getByTestId('card');
      expect(card).toHaveClass('card_container');
    });
  });
});
