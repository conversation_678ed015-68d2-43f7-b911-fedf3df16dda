import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ManagementLeadsTable } from './leads-table';

// Mock date-fns
jest.mock('date-fns', () => ({
  format: jest.fn((date, formatStr) => {
    if (!date || date.toString() === 'Invalid Date') return '';
    if (formatStr === 'MM-dd-yyyy') {
      return '01-15-2024';
    }
    return '01-15-2024';
  }),
  parseISO: jest.fn((dateStr) => {
    if (!dateStr || dateStr === 'invalid-date') {
      throw new Error('Invalid date');
    }
    return new Date('2024-01-15T10:30:00Z');
  }),
}));

// Mock Ant Design components
jest.mock('antd', () => ({
  Table: ({ rowKey, dataSource, columns, loading, className, pagination, rowClassName, onRow, children }) => (
    <div className={className} data-testid="leads-table" data-loading={loading}>
      <table>
        <thead>
          <tr>
            {columns.map((col, index) => (
              <th key={index} data-testid={`column-${col.key}`}>
                {typeof col.title === 'function' ? col.title() : col.title}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {dataSource.map((record, rowIndex) => (
            <tr
              key={record[rowKey]}
              className={rowClassName ? rowClassName(record) || '' : ''}
              onClick={() => onRow && onRow(record).onClick && onRow(record).onClick()}
              data-testid={`table-row-${record.id}`}>
              {columns.map((col, colIndex) => (
                <td key={colIndex} data-testid={`cell-${record.id}-${col.key}`}>
                  {col.render ? col.render(record[col.dataIndex], record, rowIndex) : record[col.dataIndex]}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
      {children}
    </div>
  ),
  Checkbox: ({ checked, onChange, indeterminate }) => (
    <input
      type="checkbox"
      checked={checked}
      onChange={onChange}
      data-testid="checkbox"
      data-indeterminate={indeterminate}
    />
  ),
  Tag: ({ children, className, style }) => (
    <span className={className} style={style} data-testid="tag">
      {children}
    </span>
  ),
  Pagination: ({ current, pageSize, total, onChange, showSizeChanger, className, itemRender }) => (
    <div className={className} data-testid="pagination">
      <button onClick={() => onChange(current - 1, pageSize)} disabled={current === 1} data-testid="prev-button">
        {itemRender ? itemRender(current - 1, 'prev') : 'Previous'}
      </button>
      <span data-testid="current-page">{current}</span>
      <button
        onClick={() => onChange(current + 1, pageSize)}
        disabled={current * pageSize >= total}
        data-testid="next-button">
        {itemRender ? itemRender(current + 1, 'next') : 'Next'}
      </button>
      <span data-testid="total-info">Total: {total}</span>
    </div>
  ),
  Select: ({ value, onChange, style, options }) => (
    <select
      value={value}
      onChange={(e) => onChange(parseInt(e.target.value))}
      style={style}
      data-testid="page-size-select">
      {options.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  ),
}));

// Mock StatusTag component
jest.mock('components/pages/crm-spot/shared-components/status-tag', () => ({
  StatusTag: ({ status }) => <span data-testid="status-tag">{status}</span>,
}));

// Mock styles
jest.mock('./leads-table.module.scss', () => ({
  tableContainer: 'tableContainer',
  leadsTable: 'leadsTable',
  gradeTag: 'gradeTag',
  assignedUser: 'assignedUser',
  userInitial: 'userInitial',
  paginationContainer: 'paginationContainer',
  pagination: 'pagination',
  paginationArrow: 'paginationArrow',
  selectedRow: 'selectedRow',
}));

describe('ManagementLeadsTable Component', () => {
  const mockProps = {
    leads: [],
    loading: false,
    selectedRowKey: null,
    setSelectedRowKey: jest.fn(),
    totalCount: 0,
    currentPage: 1,
    pageSize: 10,
    handlePageChange: jest.fn(),
    handlePageSizeChange: jest.fn(),
  };

  const mockLeads = [
    {
      id: 1,
      parent_first_name: 'John',
      parent_last_name: 'Doe',
      school_id: 'SCH001',
      email: '<EMAIL>',
      phone: '******-567-8900',
      status: 'new',
      sub_status: 'attempting',
      created_at: '2024-01-15T10:30:00Z',
      user_id_assigned: 123,
      assigned_to: 'Jane Smith',
      grade: '9th',
    },
    {
      id: 2,
      parent_first_name: 'Alice',
      parent_last_name: 'Johnson',
      school_id: 'SCH002',
      email: '<EMAIL>',
      phone: '******-567-8901',
      status: 'working',
      sub_status: null,
      created_at: '2024-01-14T09:15:00Z',
      user_id_assigned: null,
      assigned_to: null,
      grade: '10th',
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    test('renders table container with correct CSS class', () => {
      render(<ManagementLeadsTable {...mockProps} />);

      const container = document.querySelector('.tableContainer');
      expect(container).toBeInTheDocument();
    });

    test('renders table with correct props', () => {
      render(<ManagementLeadsTable {...mockProps} leads={mockLeads} loading />);

      const table = screen.getByTestId('leads-table');
      expect(table).toBeInTheDocument();
      expect(table).toHaveClass('leadsTable');
      expect(table).toHaveAttribute('data-loading', 'true');
    });

    test('renders all table columns', () => {
      render(<ManagementLeadsTable {...mockProps} />);

      expect(screen.getByTestId('column-name')).toHaveTextContent('Name');
      expect(screen.getByTestId('column-school_id')).toHaveTextContent('School ID');
      expect(screen.getByTestId('column-email')).toHaveTextContent('Email');
      expect(screen.getByTestId('column-phone')).toHaveTextContent('Phone');
      expect(screen.getByTestId('column-status')).toHaveTextContent('Status');
      expect(screen.getByTestId('column-created_at')).toHaveTextContent('Date Added');
      expect(screen.getByTestId('column-user_id_assigned')).toHaveTextContent('Assigned To');
      expect(screen.getByTestId('column-grade')).toHaveTextContent('Grade');
    });

    test('renders pagination container', () => {
      render(<ManagementLeadsTable {...mockProps} />);

      const paginationContainer = document.querySelector('.paginationContainer');
      expect(paginationContainer).toBeInTheDocument();
    });

    test('renders page size selector', () => {
      render(<ManagementLeadsTable {...mockProps} />);

      const pageSizeSelect = screen.getByTestId('page-size-select');
      expect(pageSizeSelect).toBeInTheDocument();
      expect(pageSizeSelect).toHaveValue('10');
    });

    test('renders pagination component', () => {
      render(<ManagementLeadsTable {...mockProps} totalCount={100} />);

      expect(screen.getByTestId('pagination')).toBeInTheDocument();
      expect(screen.getByTestId('current-page')).toHaveTextContent('1');
      expect(screen.getByTestId('total-info')).toHaveTextContent('Total: 100');
    });
  });

  describe('Data Display', () => {
    test('displays lead data correctly', () => {
      render(<ManagementLeadsTable {...mockProps} leads={mockLeads} />);

      // Check first lead
      expect(screen.getByTestId('cell-1-name')).toHaveTextContent('John Doe');
      expect(screen.getByTestId('cell-1-school_id')).toHaveTextContent('SCH001');
      expect(screen.getByTestId('cell-1-email')).toHaveTextContent('<EMAIL>');
      expect(screen.getByTestId('cell-1-phone')).toHaveTextContent('******-567-8900');

      // Check second lead
      expect(screen.getByTestId('cell-2-name')).toHaveTextContent('Alice Johnson');
      expect(screen.getByTestId('cell-2-school_id')).toHaveTextContent('SCH002');
    });

    test('displays status using StatusTag component', () => {
      render(<ManagementLeadsTable {...mockProps} leads={mockLeads} />);

      const statusTags = screen.getAllByTestId('status-tag');
      expect(statusTags).toHaveLength(2);
      expect(statusTags[0]).toHaveTextContent('attempting'); // sub_status takes precedence
      expect(statusTags[1]).toHaveTextContent('working'); // no sub_status, uses status
    });

    test('displays formatted dates', () => {
      render(<ManagementLeadsTable {...mockProps} leads={mockLeads} />);

      const dateCells = screen.getAllByTestId(/cell-\d+-created_at/);
      expect(dateCells).toHaveLength(2);
      // Check that date cells exist and contain some content (the actual formatting is handled by date-fns)
      dateCells.forEach((cell) => {
        expect(cell).toBeInTheDocument();
      });
    });

    test('displays assigned user with initial', () => {
      render(<ManagementLeadsTable {...mockProps} leads={mockLeads} />);

      const assignedCell = screen.getByTestId('cell-1-user_id_assigned');
      expect(assignedCell.querySelector('.assignedUser')).toBeInTheDocument();
      expect(assignedCell.querySelector('.userInitial')).toHaveTextContent('J');
      expect(assignedCell).toHaveTextContent('Jane Smith');
    });

    test('displays "Unassigned" for leads without assigned user', () => {
      render(<ManagementLeadsTable {...mockProps} leads={mockLeads} />);

      const unassignedCell = screen.getByTestId('cell-2-user_id_assigned');
      expect(unassignedCell).toHaveTextContent('Unassigned');
    });

    test('displays grade tags with correct styling', () => {
      render(<ManagementLeadsTable {...mockProps} leads={mockLeads} />);

      const gradeTags = screen.getAllByTestId('tag');
      expect(gradeTags).toHaveLength(2);
      expect(gradeTags[0]).toHaveTextContent('9th');
      expect(gradeTags[1]).toHaveTextContent('10th');

      gradeTags.forEach((tag) => {
        expect(tag).toHaveClass('gradeTag');
        expect(tag).toHaveStyle({
          color: '#44464B',
          backgroundColor: '#F1F3F4',
          border: '1px solid #44464B',
        });
      });
    });
  });

  describe('Row Selection', () => {
    test('calls setSelectedRowKey when row is clicked', () => {
      render(<ManagementLeadsTable {...mockProps} leads={mockLeads} />);

      const firstRow = screen.getByTestId('table-row-1');
      fireEvent.click(firstRow);

      expect(mockProps.setSelectedRowKey).toHaveBeenCalledWith(mockLeads[0]);
    });

    test('applies selected row class when row is selected', () => {
      render(<ManagementLeadsTable {...mockProps} leads={mockLeads} selectedRowKey={{ id: 1 }} />);

      const firstRow = screen.getByTestId('table-row-1');
      expect(firstRow).toHaveClass('selectedRow');

      const secondRow = screen.getByTestId('table-row-2');
      expect(secondRow).not.toHaveClass('selectedRow');
    });

    test('does not apply selected row class when no row is selected', () => {
      render(<ManagementLeadsTable {...mockProps} leads={mockLeads} selectedRowKey={null} />);

      const firstRow = screen.getByTestId('table-row-1');
      const secondRow = screen.getByTestId('table-row-2');

      expect(firstRow).not.toHaveClass('selectedRow');
      expect(secondRow).not.toHaveClass('selectedRow');
    });
  });

  describe('Pagination', () => {
    test('calls handlePageChange when pagination buttons are clicked', () => {
      render(<ManagementLeadsTable {...mockProps} currentPage={2} totalCount={100} />);

      const prevButton = screen.getByTestId('prev-button');
      const nextButton = screen.getByTestId('next-button');

      fireEvent.click(prevButton);
      expect(mockProps.handlePageChange).toHaveBeenCalledWith(1, 10);

      fireEvent.click(nextButton);
      expect(mockProps.handlePageChange).toHaveBeenCalledWith(3, 10);
    });

    test('calls handlePageSizeChange when page size is changed', () => {
      render(<ManagementLeadsTable {...mockProps} />);

      const pageSizeSelect = screen.getByTestId('page-size-select');
      fireEvent.change(pageSizeSelect, { target: { value: '50' } });

      expect(mockProps.handlePageSizeChange).toHaveBeenCalledWith(50);
    });

    test('displays custom pagination arrows', () => {
      render(<ManagementLeadsTable {...mockProps} />);

      expect(screen.getByText('← Previous')).toBeInTheDocument();
      expect(screen.getByText('Next →')).toBeInTheDocument();
    });

    test('renders page size options correctly', () => {
      render(<ManagementLeadsTable {...mockProps} />);

      const pageSizeSelect = screen.getByTestId('page-size-select');
      const options = pageSizeSelect.querySelectorAll('option');

      expect(options).toHaveLength(4);
      expect(options[0]).toHaveTextContent('10 per page');
      expect(options[1]).toHaveTextContent('50 per page');
      expect(options[2]).toHaveTextContent('100 per page');
      expect(options[3]).toHaveTextContent('200 per page');
    });
  });

  describe('Loading State', () => {
    test('passes loading state to table', () => {
      render(<ManagementLeadsTable {...mockProps} loading />);

      const table = screen.getByTestId('leads-table');
      expect(table).toHaveAttribute('data-loading', 'true');
    });

    test('does not show loading when loading is false', () => {
      render(<ManagementLeadsTable {...mockProps} loading={false} />);

      const table = screen.getByTestId('leads-table');
      expect(table).toHaveAttribute('data-loading', 'false');
    });
  });

  describe('Edge Cases', () => {
    test('handles empty leads array', () => {
      render(<ManagementLeadsTable {...mockProps} leads={[]} />);

      const table = screen.getByTestId('leads-table');
      expect(table).toBeInTheDocument();
      expect(screen.queryByTestId(/table-row-/)).not.toBeInTheDocument();
    });

    test('handles leads with missing data', () => {
      const incompleteLeads = [
        {
          id: 1,
          parent_first_name: null,
          parent_last_name: undefined,
          school_id: '',
          email: null,
          phone: undefined,
          status: null,
          sub_status: undefined,
          created_at: null,
          user_id_assigned: null,
          assigned_to: null,
          grade: null,
        },
      ];

      render(<ManagementLeadsTable {...mockProps} leads={incompleteLeads} />);

      expect(screen.getByTestId('table-row-1')).toBeInTheDocument();
      const nameCell = screen.getByTestId('cell-1-name');
      expect(nameCell.textContent.trim()).toBe(''); // null + undefined = empty
      expect(screen.getByTestId('cell-1-user_id_assigned')).toHaveTextContent('Unassigned');
    });

    test('handles leads with invalid date formats', () => {
      const leadsWithInvalidDate = [
        {
          id: 1,
          parent_first_name: 'John',
          parent_last_name: 'Doe',
          created_at: 'invalid-date',
          user_id_assigned: null,
          grade: '9th',
        },
      ];

      render(<ManagementLeadsTable {...mockProps} leads={leadsWithInvalidDate} />);

      // Check that the date cell exists and handles invalid dates gracefully
      const dateCell = screen.getByTestId('cell-1-created_at');
      expect(dateCell).toBeInTheDocument();
    });

    test('handles leads with empty created_at', () => {
      const leadsWithEmptyDate = [
        {
          id: 1,
          parent_first_name: 'John',
          parent_last_name: 'Doe',
          created_at: null,
          user_id_assigned: null,
          grade: '9th',
        },
      ];

      render(<ManagementLeadsTable {...mockProps} leads={leadsWithEmptyDate} />);

      expect(screen.getByTestId('cell-1-created_at')).toHaveTextContent('');
    });

    test('handles assigned user with single name', () => {
      const leadWithSingleName = [
        {
          id: 1,
          parent_first_name: 'John',
          parent_last_name: 'Doe',
          user_id_assigned: 123,
          assigned_to: 'Madonna',
          grade: '9th',
        },
      ];

      render(<ManagementLeadsTable {...mockProps} leads={leadWithSingleName} />);

      const assignedCell = screen.getByTestId('cell-1-user_id_assigned');
      expect(assignedCell.querySelector('.userInitial')).toHaveTextContent('M');
      expect(assignedCell).toHaveTextContent('Madonna');
    });

    test('handles assigned user with "Unassigned" name', () => {
      const leadWithUnassignedName = [
        {
          id: 1,
          parent_first_name: 'John',
          parent_last_name: 'Doe',
          user_id_assigned: 123,
          assigned_to: 'Unassigned',
          grade: '9th',
        },
      ];

      render(<ManagementLeadsTable {...mockProps} leads={leadWithUnassignedName} />);

      const assignedCell = screen.getByTestId('cell-1-user_id_assigned');
      expect(assignedCell.querySelector('.userInitial')).toHaveTextContent('');
      expect(assignedCell).toHaveTextContent('Unassigned');
    });

    test('handles leads with zero totalCount', () => {
      render(<ManagementLeadsTable {...mockProps} totalCount={0} />);

      expect(screen.getByTestId('total-info')).toHaveTextContent('Total: 0');
    });

    test('handles leads with very high page numbers', () => {
      render(<ManagementLeadsTable {...mockProps} currentPage={999} totalCount={10000} />);

      expect(screen.getByTestId('current-page')).toHaveTextContent('999');
    });

    test('handles selectedRowKey with different id type', () => {
      render(<ManagementLeadsTable {...mockProps} leads={mockLeads} selectedRowKey={{ id: '1' }} />);

      const firstRow = screen.getByTestId('table-row-1');
      expect(firstRow).not.toHaveClass('selectedRow'); // string '1' !== number 1
    });
  });

  describe('Utility Functions', () => {
    test('getGradeTag function creates correct tag structure', () => {
      render(<ManagementLeadsTable {...mockProps} leads={mockLeads} />);

      const gradeTags = screen.getAllByTestId('tag');
      gradeTags.forEach((tag) => {
        expect(tag).toHaveClass('gradeTag');
        expect(tag).toHaveStyle({
          color: '#44464B',
          backgroundColor: '#F1F3F4',
          border: '1px solid #44464B',
        });
      });
    });

    test('getInitials function handles various name formats', () => {
      const testCases = [
        {
          leads: [{ id: 1, user_id_assigned: 123, assigned_to: 'John Doe Smith', grade: '9th' }],
          expectedInitial: 'J',
        },
        {
          leads: [{ id: 1, user_id_assigned: 123, assigned_to: 'madonna', grade: '9th' }],
          expectedInitial: 'M',
        },
        {
          leads: [{ id: 1, user_id_assigned: 123, assigned_to: '', grade: '9th' }],
          expectedInitial: '',
        },
        {
          leads: [{ id: 1, user_id_assigned: 123, assigned_to: null, grade: '9th' }],
          expectedInitial: '',
        },
      ];

      testCases.forEach(({ leads, expectedInitial }, index) => {
        const { unmount } = render(<ManagementLeadsTable {...mockProps} leads={leads} />);

        if (expectedInitial) {
          const assignedCell = screen.getByTestId('cell-1-user_id_assigned');
          expect(assignedCell.querySelector('.userInitial')).toHaveTextContent(expectedInitial);
        }

        unmount();
      });
    });
  });

  describe('Component Integration', () => {
    test('renders with all props provided', () => {
      const fullProps = {
        leads: mockLeads,
        loading: true,
        selectedRowKey: { id: 1 },
        setSelectedRowKey: jest.fn(),
        totalCount: 150,
        currentPage: 3,
        pageSize: 50,
        handlePageChange: jest.fn(),
        handlePageSizeChange: jest.fn(),
      };

      render(<ManagementLeadsTable {...fullProps} />);

      expect(screen.getByTestId('leads-table')).toHaveAttribute('data-loading', 'true');
      expect(screen.getByTestId('current-page')).toHaveTextContent('3');
      expect(screen.getByTestId('total-info')).toHaveTextContent('Total: 150');
      expect(screen.getByTestId('page-size-select')).toHaveValue('50');
      expect(screen.getByTestId('table-row-1')).toHaveClass('selectedRow');
    });

    test('updates when props change', () => {
      const { rerender } = render(<ManagementLeadsTable {...mockProps} leads={[]} loading={false} />);

      expect(screen.getByTestId('leads-table')).toHaveAttribute('data-loading', 'false');
      expect(screen.queryByTestId(/table-row-/)).not.toBeInTheDocument();

      rerender(<ManagementLeadsTable {...mockProps} leads={mockLeads} loading />);

      expect(screen.getByTestId('leads-table')).toHaveAttribute('data-loading', 'true');
      expect(screen.getByTestId('table-row-1')).toBeInTheDocument();
      expect(screen.getByTestId('table-row-2')).toBeInTheDocument();
    });

    test('maintains table functionality with different data sets', () => {
      const differentLeads = [
        {
          id: 999,
          parent_first_name: 'Test',
          parent_last_name: 'User',
          school_id: 'TEST001',
          email: '<EMAIL>',
          phone: '******-999-9999',
          status: 'application-sent',
          sub_status: null,
          created_at: '2024-02-01T12:00:00Z',
          user_id_assigned: 456,
          assigned_to: 'Test Manager',
          grade: '12th',
        },
      ];

      render(<ManagementLeadsTable {...mockProps} leads={differentLeads} />);

      expect(screen.getByTestId('cell-999-name')).toHaveTextContent('Test User');
      expect(screen.getByTestId('cell-999-school_id')).toHaveTextContent('TEST001');

      const statusTag = screen.getByTestId('status-tag');
      expect(statusTag).toHaveTextContent('application-sent');

      const assignedCell = screen.getByTestId('cell-999-user_id_assigned');
      expect(assignedCell.querySelector('.userInitial')).toHaveTextContent('T');
    });
  });
});
