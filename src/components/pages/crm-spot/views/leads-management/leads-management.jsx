import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { Col, Row } from 'antd';
import { BarChartOutlined, HomeOutlined, SettingOutlined, TeamOutlined } from '@ant-design/icons';
import { AnalyticsSection } from '../../shared-components';
import { SpotLeadsWelcome } from '../../shared-components/spot-lead-welcome';
import { ParentCardContainer } from './components/parent-card-container';
import { TeamBalanceCard } from './components/team-balance-card';
import { LeadsFilterCard } from './components/leads-filter-card';
import { ManagementLeadsTable } from './components/leads-table';
import { ReassignModal } from './components/reassign-modal';
import { TeamPerformance } from './components/team-performance';
import { HeaderTitleButtons } from '../../shared-components/header-title-buttons';
import { browserHistory } from 'react-router';
import { useTeamAnalytics, useTeamBalance } from '../../hooks/useTeamBalance';
import styles from './components/team-balance-card.module.scss';
import { parseUserStatus } from '../../utils/parseUserStatus';
import { useUnassignmentsLeads } from '../../hooks/useUnassignmentsLeads';
import { useSPOTUsers } from '../../hooks/useSPOTUsers';
import { getDateRange } from '../../utils/get-date-range';
import { useToggle } from 'hooks/useToggle';
import { TeamBalance } from '../../shared-components/team-balance';
import { useDebounce } from 'hooks/useDebounce';

export const LeadsManagement = () => {
  // SPOT admin exclusive access
  const { roles, isSPOTadmin } = useSelector((state) => state.spot);

  useEffect(() => {
    if (roles.length > 0 && !isSPOTadmin) {
      browserHistory.push('/admin/dashboard');
    }
  }, [roles]);

  const { users } = useSPOTUsers();
  const { teamBalance, fetchTeamBalance, loading } = useTeamBalance();

  const { from, to } = getDateRange('month');
  const { analytics, loading: loadingAnalytics } = useTeamAnalytics({
    from,
    to,
  });

  const [isTeamAnalyticsVisible, setIsTeamAnalyticsVisible] = useState(false);
  const handleToggleTeamAnalytics = () => {
    setIsTeamAnalyticsVisible(!isTeamAnalyticsVisible);
  };

  return (
    <div>
      <HeaderTitleButtons
        isButtonsShowed
        button1Text={'Management Console'}
        button1Icon={<SettingOutlined />}
        button1Type="default"
        button1OnClick={() => browserHistory.push('/admin/management-console/team-management')}
        button2Text="Home"
        button2Icon={<HomeOutlined />}
        button2Type="default"
        button2OnClick={() => browserHistory.push('/admin/dashboard')}
      />
      <SpotLeadsWelcome>
        <SpotLeadsWelcome.Header
          title="Centralized Lead Management"
          icon={<BarChartOutlined />}
          description="Streamline lead distribution within the SPOT Team to handle team member absences and maintain balanced workloads. This dashboard enables quick reassignment of parent leads between SPOT representatives, ensuring continuous support and optimal team efficiency."
        />
        <SpotLeadsWelcome.Footer>
          Managing <span className="font-700 text-white">SPOT Team</span> leads and workload distribution
        </SpotLeadsWelcome.Footer>
      </SpotLeadsWelcome>
      <ParentCardContainer>
        <ParentCardContainer.Header
          title="Department Analytics"
          isVisible={isTeamAnalyticsVisible}
          onToggleVisibility={handleToggleTeamAnalytics}
        />
        <ParentCardContainer.Content>
          <AnalyticsSection analytics={analytics} loading={loadingAnalytics} />
          {isTeamAnalyticsVisible && <TeamPerformance teamMembers={users} />}
        </ParentCardContainer.Content>
      </ParentCardContainer>
      <Row gutter={16}>
        <Col span={12}>
          <TeamBalance teamBalance={teamBalance} fetchTeamBalance={fetchTeamBalance} loading={loading} />
        </Col>
        <Col span={12}>
          <TeamBalanceCard title="Team Status" Icon={TeamOutlined} onRefresh={fetchTeamBalance} loading={loading}>
            {!teamBalance || teamBalance.length === 0 ? (
              <div className={styles.empty_state}>No team status data available.</div>
            ) : (
              teamBalance.map((member, index) => {
                const { userStatus, tag } = parseUserStatus(member.status);
                return (
                  <div key={index} className={styles.member_row}>
                    <div className={styles.member_name}>
                      {member.first_name} {member.last_name}
                    </div>
                    <div className={`${styles.status} ${styles[userStatus]}`}>
                      {userStatus} - {tag}
                    </div>
                  </div>
                );
              })
            )}
          </TeamBalanceCard>
        </Col>
      </Row>

      <ManagementLeads teamMembers={users} />
    </div>
  );
};

const ManagementLeads = ({ teamMembers }) => {
  const [activeTab, setActiveTab] = useState('all');
  const [statusFilter, setStatusFilter] = useState('new');
  const [schoolId, setSchoolId] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [selectedRowKey, setSelectedRowKey] = useState(null);

  const debouncedSchoolId = useDebounce(schoolId, 500);

  const {
    unassignmentsLeads,
    loading: unassignedLoading,
    createAssignment,
    changeAssignment,
  } = useUnassignmentsLeads({
    assignedStatus: activeTab,
    status: statusFilter,
    schoolId: debouncedSchoolId,
    page: currentPage,
    pageSize,
  });

  const [isReassignModalVisible, toggleReassignModal] = useToggle(false);

  const handleAssignment = async (assignedUserId) => {
    try {
      if (selectedRowKey?.user_id_assigned) {
        await changeAssignment({ new_user_id: assignedUserId, assignment_id: selectedRowKey.assignment_id });
      } else {
        await createAssignment({ assignedUserId, leadId: selectedRowKey.id });
      }
    } catch (error) {
      console.error('Error creating assignment:', error);
    }
    toggleReassignModal();
    setSelectedRowKey(null);
  };

  const handlePageChange = (page, pageSize) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (value) => {
    setPageSize(value);
    setCurrentPage(1);
  };

  const [searchText, setSearchText] = useState('');

  const filteredLeads = unassignmentsLeads?.result?.filter((lead) => {
    return (
      searchText === '' ||
      lead.first_name?.toLowerCase().includes(searchText.toLowerCase()) ||
      lead.last_name?.toLowerCase().includes(searchText.toLowerCase()) ||
      lead.email?.toLowerCase().includes(searchText.toLowerCase()) ||
      lead.phone?.includes(searchText)
    );
  });

  return (
    <div>
      <LeadsFilterCard
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        searchText={searchText}
        setSearchText={setSearchText}
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
        selectedRowKeys={selectedRowKey}
        toggleReassignModal={toggleReassignModal}
        schoolId={schoolId}
        setSchoolId={setSchoolId}
      />
      <ManagementLeadsTable
        leads={filteredLeads}
        loading={unassignedLoading}
        selectedRowKey={selectedRowKey}
        setSelectedRowKey={setSelectedRowKey}
        totalCount={unassignmentsLeads?.totalCount}
        pageSize={pageSize}
        currentPage={currentPage}
        handlePageChange={handlePageChange}
        handlePageSizeChange={handlePageSizeChange}
      />

      <ReassignModal
        isVisible={isReassignModalVisible}
        onClose={toggleReassignModal}
        handleAssignment={handleAssignment}
        selectedRowKey={selectedRowKey}
        teamMembers={teamMembers}
      />
    </div>
  );
};
