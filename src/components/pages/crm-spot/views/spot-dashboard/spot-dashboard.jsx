import React, { useState } from 'react';
import { Typography } from 'antd';
import { originalLeads } from '../../mocks/mockLeads';
import { LeadHeader } from '../../shared-components';
import { useLeads } from '../../hooks/useLeads';
import { useSelector } from 'react-redux';
import { clearSelectedLead, setFilterValue, setGlobalValue, setStatusFilter } from 'redux/spot/spot-actions';
import {
  LeadFilters,
  LeadsTableContent,
  AddLeadForm,
  UpdateStage,
  AdvancedFilters,
  TaskTable,
  LeadTaskModal,
  LeadEdition,
  LeadHistoryModal,
} from './components';
import { _getLeads, _updateLeadBatch } from 'controllers/leads/leads_controller';
import styles from './spot-dashboard.module.scss';
import { getProfile } from 'util/auth';
import { useUserTasks } from '../../hooks/useUserTasks';
import { useLeadAssignments } from '../../hooks/useAssignmentsLeads';
import { _getUserLeadAssignments } from 'controllers/lead-assignments/lead-assignments-controller';
import { HeaderTitleButtons } from '../../shared-components/header-title-buttons';
import { SettingOutlined } from '@ant-design/icons';
import { useToggle } from 'hooks/useToggle';
import { useNotes } from '../../hooks/useNotes';
import { useSchoolsAssigned } from '../../hooks/useSchoolsAssigned';

const { Text } = Typography;

export const SPOTDashboard = () => {
  const { selectedLead, statusFilter, isLeadEdition, filterValue, advancedFilters, isAssignedMode, tasks } =
    useSelector((state) => state.spot);
  const userId = getProfile().sub;

  const { updateUserTaskStatus, createUserTask } = useUserTasks(userId);

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const handlePageChange = (page, size) => {
    setCurrentPage(page);
    if (size !== pageSize) setPageSize(size);
  };

  const handlePageSizeChange = (size) => {
    setPageSize(size);
    setCurrentPage(1);
  };

  const { schoolsByUser, schoolId, onSelectSchool } = useSchoolsAssigned({ user_id: userId });

  const {
    data: rawLeads,
    loading,
    updateAssignment,
    getLeadAssignments,
  } = useLeadAssignments({
    user_id: userId,
    status: statusFilter === 'tasks' ? '' : statusFilter,
    createdAt_from: advancedFilters.createdAt_from || '',
    createdAt_to: advancedFilters.createdAt_to || '',
    grades: advancedFilters.grades,
    search_notes: advancedFilters.search_notes || '',
    // sources: [],
    assigned_schools: isAssignedMode,
    school_id: schoolId,
    page: currentPage,
    pageSize,
  });

  const handleFilterSearch = (value) => {
    setFilterValue(value);
  };

  // local filtering
  const { leads } = useLeads({ initialLeads: rawLeads, filterValue });

  const [isHistoryModalOpen, toggleHistoryModalOpen] = useToggle(false);
  const notes = useNotes(selectedLead);

  return (
    <div>
      <HeaderTitleButtons />
      <div className={styles.container}>
        <div className={styles.contentSection}>
          <LeadHeader user_id={userId} />
          <LeadFilters
            isAssignedMode={isAssignedMode}
            statusFilter={statusFilter}
            handleFilterSearch={handleFilterSearch}
            setStatusFilter={setStatusFilter}
            schoolsByUser={schoolsByUser}
            schoolId={schoolId}
            onSelectSchool={onSelectSchool}
          />
          {statusFilter !== 'tasks' ? (
            <LeadsTableContent
              userId={userId}
              leads={leads || []}
              loading={loading}
              handlePageChange={handlePageChange}
              handlePageSizeChange={handlePageSizeChange}
              pageSize={pageSize}
              currentPage={currentPage}
              toggleHistoryModalOpen={toggleHistoryModalOpen}
            />
          ) : (
            <TaskTable tasks={tasks} updateUserTaskStatus={updateUserTaskStatus} />
          )}
        </div>
      </div>
      {/* Modals layer: */}
      <AdvancedFilters />

      <LeadTaskModal tasks={tasks} createUserTask={createUserTask} updateUserTaskStatus={updateUserTaskStatus} />

      <AddLeadForm />

      <LeadEdition
        open={isLeadEdition}
        onClose={() => {
          setGlobalValue('isLeadEdition', false);
          clearSelectedLead();
        }}
        getLeadAssignments={getLeadAssignments}
        notesHook={notes}
      />

      <LeadHistoryModal notesHook={notes} open={isHistoryModalOpen} onClose={toggleHistoryModalOpen} />

      <UpdateStage
        assignments={{
          getLeadAssignments,
          updateAssignment,
          loading,
        }}
        userId={userId}
        notesHook={notes}
      />
    </div>
  );
};
