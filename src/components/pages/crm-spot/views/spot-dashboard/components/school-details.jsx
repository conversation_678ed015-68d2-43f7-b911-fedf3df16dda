import React from 'react';
import styles from './school-details.module.scss';
import { Button } from 'antd';
import {
  CloseOutlined,
  BookOutlined,
  UserOutlined,
  CalendarOutlined,
  EnvironmentOutlined,
  CarOutlined,
  SkinOutlined,
  ReadOutlined,
  BulbOutlined,
  PhoneOutlined,
  MailOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
import { closeSchoolDetails } from 'redux/spot/spot-actions';
import { useSchool } from 'components/pages/crm-spot/hooks/useSchool';

export const SchoolDetails = ({ schoolId }) => {
  const { school, loading, error } = useSchool(schoolId);

  if (loading) return <div>Cargando escuela...</div>;
  if (error) return <div>Error: {error.message}</div>;
  if (!school) return null;

  const studentsPerTeachersRatio = getStudentsPerTeachersRatio({
    numberOfStudents: school.number_of_students,
    numberOfTeachers: school.number_of_teachers,
  });

  const insights = getInsightsFromFeatures(school?.features);

  return (
    <>
      <SchoolHeader school={school} onClose={closeSchoolDetails} />
      <div className={styles.drawerContent}>
        <div className="box spacing-m-0">
          <div className={styles.sectionHeader}>
            <BookOutlined className={styles.sectionIcon} />
            <h3 className={styles.sectionTitle}>Overview</h3>
          </div>
          <p className={styles.sectionContent}>{school.description}</p>
        </div>

        <div className="box spacing-m-0">
          <div className={styles.sectionHeader}>
            <BulbOutlined className={styles.sectionIcon} />
            <h3 className={styles.sectionTitle}>Key Facts</h3>
          </div>
          <div className={styles.keyFact}>
            <UserOutlined className={styles.keyFactIcon} />
            <span className="text-dark-blue">Student-Teacher Ratio: {studentsPerTeachersRatio ?? 'N/A'}</span>
          </div>
          <div className={styles.keyFactsContent}>
            <div className={styles.keyFact}>
              <EnvironmentOutlined className={styles.keyFactIcon} />
              <span className="text-dark-blue">
                Location: {school.address} - {school.city}, {school.state}
              </span>
            </div>
            {school.grades && (
              <div className={styles.gradesContainer}>
                <span className={styles.gradesBadge}>Grades: {school.grades.split(',').join(', ')}</span>
              </div>
            )}
          </div>
        </div>

        {/* Map Section */}
        {/* <div className={styles.mapContainer}>
            <iframe
              src="https://www.openstreetmap.org/export/embed.html?bbox=-118.24&amp;layer=mapnik"
              width="100%"
              height="300"
              style={{ border: 0 }}
              title="School Location"
              className={styles.mapFrame}
            />
            <div className={styles.mapAttribution}>
              <a href="https://www.openstreetmap.org/#map=15/34.05/-118.24" target="_blank" rel="noopener noreferrer">
                Report a problem
              </a>{' '}
              | © OpenStreetMap contributors
            </div>
          </div>
        </div> */}

        {insights
          .filter((item) => Array.isArray(item.data) && item.data.length > 0)
          .map((item) => (
            <div className="box spacing-my-16" key={item.title}>
              <h3 className={styles.sectionTitle}>{item.title}</h3>
              {item.data.map(([key, group]) => (
                <div key={key} className={styles.program}>
                  <h4 className={styles.programTitle}>{key}</h4>
                  <p className={styles.programDescription}>{Array.isArray(group) ? group.join(', ') : group}</p>
                </div>
              ))}
            </div>
          ))}
      </div>
    </>
  );
};

const SchoolHeader = ({ school, onClose }) => {
  return (
    <div className={styles.schoolHeader}>
      <div className={styles.headerTop}>
        <div className={styles.headerInfo}>
          <h2 className={styles.schoolName}>{school.name}</h2>
          <p className={styles.schoolAddress}>{school.address}</p>
        </div>
        <Button type="text" icon={<CloseOutlined />} onClick={onClose} className={styles.closeButton} />
      </div>
    </div>
  );
};

const FEATURES = {
  UNIFORMS: 'Uniforms',
  LANGUAGE: 'Language Education',
  LANGUAGE_EDUCATION: 'Additional language classes',
  GENDER: 'Gender',
  GENDER_LABEL: 'Coed',
  ACCESSIBILITY_PROGRAMS: 'Accessibility Programs',
  RELIGION: 'Religious Affiliation',
  CORE_KNOWLEDGE: 'Core Knowledge',
  CORE_KNOWLEDGE_LABEL: 'School Focus',
  SCHEDULE: 'Schedule',
  EDUCATIONAL: 'Specific Educational Approach',
  TRADITIONAL: 'Traditional',
  ALTERNATIVE: 'Alternative',
  EDUCATIONAL_LABEL: 'Educational Approach',
  SCHOLARSCHIP_PROGRAMS: 'Scholarship Programs',
  SCHOLARSCHIP_PROGRAMS_LABEL: 'Payment Resources',
  SCHOOL_ACCREDITATIONS: 'School Accreditations',
  GIFTED_STUDENTS_SUPPORT: 'Gifted Students support',
  LANGUAGE_EDUCATION: 'Language Education',
};

const getInsightsFromFeatures = (features) => {
  const generalFeatureData = {};
  const scholarshipFeatureData = {};
  const teachingMethod = {};
  const tuitionFeatures = {};

  features?.forEach((feature) => {
    const groupName = feature.group.name;

    switch (groupName) {
      case FEATURES.GENDER:
        if (!generalFeatureData[FEATURES.GENDER_LABEL]) {
          generalFeatureData[FEATURES.GENDER_LABEL] = [];
        }
        generalFeatureData[FEATURES.GENDER_LABEL] && generalFeatureData[FEATURES.GENDER_LABEL].push(feature.name);

        break;

      case FEATURES.RELIGION:
        if (!generalFeatureData[FEATURES.RELIGION]) {
          generalFeatureData[FEATURES.RELIGION] = [];
        }
        generalFeatureData[FEATURES.RELIGION] && generalFeatureData[FEATURES.RELIGION].push(feature.name);

        break;

      case FEATURES.SCHEDULE:
        if (!generalFeatureData[FEATURES.SCHEDULE]) {
          generalFeatureData[FEATURES.SCHEDULE] = [];
        }
        generalFeatureData[FEATURES.SCHEDULE] && generalFeatureData[FEATURES.SCHEDULE].push(feature.name);

        break;

      case FEATURES.UNIFORMS:
        generalFeatureData[FEATURES.UNIFORMS] = 'Yes';

        break;

      // tuition
      case FEATURES.SCHOLARSCHIP_PROGRAMS:
        if (!tuitionFeatures[FEATURES.SCHOLARSCHIP_PROGRAMS_LABEL]) {
          tuitionFeatures[FEATURES.SCHOLARSCHIP_PROGRAMS_LABEL] = [];
        }
        tuitionFeatures[FEATURES.SCHOLARSCHIP_PROGRAMS_LABEL].push(feature.name);

        break;

      case FEATURES.SCHOOL_ACCREDITATIONS:
        if (!generalFeatureData[FEATURES.SCHOOL_ACCREDITATIONS]) {
          generalFeatureData[FEATURES.SCHOOL_ACCREDITATIONS] = [];
        }
        generalFeatureData[FEATURES.SCHOOL_ACCREDITATIONS].push(feature.name);

        break;

      // School Programs
      case FEATURES.GIFTED_STUDENTS_SUPPORT:
        if (!scholarshipFeatureData[FEATURES.GIFTED_STUDENTS_SUPPORT]) {
          scholarshipFeatureData[FEATURES.GIFTED_STUDENTS_SUPPORT] = [];
        }
        scholarshipFeatureData[FEATURES.GIFTED_STUDENTS_SUPPORT].push(feature.name);

        break;

      case FEATURES.LANGUAGE_EDUCATION:
        if (!scholarshipFeatureData[FEATURES.LANGUAGE_EDUCATION]) {
          scholarshipFeatureData[FEATURES.LANGUAGE_EDUCATION] = [];
        }
        scholarshipFeatureData[FEATURES.LANGUAGE_EDUCATION].push(feature.name);

        break;

      // TeachingMethod
      case FEATURES.EDUCATIONAL:
      case FEATURES.ALTERNATIVE:
      case FEATURES.TRADITIONAL:
        if (!teachingMethod[FEATURES.EDUCATIONAL_LABEL]) {
          teachingMethod[FEATURES.EDUCATIONAL_LABEL] = [];
        }
        teachingMethod[FEATURES.EDUCATIONAL_LABEL] && teachingMethod[FEATURES.EDUCATIONAL_LABEL].push(feature.name);

        break;

      case FEATURES.CORE_KNOWLEDGE:
        if (!teachingMethod[FEATURES.CORE_KNOWLEDGE_LABEL]) {
          teachingMethod[FEATURES.CORE_KNOWLEDGE_LABEL] = [];
        }
        teachingMethod[FEATURES.CORE_KNOWLEDGE_LABEL] &&
          teachingMethod[FEATURES.CORE_KNOWLEDGE_LABEL].push(feature.name);

        break;

      default:
        break;
    }
  });

  return [
    { title: 'General School Features', data: Object.entries(generalFeatureData) },
    { title: 'School Programs', data: Object.entries(scholarshipFeatureData) },
    { title: 'Teaching Method', data: Object.entries(teachingMethod) },
    { title: 'Tuition', data: Object.entries(tuitionFeatures) },
  ];
};

const getStudentsPerTeachersRatio = ({ numberOfStudents, numberOfTeachers }) => {
  if (numberOfStudents && numberOfTeachers) {
    let aux;
    const w = numberOfTeachers;
    const h = numberOfStudents;

    const r = w === 0 ? 1 : w;
    aux = `${parseInt(h / r, 10)} : ${w / r}`;
    return aux;
  }
};
