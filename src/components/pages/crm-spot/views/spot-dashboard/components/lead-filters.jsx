import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import { Flex, Input, Button, Tabs, Badge } from 'antd';
import { SearchOutlined, FilterOutlined, PlusOutlined } from '@ant-design/icons';
import { isSameDay } from 'date-fns';
import { originalLeads } from '../../../mocks/mockLeads';
import { setAssignedMode, toggleAdvancedFilter, toggleNewLead } from 'redux/spot/spot-actions';
import styles from './lead-filters.module.scss';

/**
 * @typedef {Object} LeadFiltersProps
 * @property {string} filterValue - The current search filter value
 * @property {function} handleFilterSearch - Function to handle changes to the search filter
 * @property {string} statusFilter - The currently selected status filter tab
 * @property {function} setStatusFilter - Function to update the status filter
 */

/**
 * LeadFilters component for the SPOT dashboard
 *
 * This component provides filtering functionality for leads in the SPOT dashboard.
 * It includes a search input, buttons for adding leads and advanced filtering,
 * and tabs for filtering leads by status.
 *
 * @param {LeadFiltersProps} props - Component props
 * @returns {JSX.Element} The rendered LeadFilters component
 */
export const LeadFilters = ({ filterValue, handleFilterSearch, statusFilter, setStatusFilter, isAssignedMode }) => {
  /**
   * Determines if there are any incomplete tasks due today across all leads
   *
   * @type {boolean}
   */
  const hasTasksDueToday = useMemo(
    () =>
      originalLeads.some((lead) =>
        lead.tasks?.some((task) => {
          if (task.completed) return false;
          const taskDate = new Date(task.dueDate);
          const today = new Date();
          return isSameDay(taskDate, today);
        })
      ),
    []
  );

  /**
   * Configuration for the status filter tabs
   *
   * @type {Array<{key: string, label: string|JSX.Element}>}
   */
  const items = useMemo(() => {
    let baseItems = [
      { key: 'new', label: 'New' },
      { key: 'attempting-contact', label: 'Attempting Contact' },
      { key: 'working', label: 'Working' },
      // 'matched' will be conditionally included
      { key: 'application-sent', label: 'Application Sent' },
      { key: 'archived', label: 'Archived' },
      {
        key: 'tasks',
        label: (
          <span>
            Tasks
            {hasTasksDueToday && <Badge dot className={styles.badge} />}
          </span>
        ),
      },
    ];

    if (!isAssignedMode) {
      // Insert 'matched' after 'working' if not assigned mode
      baseItems.splice(3, 0, { key: 'matched', label: 'Matched' });
      return baseItems;
    }
    // In assigned mode, add extra tabs and hide 'matched'
    // Insert after 'application-sent'
    const insertIdx = baseItems.findIndex((item) => item.key === 'application-sent') + 1;
    baseItems.splice(
      insertIdx,
      0,
      { key: 'application-received', label: 'Application Received' },
      { key: 'accepted', label: 'Accepted' },
      { key: 'waitlist', label: 'Waitlist' }
    );
    return baseItems;
  }, [isAssignedMode, hasTasksDueToday]);

  return (
    <div className={styles.container}>
      <Flex justify="end" align="flex-end" gap={16}>
        <div style={{ flex: 1 }}>
          <Input
            placeholder={statusFilter === 'tasks' ? 'Search tasks...' : 'Search leads...'}
            prefix={<SearchOutlined />}
            value={filterValue}
            onChange={(e) => handleFilterSearch(e.target.value)}
            className={styles.searchInput}
          />
        </div>
        {/* <div>
          <Flex gap="middle" align="flex-end">
            <Button size="big" icon={<PlusOutlined />} type="primary" onClick={toggleNewLead}>
              Add Lead
            </Button>
            <Button icon={<FilterOutlined />} type="primary" onClick={toggleAdvancedFilter}>
              Filters
            </Button>
          </Flex>
        </div> */}
        <Button icon={<FilterOutlined />} type="primary" onClick={toggleAdvancedFilter}>
          Filters
        </Button>
      </Flex>

      <Flex className="spacing-my-16 border-bottom-gray" align="flex-end">
        <Tabs
          activeKey={statusFilter}
          animated
          onChange={setStatusFilter}
          className="tabs-lead-filters"
          items={items}
          tabBarGutter={16}
          tabBarExtraContent={
            <button className={`${styles.button} ${isAssignedMode && styles.selected}`} onClick={setAssignedMode}>
              Assigned School Leads
            </button>
          }
        />
      </Flex>
    </div>
  );
};

/**
 * PropTypes for the LeadFilters component
 */
LeadFilters.propTypes = {
  /** The current search filter value */
  filterValue: PropTypes.string,
  /** Function to handle changes to the search filter */
  handleFilterSearch: PropTypes.func,
  /** The currently selected status filter tab */
  statusFilter: PropTypes.string,
  /** Function to update the status filter */
  setStatusFilter: PropTypes.func,
};
