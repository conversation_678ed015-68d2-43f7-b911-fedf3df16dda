import React, { useState, useEffect } from 'react';
import { Typography, Drawer, Input, Select, Flex, Button, Card, Space } from 'antd';
import { useSelector } from 'react-redux';
import grades from 'util/grades';
import { _updateLeadBatch } from 'controllers/leads/leads_controller';
import { useUpdateLead } from 'components/pages/crm-spot/hooks/useUpdateLead';
import { MessageOutlined, PhoneOutlined, MailOutlined } from '@ant-design/icons';
import { formatDate } from 'components/pages/crm-spot/utils/format-date';

const { Title, Paragraph, Text } = Typography;

export const LeadEdition = ({ notesHook, open, onClose, getLeadAssignments }) => {
  const { selectedLead } = useSelector((state) => state.spot);

  const [formState, setFormState] = useState(() => ({
    parent_first_name: '',
    parent_last_name: '',
    child_first_name: '',
    child_last_name: '',
    email: '',
    phone: '',
    language: '',
    grade: '',
  }));

  const [noteText, setNoteText] = useState('');

  const { notes, createNode, loading } = notesHook;

  // Update formState when selectedLead changes
  useEffect(() => {
    setFormState({
      parent_first_name: selectedLead?.parent_first_name || '',
      parent_last_name: selectedLead?.parent_last_name || '',
      child_first_name: selectedLead?.child_first_name || '',
      child_last_name: selectedLead?.child_last_name || '',
      email: selectedLead?.email || '',
      phone: selectedLead?.phone || '',
      language: selectedLead?.language || 'english',
      grade: selectedLead?.grade || '',
    });
  }, [selectedLead]);

  const handleInputChange = (id, value) => {
    setFormState((prev) => ({ ...prev, [id]: value }));
  };

  const [isEditing, setIsEditing] = useState(false);

  const { updateLead } = useUpdateLead();
  const handleUpdate = async () => {
    const schoolId = selectedLead.school_id;
    const leadIds = [selectedLead.id];

    try {
      await updateLead({ schoolId, leadIds, values: formState });
      await getLeadAssignments();
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating lead:', error);
    }
  };

  const handleEdit = () => {
    if (isEditing) {
      handleUpdate();
    } else {
      setIsEditing(true);
    }
  };

  const handleAddNote = async () => {
    if (!noteText.trim()) return;
    await createNode({ leadId: selectedLead.id, note: noteText });
    setNoteText('');
  };

  return (
    <Drawer
      open={open}
      size="large"
      placement="right"
      onClose={onClose}
      title={
        <Flex align="center" justify="space-between">
          <Title level={5} style={{ marginBottom: 0 }}>
            Lead Edition
          </Title>
          <Button onClick={handleEdit}>{isEditing ? 'Save' : 'Edit Lead'}</Button>
        </Flex>
      }>
      <div>
        <Card
          title={
            <Title level={5} style={{ margin: 0 }}>
              Lead Information
            </Title>
          }>
          <Paragraph strong style={{ fontSize: '.75rem' }}>
            Parent Information
          </Paragraph>
          <Flex align="center" gap={16}>
            <InputField
              id="parent_first_name"
              label="Parent First Name"
              value={formState.parent_first_name}
              onChange={(e) => handleInputChange('parent_first_name', e.target.value)}
              disabled={!isEditing}
            />
            <InputField
              id="parent_last_name"
              label="Parent Last Name"
              value={formState.parent_last_name}
              onChange={(e) => handleInputChange('parent_last_name', e.target.value)}
              disabled={!isEditing}
            />
          </Flex>

          <Paragraph strong style={{ fontSize: '.75rem' }}>
            Student Information
          </Paragraph>
          <Flex align="center" gap={16}>
            <InputField
              id="child_first_name"
              label="Student First Name"
              value={formState.child_first_name}
              onChange={(e) => handleInputChange('child_first_name', e.target.value)}
              disabled={!isEditing}
            />
            <InputField
              id="child_last_name"
              label="Student Last Name"
              value={formState.child_last_name}
              onChange={(e) => handleInputChange('child_last_name', e.target.value)}
              disabled={!isEditing}
            />
          </Flex>

          <Flex align="center" gap={16}>
            <InputField
              id="email"
              label="Email"
              value={formState.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              disabled={!isEditing}
            />
            <InputField
              id="phone"
              label="Phone"
              value={formState.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              disabled={!isEditing}
            />
          </Flex>

          <Flex align="center" gap={16}>
            <InputField id="language" label="Language">
              <Text>Language</Text>
              <Select
                style={{ width: '100%' }}
                value={formState.language}
                onChange={(value) => handleInputChange('language', value)}
                options={[
                  { value: 'english', label: 'English' },
                  { value: 'spanish', label: 'Spanish' },
                ]}
                disabled={!isEditing}
              />
            </InputField>
            <InputField id="grade" label="Grade">
              <Select
                style={{ width: '100%' }}
                value={formState.grade}
                onChange={(value) => handleInputChange('grade', value)}
                options={grades}
                disabled={!isEditing}
              />
            </InputField>
          </Flex>
        </Card>

        {/* Interaction History Section */}
        <div style={{ marginTop: '24px' }}>
          <Card
            title={
              <Title level={5} style={{ margin: 0 }}>
                Interaction History
              </Title>
            }
            style={{ borderRadius: '8px' }}
            styles={{ body: { padding: '16px', maxHeight: '300px', overflowY: 'auto' } }}>
            <Space direction="vertical" style={{ width: '100%' }} size="large">
              {notes?.length > 0 ? (
                notes?.map((note, index) => {
                  if (note.object_type) {
                    return (
                      <div
                        key={index}
                        style={{
                          display: 'flex',
                          gap: '12px',
                          background: '#f5f5f5',
                          padding: '16px',
                          borderRadius: '8px',
                        }}>
                        <div style={{ marginTop: '4px' }}>{note?.icon}</div>
                        <div style={{ flex: 1 }}>
                          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '4px' }}>
                            <Text strong>{note.user_id}</Text>
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              {formatDate(note.created_at)}
                            </Text>
                          </div>
                          <Text>{note.note}</Text>
                        </div>
                      </div>
                    );
                  }
                })
              ) : (
                <Text>No Interaction Histoy Yet.</Text>
              )}
            </Space>
          </Card>
        </div>

        {/* Add Note Section */}
        <div style={{ marginTop: '24px' }}>
          <Card
            title={
              <Title level={5} style={{ margin: 0 }}>
                Add Note
              </Title>
            }
            style={{ borderRadius: '8px' }}>
            <Input.TextArea
              placeholder="Add your notes here..."
              value={noteText}
              onChange={(e) => setNoteText(e.target.value)}
              style={{ minHeight: '100px', marginBottom: '16px' }}
            />
            <Button type="primary" loading={loading} onClick={handleAddNote}>
              Add Note
            </Button>
          </Card>
        </div>
      </div>
    </Drawer>
  );
};

const InputField = ({ children, id, label, value, disabled, onChange }) => {
  return (
    <label htmlFor={id} style={{ display: 'block', width: '100%', marginBottom: '8px' }}>
      <Text>{label}</Text>
      {children || <Input id={id} name={id} value={value} onChange={onChange} disabled={disabled} />}
    </label>
  );
};
