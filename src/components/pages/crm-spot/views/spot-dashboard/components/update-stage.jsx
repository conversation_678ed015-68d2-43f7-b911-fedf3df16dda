import React, { useState } from 'react';
import { Modal, Button, Input } from 'antd';
import styles from './update-stage.module.scss';
import { useSelector } from 'react-redux';
import { clearSelectedLeads, closeUpdateLead, setSelectedLead } from 'redux/spot/spot-actions';
import { RadioGroup } from 'components/pages/crm-spot/shared-components';
import { useNotification } from 'components/pages/crm-spot/layouts/crm-spot-layout';
import { useUpdateLead } from 'components/pages/crm-spot/hooks/useUpdateLead';
import { useNotes } from 'components/pages/crm-spot/hooks/useNotes';

export const UpdateStage = ({ assignments, userId }) => {
  const { isUpdateLeadOpen, selectedLead, selectedLeads, statusFilter } = useSelector((state) => state.spot);

  const [value, setValue] = useState(statusFilter);

  const { openNotification } = useNotification();

  const { updateLead, loading: leadLoading } = useUpdateLead();

  const [note, setNote] = useState('');
  const { createNode } = useNotes(selectedLead);
  const handleNewNote = async () => {
    if (note.trim()) {
      await createNode({ leadId: selectedLead.id, note });
      setNote('');
    }
  };

  const updateLeadStatus = async () => {
    const schoolId = selectedLead.school_id;
    const leadIds = selectedLeads.length > 0 ? selectedLeads : [selectedLead.id];

    if (['attempting-contact', 'working', 'matched'].includes(value)) {
      await assignments.updateAssignment({
        user_id: userId,
        assignment_id: selectedLead.lead_assignment_id,
        sub_status: value,
      });
      if (selectedLead.status !== 'new') {
        await updateLead({ schoolId, leadIds, values: { status: 'new' } });
      }
    } else {
      const updateRes = await updateLead({ schoolId, leadIds, values: { status: value } });
      if (updateRes.ok) {
        assignments.getLeadAssignments();
      }
    }
    await handleNewNote();

    closeUpdateLead();
    clearSelectedLeads();

    // dispatch UI feedback
    openNotification({
      message: 'Status Updated',
      description: `Updated ${selectedLeads.length > 1 ? 'leads' : 'lead'} to ${value.toLowerCase()} successfully.`,
    });
  };

  const stages = [
    {
      value: 'new',
      label: 'New Lead',
      description: 'Fresh lead that needs initial contact',
    },
    {
      value: 'attempting-contact',
      label: 'Attempting Contact',
      description: 'Actively trying to reach the family',
    },
    {
      value: 'working',
      label: 'Working',
      description: 'In active communication with the family',
    },
    {
      value: 'matched',
      label: 'Matched',
      description: "Family has been matched with a school but hasn't yet committed or applied",
    },
    {
      value: 'application-sent',
      label: 'Application Sent',
      description: 'Application has been sent to family',
    },
    {
      value: 'archived',
      label: 'Archived',
      description: 'Lead is no longer active or has been declined',
    },
  ];

  return (
    <Modal
      title={
        selectedLeads.length > 0
          ? `Bulk Update Leads (${selectedLeads.length} selected)`
          : `Update Stage - ${selectedLead?.id}`
      }
      open={isUpdateLeadOpen}
      onCancel={() => {
        closeUpdateLead();
        setSelectedLead(null);
      }}
      footer={(_, { CancelBtn }) => (
        <>
          <CancelBtn />
          <Button type="primary" loading={assignments.loading || leadLoading} onClick={updateLeadStatus}>
            Update Lead
          </Button>
        </>
      )}>
      <div className={styles.stageContainer}>
        <p className={styles.stageLabel}>Select New Stage</p>
        <RadioGroup options={stages} value={value} setValue={setValue} />
        <label htmlFor="note">Add Note</label>
        <Input.TextArea
          placeholder="Add a note about this stage update"
          value={note}
          onChange={({ target }) => setNote(target.value)}
          rows={4}
        />
      </div>
    </Modal>
  );
};
