import React, { useState } from 'react';
import { Mo<PERSON>, Button, Input } from 'antd';
import styles from './update-stage.module.scss';
import { useSelector } from 'react-redux';
import { clearSelectedLeads, closeUpdateLead, setSelectedLead } from 'redux/spot/spot-actions';
import { RadioGroup } from 'components/pages/crm-spot/shared-components';
import { useNotification } from 'components/pages/crm-spot/layouts/crm-spot-layout';

export const UpdateStage = ({ notesHook, assignments, userId }) => {
  const { isUpdateLeadOpen, selectedLead, selectedLeads, isAssignedMode } = useSelector((state) => state.spot);

  const [value, setValue] = useState(selectedLead?.sub_status || selectedLead?.status);

  const { openNotification } = useNotification();

  const [note, setNote] = useState('');
  const { createNode } = notesHook;
  const handleNewNote = async () => {
    if (note.trim()) {
      await createNode({ leadId: selectedLead.id, note });
      setNote('');
    }
  };

  // handle lead update, dipatch notification and note creation
  const updateLeadStatus = async () => {
    const updatedAssign = await assignments.updateAssignment({ user_id: userId, newValue: value });

    await handleNewNote();

    closeUpdateLead();
    clearSelectedLeads();

    // dispatch UI feedback
    if (updatedAssign) {
      openNotification({
        message: 'Status Updated',
        description: `Updated ${selectedLeads.length > 1 ? 'leads' : 'lead'} to ${value.toLowerCase()} successfully.`,
      });
    }
  };

  const stages = [
    {
      value: 'new',
      label: 'New Lead',
      description: 'Fresh lead that needs initial contact',
    },
    {
      value: 'attempting-contact',
      label: 'Attempting Contact',
      description: 'Actively trying to reach the family',
    },
    {
      value: 'working',
      label: 'Working',
      description: 'In active communication with the family',
    },
    {
      value: 'matched',
      label: 'Matched',
      description: "Family has been matched with a school but hasn't yet committed or applied",
    },
    {
      value: 'application-sent',
      label: 'Application Sent',
      description: 'Application has been sent to family',
    },
    {
      value: 'archived',
      label: 'Archived',
      description: 'Lead is no longer active or has been declined',
    },
  ];

  let extendedStages = stages;
  if (isAssignedMode) {
    // Insert 'application-received'
    const indexArchived = stages.findIndex((s) => s.value === 'archived');
    extendedStages = [
      ...stages.slice(0, indexArchived),
      {
        value: 'application-received',
        label: 'Application Received',
        description: 'Application has been received from the family',
      },
      ...stages.slice(indexArchived, indexArchived + 1),
      {
        value: 'waitlist',
        label: 'Waitlist',
        description: 'Family/student is on the waitlist',
      },
      {
        value: 'accepted',
        label: 'Accepted',
        description: 'Family/student has been accepted',
      },
    ];
  }

  // Hide 'new', 'attempting-contact', 'working' & 'matched' if lead status is not 'new'
  const filteredStages =
    selectedLead?.status !== 'new'
      ? extendedStages.filter((stage) => !['new', 'attempting-contact', 'working', 'matched'].includes(stage.value))
      : extendedStages;

  return (
    <Modal
      title={
        selectedLeads.length > 0
          ? `Bulk Update Leads (${selectedLeads.length} selected)`
          : `Update Stage - ${selectedLead?.id}`
      }
      open={isUpdateLeadOpen}
      onCancel={() => {
        closeUpdateLead();
        setSelectedLead(null);
      }}
      footer={(_, { CancelBtn }) => (
        <>
          <CancelBtn />
          <Button type="primary" loading={assignments.loading} onClick={updateLeadStatus}>
            Update Lead
          </Button>
        </>
      )}>
      <div className={styles.stageContainer}>
        <p className={styles.stageLabel}>Select New Stage</p>
        <RadioGroup options={filteredStages} value={value} setValue={setValue} />
        <label htmlFor="note">Add Note</label>
        <Input.TextArea
          placeholder="Add a note about this stage update"
          value={note}
          onChange={({ target }) => setNote(target.value)}
          rows={4}
        />
      </div>
    </Modal>
  );
};
