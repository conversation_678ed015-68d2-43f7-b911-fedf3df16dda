.drawerContent {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  overflow-y: auto;
  max-height: calc(100vh - 5rem);
}

.schoolHeader {
  padding: 1.5rem;
  border-bottom: 0.0625rem solid #d9d9d9;
  background-color: rgba(0, 80, 108, 0.05);
  display: flex;
  flex-direction: column;
}

.headerTop {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.headerInfo {
  display: flex;
  flex-direction: column;
}

.schoolName {
  font-size: 1.25rem;
  font-weight: 600;
  color: #00506c;
}

.schoolAddress {
  font-size: 0.875rem;
  color: #8c8c8c;
  margin-top: 0.25rem;
}

.closeButton {
  color: #8c8c8c;
  &:hover {
    color: #000;
  }
}

// Section Styles
// Usando la clase box que ya existe globalmente
// Solo añadimos margen entre secciones
.drawerContent {
  :global(.box) {
    &:not(:last-child) {
      margin-bottom: 0;
    }
  }
}

.sectionHeader {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
}

.sectionIcon {
  color: #00506c;
  font-size: 1.125rem;
  margin-right: 0.5rem;
}

.sectionTitle {
  font-size: 1rem;
  font-weight: 600;
  color: #020817;
  margin: 0;
}

.sectionContent {
  color: #64748b;
  font-size: 0.875rem;
  line-height: 1.5;
}

// Key Facts Styles
.keyFactsContent {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.keyFact {
  margin: 0.5rem 0;
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  color: #64748b;
}

.keyFactIcon {
  color: #00506c;
  margin-right: 0.5rem;
  font-size: 0.875rem;
}

.gradesContainer {
  margin-top: 1rem;
  display: flex;
  justify-content: center;
}

.gradesBadge {
  display: inline-block;
  font-size: 0.875rem;
  font-weight: 600;
  color: #000;
  background-color: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 9999px;
  padding: 0.375rem 1rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

// Map Styles
.mapContainer {
  margin-top: 1rem;
  border-radius: 0.5rem;
}

.mapFrame {
  border-radius: 0.5rem;
  width: 100%;
  border: none;
}

.mapAttribution {
  font-size: 0.6875rem;
  color: #6b7280;
  text-align: right;
  margin-top: 0.25rem;

  a {
    color: #00506c;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

// Programs Styles
.programsContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.program {
  background-color: rgba(0, 80, 108, 0.05);
  margin: 1rem 0;
  padding: 0.75rem;
  border-radius: 0.375rem;
}

.programTitle {
  font-size: 0.9375rem;
  font-weight: 600;
  color: #00506c;
  margin: 0 0 0.5rem 0;
  text-align: center;
}

.programDescription {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
  text-align: center;
}

// Transportation Styles
.transportationContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.transportItem {
  background-color: rgba(0, 80, 108, 0.05);
  padding: 0.75rem;
  border-radius: 0.375rem;
}

.transportTitle {
  font-size: 0.9375rem;
  font-weight: 600;
  color: #00506c;
  margin: 0 0 0.25rem 0;
  text-align: center;
}

.transportDescription {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
  text-align: center;
}

// Contacts Styles
.contactsContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.contact {
  background-color: rgba(0, 80, 108, 0.05);
  padding: 0.75rem;
  border-radius: 0.375rem;
}

.contactName {
  font-size: 0.9375rem;
  font-weight: 600;
  color: #020817;
  margin: 0 0 0.25rem 0;
}

.contactTitle {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0 0 0.5rem 0;
}

.contactDetail {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  color: #64748b;
  margin-top: 0.25rem;
}

.contactIcon {
  color: #00506c;
  margin-right: 0.5rem;
  font-size: 0.875rem;
}

// Dates Styles
.datesContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.dateItem {
  background-color: rgba(0, 80, 108, 0.05);
  padding: 0.75rem;
  border-radius: 0.375rem;
}

.dateTitle {
  font-size: 0.9375rem;
  font-weight: 600;
  color: #020817;
  margin: 0 0 0.25rem 0;
  text-align: center;
}

.dateValue {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
  text-align: center;
}
