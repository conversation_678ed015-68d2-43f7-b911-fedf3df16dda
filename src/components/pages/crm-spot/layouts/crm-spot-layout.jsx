import React, { useState, createContext, useContext, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { Button, Badge, Dropdown, Typography, notification } from 'antd';
import { SettingOutlined, HomeOutlined, BellOutlined } from '@ant-design/icons';
import { QueueSelector } from './queue-selector';
import { NotificationModal } from '../shared-components/notification-modal';
import { browserHistory, Link } from 'react-router';
import { _getUserProfileById } from 'controllers/users/users_controller';
import styles from './crm-spot-layout.module.scss';
import { _isSuperadmin, getProfile } from 'util/auth';
import { useUserStatus } from '../hooks/useUserStatus';
import { useNotifications } from '../hooks/useNotifications';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

const NotificationContext = createContext();
export const NotificationProvider = ({ children }) => {
  const [api, contextHolder] = notification.useNotification();

  const openNotification = ({ type = 'success', message = 'Success', description }) => {
    api[type]({ message, description, placement: 'bottomRight' });
  };

  return (
    <NotificationContext.Provider value={{ openNotification }}>
      {contextHolder}
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotification = () => {
  return useContext(NotificationContext);
};

export const CRMSpotLayout = ({ children }) => {
  const isSuperAdmin = _isSuperadmin();
  const userId = getProfile().sub;
  const { updateUserStatus, loading } = useUserStatus(userId);
  const {
    roles,
    isSPOTadmin,
    userStatus: status,
    notifications,
    unreadNotifications,
    tasks,
  } = useSelector((state) => state.spot);

  // Notifications hook
  const {
    loading: notificationsLoading,
    error: notificationsError,
    markAsRead,
    batchNotificationEdit,
    deleteNotification,
    createNotification,
  } = useNotifications(userId);

  // detect expired tasks and dispatch notification:
  useEffect(() => {
    const getExpiredTask = async () => {
      const tasksNotified = getTodayNotifications(notifications);

      if (tasksNotified.length > 0) {
        console.log('not execute');
      } else {
        const { expired, dueToday } = getExpiredTasks(tasks.pending);

        if (expired.length > 0 || dueToday.length > 0) {
          const message = `You have ${expired.length} tasks expired and ${dueToday.length} tasks due today`;
          await createNotification({ message, priority: 'high', type: 'task_reminder' });
        }
      }
    };
    if (tasks && notifications) {
      getExpiredTask();
    }
  }, [tasks, notifications]);

  const [isNotificationModalVisible, setIsNotificationModalVisible] = useState(false);

  const menuItems = [
    {
      key: 'dashboard',
      label: 'SPOT Dashboard',
      path: '/admin/dashboard',
    },
    ...(roles.length > 0 && isSPOTadmin
      ? [
          {
            key: 'leads-management',
            label: 'Lead Management',
            path: '/admin/leads-management',
          },
          {
            key: 'management-console',
            label: 'Management Console',
            path: '/admin/management-console/team-management',
          },
        ]
      : []),
  ];

  const handleMenuClick = (e) => {
    const item = menuItems.find((item) => item.key === e.key);
    if (item && item.path) {
      browserHistory.push(item.path);
    }
  };

  const toggleNotificationModal = () => {
    setIsNotificationModalVisible(!isNotificationModalVisible);
  };

  return (
    <NotificationProvider>
      <div className="layoutv2">
        <div className={styles.bar}>
          <div className={styles.menu}>
            <Link className="" to="/admin/schools">
              <img
                src="https://scholamatch-static.s3.us-west-2.amazonaws.com/ScholaR.svg"
                alt="Schola Logo"
                style={{ width: '110px' }}
              />
            </Link>
            <Link className="nav-link" to="/admin/schools">
              School list
            </Link>
            {isSuperAdmin && (
              <>
                <Link className="nav-link" to="/admin/import">
                  Import Schools
                </Link>
                <Link className="nav-link" to="/admin/assignments">
                  Assignments
                </Link>
              </>
            )}
          </div>
          <div className={styles.menu}>
            <Badge count={unreadNotifications?.length}>
              <Button
                className={styles.notification_button}
                icon={<BellOutlined />}
                size="large"
                onClick={toggleNotificationModal}
              />
            </Badge>

            <QueueSelector userId={userId} status={status} updateUserStatus={updateUserStatus} loading={loading} />

            <Dropdown
              placement="bottomRight"
              menu={{
                items: menuItems,
                onClick: handleMenuClick,
              }}
              trigger={['click']}>
              <Button
                icon={<SettingOutlined />}
                size="large"
                onClick={(e) => e.preventDefault()}
                data-component-path="crm-spot/layouts/crm-spot-layout/settings-button"
              />
            </Dropdown>
          </div>
        </div>
        <main className={styles.container}>{children}</main>
        <NotificationModal
          notifications={notifications}
          unreadNotifications={unreadNotifications}
          markAsRead={markAsRead}
          batchNotificationEdit={batchNotificationEdit}
          deleteNotification={deleteNotification}
          visible={isNotificationModalVisible}
          onClose={toggleNotificationModal}
        />
      </div>
    </NotificationProvider>
  );
};

function getExpiredTasks(tasks) {
  if (!tasks) return;
  const format = 'YYYY-MM-DD';
  const today = dayjs().format(format);

  const expired = tasks.filter((task) => task.due_date && dayjs.utc(task.due_date).format(format) < today);
  const dueToday = tasks.filter((task) => task.due_date && dayjs.utc(task.due_date).format(format) === today);
  return { expired, dueToday };
}

function getTodayNotifications(notifications) {
  if (!notifications) return [];
  const today = dayjs().format('YYYY-MM-DD');
  return notifications.filter(
    (n) => n.created_at && dayjs(n.created_at).format('YYYY-MM-DD') === today && n.type === 'task_reminder'
  );
}
