import { useState, useEffect } from 'react';
import { _getSchoolsAssigned } from 'controllers/lead-assignments/lead-assignments-controller';

export const useSchoolsAssigned = ({ user_id }) => {
  const [schoolsByUser, setSchoolsByUser] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const [schoolId, setSchoolId] = useState(null);
  const onSelectSchool = (id) => setSchoolId(id);

  const fetchSchoolsAssigned = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await _getSchoolsAssigned(user_id);
      const data = await response.json();
      setSchoolsByUser(data || []);
    } catch (err) {
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSchoolsAssigned();
  }, []);

  return {
    schoolsByUser,
    loading,
    error,
    schoolId,
    onSelectSchool,
  };
};
