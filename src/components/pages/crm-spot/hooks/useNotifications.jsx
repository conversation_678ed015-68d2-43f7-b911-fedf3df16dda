import { useState, useEffect, useCallback } from 'react';
import {
  _getUserNotifications,
  _getUserNotificationsUnread,
  _updateUserNoticationStatusSetRead,
  _deleteUserNotication,
  _updateNotificationsBatch,
  _createUserNotification,
} from 'controllers/users-notifications/users-notifications-controller';
import { setUnreadNotifications, setNotifications } from 'redux/spot/spot-actions';

export const useNotifications = (userId) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchNotifications = useCallback(
    async (userIdParam = userId) => {
      setLoading(true);
      setError(null);
      try {
        const response = await _getUserNotifications(userIdParam);
        const data = await response.json();
        setNotifications(data);
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    },
    [userId]
  );

  const fetchUnreadNotifications = useCallback(
    async (userIdParam = userId) => {
      setLoading(true);
      setError(null);
      try {
        const response = await _getUserNotificationsUnread(userIdParam);
        const data = await response.json();
        setUnreadNotifications(data);
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    },
    [userId]
  );

  useEffect(() => {
    if (userId) {
      fetchNotifications();
      fetchUnreadNotifications();
    }
  }, [userId, fetchNotifications, fetchUnreadNotifications]);

  const markAsRead = useCallback(
    async (notificationId) => {
      setLoading(true);
      setError(null);
      try {
        console.log(userId, notificationId);
        const response = await _updateUserNoticationStatusSetRead(userId, notificationId);
        await fetchNotifications();
        await fetchUnreadNotifications();
        return response;
      } catch (err) {
        setError(err);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [userId, fetchNotifications, fetchUnreadNotifications]
  );

  const deleteNotification = useCallback(
    async (notificationId) => {
      setLoading(true);
      setError(null);
      try {
        const response = await _deleteUserNotication(userId, notificationId);
        await fetchNotifications();
        await fetchUnreadNotifications();
        return response;
      } catch (err) {
        setError(err);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [userId, fetchNotifications, fetchUnreadNotifications]
  );

  const batchNotificationEdit = useCallback(
    async (notificationIds = [], key) => {
      setLoading(true);
      setError(null);
      try {
        const response = await _updateNotificationsBatch(userId, notificationIds, key);
        await fetchNotifications();
        await fetchUnreadNotifications();
        return response;
      } catch (err) {
        setError(err);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [userId, fetchNotifications, fetchUnreadNotifications]
  );

  const createNotification = async ({ message, priority, type, school_id }) => {
    setLoading(true);
    setError(null);
    try {
      const response = await _createUserNotification({ user_id: userId, message, priority, type, school_id });
      await fetchNotifications();
      await fetchUnreadNotifications();
      return response;
    } catch (err) {
      setError(err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    getNotifications: fetchNotifications,
    getUnreadNotifications: fetchUnreadNotifications,
    markAsRead,
    deleteNotification,
    batchNotificationEdit,
    createNotification,
  };
};
