import { useState, useEffect, useCallback } from 'react';
import { useSelector } from 'react-redux';
import {
  _getAssignmentsCount,
  _getUserLeadAssignments,
  _updateSubStatus,
  _getSchoolsAssigned,
  _createAssignLead,
} from 'controllers/lead-assignments/lead-assignments-controller';
import { useUpdateLead } from './useUpdateLead';

/**
 * @typedef {Object} LeadAssignmentsParams
 * @property {string} user_id
 * @property {string} status
 * @property {string} createdAt_from
 * @property {string} createdAt_to
 * @property {Array<string>} grades
 * @property {Array<string>} sources
 * @property {string} search_notes
 * @property {boolean} assigned_schools
 */
export const useLeadAssignments = ({
  user_id,
  status,
  createdAt_from,
  createdAt_to,
  grades,
  sources,
  search_notes,
  assigned_schools,
  school_id,
  page,
  pageSize,
}) => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchLeadAssignments = async () => {
    if (status === '') return;

    setLoading(true);
    setError(null);
    try {
      const response = await _getUserLeadAssignments({
        user_id,
        status,
        createdAt_from,
        createdAt_to,
        grades,
        sources,
        search_notes,
        assigned_schools,
        school_id,
        page,
        pageSize,
      });
      const data = await response.json();
      setData(data || []);
    } catch (err) {
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLeadAssignments();
  }, [status, createdAt_from, createdAt_to, grades, search_notes, assigned_schools, school_id, page, pageSize]);

  const { selectedLead, selectedLeads } = useSelector((state) => state.spot);
  const { updateLead, loading: leadLoading } = useUpdateLead();

  const updateAssignment = async ({ user_id, newValue }) => {
    setLoading(true);
    setError(null);
    try {
      const schoolId = selectedLead.school_id;
      const leadIds = selectedLeads.length > 0 ? selectedLeads : [selectedLead.id];

      if (!selectedLead.lead_assignment_id) {
        await _createAssignLead({
          assignedUserId: user_id,
          leadId: selectedLead.id,
          subStatus: newValue,
          assignmentType: 'school_assigned',
        });
        await fetchLeadAssignments();
        return true;
      }

      const subStages = ['attempting-contact', 'working', 'matched'];
      if (subStages.includes(newValue)) {
        await _updateSubStatus({ user_id, assignment_id: selectedLead.lead_assignment_id, sub_status: newValue });
      } else {
        await updateLead({ schoolId, leadIds, values: { status: newValue } });
        await _updateSubStatus({ user_id, assignment_id: selectedLead.lead_assignment_id, sub_status: '' });
      }
      await fetchLeadAssignments();
      return true;
    } catch (err) {
      setError(err);
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    data,
    loading,
    error,
    getLeadAssignments: fetchLeadAssignments,
    updateAssignment,
  };
};
