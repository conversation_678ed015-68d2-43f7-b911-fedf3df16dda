import { useState, useEffect, useCallback } from 'react';
import {
  _getAssignmentsCount,
  _getUserLeadAssignments,
  _updateSubStatus,
} from 'controllers/lead-assignments/lead-assignments-controller';

/**
 * @typedef {Object} LeadAssignmentsParams
 * @property {string} user_id
 * @property {string} status
 * @property {string} createdAt_from
 * @property {string} createdAt_to
 * @property {Array<string>} grades
 * @property {Array<string>} sources
 * @property {string} search_notes
 * @property {boolean} assigned_schools
 */
export const useLeadAssignments = ({
  user_id,
  status,
  createdAt_from,
  createdAt_to,
  grades,
  sources,
  search_notes,
  assigned_schools,
  page,
  pageSize,
}) => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchLeadAssignments = async () => {
    if (status === '') return;

    setLoading(true);
    setError(null);
    try {
      const response = await _getUserLeadAssignments({
        user_id,
        status,
        createdAt_from,
        createdAt_to,
        grades,
        sources,
        search_notes,
        assigned_schools,
        page,
        pageSize,
      });
      const data = await response.json();
      setData(data || []);
    } catch (err) {
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLeadAssignments();
  }, [status, createdAt_from, createdAt_to, grades, search_notes, assigned_schools, page, pageSize]);

  const updateAssignment = async ({ user_id, assignment_id, sub_status }) => {
    setLoading(true);
    setError(null);
    try {
      const response = await _updateSubStatus(user_id, assignment_id, sub_status);
      const data = await response.json();
      // refetch call
      await fetchLeadAssignments();
      return data;
    } catch (err) {
      setError(err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    data,
    loading,
    error,
    getLeadAssignments: fetchLeadAssignments,
    updateAssignment,
  };
};

export const useAssignmentsCount = ({ user_id }) => {
  const [counts, setCounts] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchAssignmentsCount = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await _getAssignmentsCount(user_id);
      const data = await response.json();
      setCounts(data || null);
    } catch (err) {
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAssignmentsCount();
  }, []);

  return {
    counts,
    loading,
    error,
    getAssignmentsCount: fetchAssignmentsCount,
  };
};
