import { useEffect, useState } from 'react';
import { _getSchoolById } from 'controllers/schools/schools_controller';

export const useSchool = (schoolId) => {
  const [school, setSchool] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!schoolId) return;
    setLoading(true);
    setError(null);
    _getSchoolById(schoolId)
      .then(async (response) => {
        if (!response.ok) throw new Error('No se pudo obtener la escuela');
        const data = await response.json();
        setSchool(data);
      })
      .catch((err) => setError(err))
      .finally(() => setLoading(false));
  }, [schoolId]);

  return { school, loading, error };
};
