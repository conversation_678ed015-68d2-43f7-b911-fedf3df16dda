import { useEffect, useState } from 'react';
import {
  _getLeadsManagement,
  _createAssignLead,
  _reassignLead,
} from 'controllers/lead-assignments/lead-assignments-controller';

export const useUnassignmentsLeads = ({ assignedStatus, status, schoolId, page, pageSize }) => {
  const [unassignmentsLeads, setUnassignmentsLeads] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchUnassignmentsLeads = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await _getLeadsManagement({ assignedStatus, status, schoolId, page, pageSize });
      const data = await response.json();
      setUnassignmentsLeads(data);
    } catch (err) {
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUnassignmentsLeads();
  }, [assignedStatus, status, schoolId, page, pageSize]);

  const createAssignment = async ({ assignedUserId, leadId }) => {
    setLoading(true);
    setError(null);
    try {
      const response = await _createAssignLead({ assignedUserId, leadId });
      const data = await response.json();
      // refetch call
      await fetchUnassignmentsLeads();
      return data;
    } catch (err) {
      setError(err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const changeAssignment = async ({ new_user_id, assignment_id }) => {
    setLoading(true);
    setError(null);
    try {
      const response = await _reassignLead(new_user_id, assignment_id);
      const data = await response.json();
      // refetch call
      await fetchUnassignmentsLeads();
      return data;
    } catch (err) {
      setError(err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    unassignmentsLeads,
    loading,
    error,
    fetchUnassignmentsLeads,
    createAssignment,
    changeAssignment,
  };
};
