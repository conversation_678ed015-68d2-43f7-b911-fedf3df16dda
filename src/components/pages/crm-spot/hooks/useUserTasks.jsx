import { useState, useEffect, useCallback } from 'react';
import { _getUserTasks, _createUserTask, _updateUserTaskStatus } from 'controllers/users-tasks/users-tasks-controller';
import { setTasks } from 'redux/spot/spot-actions';

export const useUserTasks = (userId, status = '') => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchTasks = useCallback(
    async (userIdParam = userId, statusParam = status) => {
      setLoading(true);
      setError(null);
      try {
        const response = await _getUserTasks(userIdParam, statusParam);
        const data = await response.json();

        setTasks({
          pending: data.filter((task) => task.status === 'pending'),
          completed: data.filter((task) => task.status === 'completed'),
        });
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    },
    [userId, status]
  );

  useEffect(() => {
    if (userId) {
      fetchTasks();
    }
  }, [userId, status, fetchTasks]);

  const createUserTask = useCallback(
    async (leadId, task, dueDate) => {
      setLoading(true);
      setError(null);
      try {
        const response = await _createUserTask(userId, leadId, task, dueDate);
        // refetch
        await fetchTasks();
        return response;
      } catch (err) {
        setError(err);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [userId, fetchTasks]
  );

  const updateUserTaskStatus = useCallback(
    async (taskId, newStatus) => {
      setLoading(true);
      setError(null);
      try {
        const response = await _updateUserTaskStatus(userId, taskId, newStatus);
        // refetch
        await fetchTasks();
        return response;
      } catch (err) {
        setError(err);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [userId, fetchTasks]
  );

  return {
    loading,
    error,
    getUserTasks: fetchTasks,
    createUserTask,
    updateUserTaskStatus,
  };
};
