import { useEffect, useState } from 'react';
import { _getSMschools } from 'controllers/lead-assignments/lead-assignments-controller';

export const useSchoolsMatch = (scholamatch_url) => {
  if (!scholamatch_url) {
    return;
  }
  const { zip, grade, schooltypes, features } = getMatchParams(scholamatch_url);

  const [schools, setSchools] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const getSchools = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await _getSMschools({
        zip,
        grade,
        features,
        school_types: schooltypes,
      });
      const data = await response.json();
      setSchools(data);
    } catch (err) {
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getSchools();
  }, []);

  return { schools, loading, error };
};

const getMatchParams = (url) => {
  const params = new URLSearchParams(url.split('?')[1]);
  const zip = params.get('zipCode');
  const grade = params.get('grade');

  const question1SchoolType = {
    a: ['public charter', 'public district', 'public magnet'],
    b: ['private'],
    c: ['public district', 'public magnet', 'public charter', 'private'],
    d: ['virtual/online', 'special', 'vocational'],
  };

  /** subjects: math and science, history and social, art and music, reading and writing */
  const question2Features = {
    a: [69, 182, 285, 72, 71, 74, 229, 92, 219, 252, 90, 91, 215, 236, 551], // math and science
    b: [210, 445, 224, 241, 199, 354, 186, 46, 31, 250, 205, 99, 440, 507, 295, 199, 354, 440], // art and music
    c: [210, 223, 100, 181], // history and social studies
    d: [76, 203, 95, 194, 254, 239, 248], // reading and writing
  };
  /** learning preferences */
  const question3Features = {
    a: [4, 6, 8, 356, 368, 388, 372, 369], // Doing things hands-on
    b: [15, 387, 403], // Talking and working in groups
    c: [418, 3, 435, 395, 367, 409], // Reading and working independently through problems
    d: [14, 7, 192, 102], // Watching videos and seeing things visually
  };
  /** extracurricular activities: sports, technology, music, arts etc */
  const question4Features = {
    a: [
      441, 67, 48, 49, 443, 547, 50, 266, 51, 277, 191, 269, 273, 279, 270, 268, 53, 54, 55, 56, 267, 275, 278, 274, 57,
      510, 272, 345, 58, 281, 263, 59, 60, 319, 376, 66, 280, 61, 62, 264, 63, 64, 282, 65, 550, 313,
    ], // sports
    b: [520, 295, 315, 307, 415, 507, 329, 328, 283, 337, 416, 335, 349, 324, 332, 287, 302, 527, 326, 288, 554], // arts and crafts
    c: [322, 438, 544, 373, 301, 545, 289, 293], // technology ot gaming clubs
    d: [311, 285, 292], // academic-focused clubs
    e: [294, 442, 317, 524], // music or drama
    f: [296, 522, 321, 309, 432, 334], // helping in the community
    g: [],
  };
  /** special needs, support programs, additional offerings */
  const question5Features = {
    a: [103, 105, 506, 111, 106, 107, 108, 109, 110, 112, 113, 114, 115], // specific needs support
    b: [509, 359, 508, 502, 444], // support programs
    c: [424, 364, 536, 417, 537, 370, 521], // additional offerings
    d: [],
  };
  /** languages needs */
  const question6Features = {
    a: [
      118, 171, 172, 178, 125, 177, 174, 175, 231, 120, 121, 122, 123, 124, 126, 451, 402, 170, 179, 242, 173, 86, 256,
      400, 255, 176, 401, 385, 180, 119,
    ], // languages classes
    b: [343, 357, 540, 404, 444, 514], // english language learning support
    c: [],
  };
  /** specialized school programs */
  const question7Features = {
    a: [504, 514], // gifted programs
    b: [142, 371], // advanced courses
    c: [557, 327], // JROTC
    d: [],
  };

  const question9Environment = {
    a: [342, 513, 523, 526, 553], // In-person
    b: [447, 9, 446, 408, 503], // Online - Alternative
    c: [342, 513, 523, 526, 553, 447, 446, 408, 503, 9], // Blended
    d: [342, 513, 447, 9, 446, 408, 503, 523, 526, 553], // All
  };

  // Collect all feature ids from questions 2, 3, 4, 5, 6, 7, 9
  const getAllFeatures = () => {
    const features = [];
    // Helper to push all values from a question's selected option
    const pushFeature = (questionObj, qKey) => {
      const val = params.get(qKey);
      if (val && Array.isArray(questionObj[val])) {
        features.push(...questionObj[val]);
      }
    };
    pushFeature(question2Features, 'question2');
    pushFeature(question3Features, 'question3');
    pushFeature(question4Features, 'question4');
    pushFeature(question5Features, 'question5');
    pushFeature(question6Features, 'question6');
    pushFeature(question7Features, 'question7');
    pushFeature(question9Environment, 'question9');
    // remove filter before send to PROD
    // return features.filter((item) => item < 464);
    return features;
  };

  const question1 = params.get('question1');
  const schooltypes = question1SchoolType[question1]?.join(',');

  const features = getAllFeatures();

  return { zip, grade, schooltypes, features };
};
