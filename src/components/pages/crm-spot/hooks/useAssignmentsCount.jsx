import { useState, useEffect } from 'react';
import { _getAssignmentsCount } from 'controllers/lead-assignments/lead-assignments-controller';

export const useAssignmentsCount = ({ user_id }) => {
  const [counts, setCounts] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchAssignmentsCount = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await _getAssignmentsCount(user_id);
      const data = await response.json();
      setCounts(data || null);
    } catch (err) {
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAssignmentsCount();
  }, []);

  return {
    counts,
    loading,
    error,
    getAssignmentsCount: fetchAssignmentsCount,
  };
};
