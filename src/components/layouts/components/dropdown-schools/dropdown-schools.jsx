import { DownOutlined } from '@ant-design/icons';
import { Link } from 'react-router';
import styles from '../header-school.module.scss';
import { useToggle } from 'hooks/useToggle';
import { useClickOutside } from 'hooks/useClickOutside';

export const DropDownSchools = ({ schools, currentSchool, isSuperadmin }) => {
  const [isShowDrop, toggleDrop] = useToggle(false);

  const schoolsDropRef = useClickOutside(() => {
    if (isShowDrop) {
      toggleDrop();
    }
  });

  const schoolLogo = currentSchool?.logo_image || '/crest.png';

  return (
    <div id="drop schools" className={styles.schoolDropMenu} ref={schoolsDropRef} onClick={toggleDrop}>
      <div>
        <img src={schoolLogo} alt="school logo" />
      </div>
      <span className={styles.schoolName}>{currentSchool?.name}</span>
      <div>
        <DownOutlined />
      </div>
      {isShowDrop && (schools?.results?.length > 0 || isSuperadmin) && (
        <div className={styles.dropMenuSchools}>
          <ul className={styles.schoolsListingMenu}>
            {schools?.results?.map((school) => (
              <li
                className={`${styles.item} ${school.id === currentSchool?.id && styles.active}`}
                onClick={() => {
                  window.location.replace(`/admin/v2/schools/${school?.id}/dashboard`);
                }}>
                {school.name}
              </li>
            ))}
            {isSuperadmin && (
              <Link key={'schoollist'} className={styles.link} to={`/admin/schools`}>
                <li className={`${styles.item} ${styles.all}`}>View All</li>
              </Link>
            )}
          </ul>
        </div>
      )}
    </div>
  );
};
