import client from '../client';

export const _createUserNotification = async ({ user_id, school_id, message, priority, type }) => {
  return await client.post(
    `user-notifications/${school_id}/${user_id}`,
    { message, priority, type },
    { credentials: 'omit' }
  );
};

export const _deleteUserNotication = async (user_id, id) => {
  return await client.patch(`user-notifications/${user_id}/${id}/delete`, {}, { credentials: 'omit' });
};

export const _updateUserNoticationStatusSetRead = async (user_id, id) => {
  return await client.patch(`user-notifications/${user_id}/${id}/read`, {}, { credentials: 'omit' });
};

export const _updateNotificationsBatch = async (user_id, notificationIds, key) => {
  return await client.patch(`user-notifications/${user_id}`, { notificationIds, key }, { credentials: 'omit' });
};

export const _getUserNotifications = async (user_id) => {
  return await client.get(`user-notifications/${user_id}`, {}, { credentials: 'omit' });
};

export const _getUserNotificationsUnread = async (user_id) => {
  return await client.get(`user-notifications/${user_id}?unreadOnly=true`, {}, { credentials: 'omit' });
};
