import moment from 'moment'
import _, { snakeCase } from 'lodash'
import PdfPrinter from 'pdfmake'
import { Input, stringify } from 'csv-stringify/sync'
import archiver from 'archiver'
import { Readable } from 'stream'
import axios from 'axios'
import { trackEventFP } from '@connectors/freshpaint-services'
import {facebookUtils} from '@utils'
import { v4 as uuidv4 } from 'uuid'
// Import controllers
import { notesController, schoolController, usersController, applicationsController, urlController, reasonsController } from '@controllers'

//Import object schemas
import * as schemas from '@schemas'

// Import utils functions
import {
  getObjectValues,
  buildBulkInsert,
  buildUpdate,
  insertLogChange,
  sendApplicationToLead,
  marketingCampaignExecuteTrigger,
  statusToTitleCase,
  addLogEventAsNote,
  validate,
  sendEmail,
  schoolUtils,
  findStateByZipcode,
  searchHistory,
  leadUtils,
  sendSms,
  toTitleCase,
  googleSearches
} from '@utils'

// Import error handlers
import {serverError, notFoundError, unauthorizedError, conflictError} from '@errors'
import { ConflictError } from '@errorClasses'

// Import queries
import {
  leads_queries as queries,
  schools_queries,
  leads_transportation_queries,
  insertQuery,
  selectQueryIn,
  leads_queries,
  user_schools_queries,
  notes_queries,
  lottery_queries,
  search_history_queries as sh_queries
} from '@queries'

// Import postgres connector
import postgres from '@connectors/postgres'

// Import auth0 connector
import * as auth0 from '@connectors/auth0-functions'

import {
  S3_BUCKET,
  STATUSES,
  SPOT_EMAIL,
  REMINDER_PERIODS,
  LEAD_TRANSPORTATION_REASONS,
  getSystemAdminLeadSources,
  getSystemLeadSources,
  getSystemScholaMatch,
  getSystemGeneralLead,
  getSystemScholaMatch2,
  getSystemScholaSchoolCampaign,
  getSystemScholaMatch3,
  getSystemPotentialLeadsRO,
  getSystemAutoScholaMatch,
  getSystemScholaPreQualify,
  getSystemScholaMatchSPOT,
  APPLICATION_EXPORT_COLUMNS,
  APPLICATION_PRINT_FONTS,
  LEAD_CSV_EXPORT_COLUMNS,
  FRONTEND_HOST_ADMIN,
  SCHOLA_CONSTANTS,
  INFO_RECIPIENT,
  FRONTEND_HOST_SEARCH,
  SCHOOL_ACCOUNT_TYPES,
  SALES_RECIPIENT,
  LEAD_STATUSES,
  SCHOLA_TIERS,
  INVALID_NAMES,
  GRADE_NAMES,
  VALID_CONTACT,
  SCHOOLS_TO_AUTOMATCH_2,
  DEFAULT_SCHOOL_CS,
  REROUTE_5,
  FEEDBACK_SURVEY,
  BEN_CEO_CONTACT,
  TWILIO_FROM,
  FROM_EMAIL,
  getSystemScholaProfileA,
  LOGGER,
  PREVENT_RUNNING_ASM,
  REACTIVATE_CAMPAIGN_SOURCES,
  getSystemScholaGeneralCampaign,
  SCHOOLS_WITH_LOTTERY,
  SCHOOLS_WITH_INTEGRATIONS,
  NODE_ENV,
} from '@constants'

// Create logger
import { getLogger } from '@config/logger'
const logger = (LOGGER == 'cloudwatch') ? console : getLogger({ service: 'controllers/leads' })

// Import interfaces
import {
  Lead,
  SubmitApplicationWithSchoolId,
  LeadValues,
  LeadTransportation,
  Transportation,
  LeadAdditionalData,
  PrettyJotFormParam,
  JotFormAddress,
  EmergencyContact,
  DoctorAddress,
  LeadSource,
  PotentialLeadCount,
  PotentialLeadCountFilters,
  LeadsPost,
  HistoricalLeadData,
  LeadEmailContext,
  EditLeadForSchool,
  EmailParams,
  LeadsHistory,
  GetSchoolIdLeads,
  LeadFields,
  PotentialLeadsGet,
  AutoScholaMatchChild,
  AutoScholaMatchParent,
  LeadsFromFB,
  AutoScholaMatchRes,
  AutoScholaMatchLead,
  School,
  LeadSourceCostGet,
  UserProfileLeads,
  LeadCountByStatus,
  LeadSourceCostPost,
  LeadSourceCostsPatch,
  LeadTransportationResponse,
  LeadsPrint,
  ApplicationsForPrint,
  IObjectKeys,
  LeadFieldDB,
  LeadColumnObject,
  LeadCSVColumnNames,
  ExportApplicationsForSchool,
  ApplicationFields,
  ApplicationExtra,
  ApplicationExtraField,
  CSVAndNow,
  LeadCustomImport,
  LeadStatus,
  LeadFB,
  FeedbackSurvey,
  JotformLeadSubmitted,
  SendScholaMatchJotform,
  SchoolMintProcess,
  LeadAIResponses,
  CustomSurvey,
  RemovedLeadSource,
  RemovedLeadStatus,
  SchoolMintWebScraper,
  LeadIntegrations,
  SubmitApplicationExternal,
  MergeDupLeads,
  SchoolMintMatch,
} from '@interfaces'

// This template is not used because we are sending the same template for Claimed cases
// import enrrolmentInterestNotificationClaimedUnpaid from '@email-templates/enrollment-interest-notification-claimed-unpaid'
import enrrolmentInterestNotificationClaimedPaid from '@email-templates/enrollment-interest-notification-claimed-paid'
import enrrolmentInterestNotificationUnclaimed from '@email-templates/enrollment-interest-notification-unclaimed'
import feedbackSurveyEmailES from '@email-templates/feedback-survey-es'
import feedbackSurveyEmailEN from '@email-templates/feedback-survey-en'
import schoolmintAppNotification from '@email-templates/schoolmint-app-notification'
import smEmailUnresponsiveLeadsEn from '@email-templates/scholamatch-email-to-unresponsive-leads-en'
import smEmailUnresponsiveLeadsEs from '@email-templates/scholamatch-email-to-unresponsive-leads-es'
import customSurveyEmail from '@email-templates/custom-survey'

import getContactSPOTTemplate from '@email-templates/contact-spot'

// Import html generator
import lead_reminder_1_day from '@email-templates/lead-reminder-1-day'
import lead_reminder_3_days from '@email-templates/lead-reminder-3-days'
import lead_reminder_10_days from '@email-templates/lead-reminder-10-days'
import lead_reminder_1_week from '@email-templates/lead-reminder-1-week'
import lead_reminder_2_weeks from '@email-templates/lead-reminder-2-weeks'
import { param } from 'swagger-router'
import { LOTTERY_SUBSTAGE } from '../constants/lottery'

/**
 * Leads controller functions
 * Used by
 * /src/routes/leads
 * /src/controllers/applications
 */

export async function createLead(lead_values: LeadValues, application_params?: SubmitApplicationWithSchoolId) {
  try {
    let send_application = (lead_values?.send_application == undefined ? true : false)
    delete lead_values?.send_application

    if (lead_values?.zipcode && !lead_values?.latitude && !lead_values?.longitude) {
      const { direction, zipcode, address, city, state_short } = await googleSearches.searchByZipState(lead_values.zipcode, lead_values?.address)
      lead_values['address'] = address
      lead_values['zipcode'] = zipcode
      lead_values['latitude'] = direction?.lat
      lead_values['longitude'] = direction?.lon
      lead_values['city'] = city
      lead_values['state'] = state_short
      lead_values['address_description'] = address
    }

    if(lead_values.note) {
      const note = JSON.parse(JSON.stringify(lead_values.note))
      if (note.integration && (note.integration == 'jotforms' || note.integration == 'formsort')) {
        lead_values.application_source = note.integration
      }
      if(note.formId) {
        lead_values.application_form_id = note.formId
      }
    }
    const {logged_id} = lead_values
    delete lead_values.logged_id
    if(lead_values.language) {
      const language = lead_values.language.toLowerCase()
      lead_values.language = language
      if(language === 'en') lead_values.language = 'english'
      if(language === 'es') lead_values.language = 'spanish'
    }
    let transportation:Transportation
    if(lead_values.transportation) {
      transportation = Object.assign({}, lead_values.transportation) as Transportation
    }
    delete lead_values.transportation

    let additional_data: LeadAdditionalData
    if(lead_values.additional_data) {
      additional_data = Object.assign({}, lead_values.additional_data) as LeadAdditionalData
    }
    delete lead_values.additional_data

    const integrations= {} as LeadIntegrations
    integrations.meta_leadgen_id = lead_values.meta_leadgen_id || ''
    integrations.meta_fbclid = lead_values.meta_fbclid || ''
    integrations.google_gclid = lead_values.google_gclid || ''
    delete lead_values.meta_leadgen_id
    delete lead_values.meta_fbclid
    delete lead_values.google_gclid

    const leads: Array<Lead> = []
    let old_lead = {} as LeadValues
    let operation_type = 'add'
    // Get auth0 user by email
    const user = await auth0.getUserByEmail(lead_values.email)

    // if the lead_values doesn't contains user_id we set the user_id from the past request
    if(!lead_values?.user_id){
      lead_values.user_id = user?.user_id
    }

    // Get lead
    lead_values.user_student_id = lead_values.student_id
    delete lead_values.student_id
    if(lead_values.id) {
      const lead_response = await postgres.query(queries.getLead(), [lead_values.id, lead_values.school_id])
      old_lead = lead_response.rows[0]
      if(user?.user_id) {
        lead_values.user_id = user.user_id
      }
    }
    if(old_lead && !_.isEmpty(old_lead)) {
      lead_values.status = old_lead.status
      // Update lead
      const condition = { id: old_lead.id}
      lead_values.updated_at = moment.utc()
      const built_update = buildUpdate('leads', condition, lead_values)
      console.log('built_update', built_update)
      const updated_lead = await postgres.query(built_update.text, built_update.values)
      leads.push(updated_lead.rows[0])
      operation_type = 'edit'
    } else {
      // support received source
      if(lead_values.source && !lead_values.lead_source_id) {
        lead_values.lead_source_id = (await getOrCreateLeadSourceByName(lead_values.school_id, lead_values.source)).id
        delete lead_values.source
      }
      // Create lead
      const now = moment.utc()
      lead_values.created_at = lead_values.created_at ? moment.utc(lead_values.created_at) : now
      lead_values.updated_at = now
      let values:any = await getObjectValues(lead_values)
      const new_lead = await postgres.query(
        insertQuery('leads', lead_values), values
      )
      leads.push(new_lead.rows[0])

      // send all 'Schola Profile A' to general school too request from DJ, Ale, Juan
      if (lead_values.lead_source_id == -10 && NODE_ENV == 'production') {
        const lead_values_tmp = { ...lead_values, school_id: 270990 }
        values = await getObjectValues(lead_values_tmp)
        const new_lead_gs = await postgres.query(
          insertQuery('leads', lead_values_tmp), values
        )
        leads.push(new_lead_gs.rows[0])
      }
    }

    // done to accept more than one leads in the array
    for(const lead of leads){
      // Fill old lead with keys for all the updated/created lead fields
      Object.keys(lead).map(key => {
        const value = lead[key]
        if(Array.isArray(value)) {
          lead[key] = JSON.stringify(value)
        }
        if(!(key in old_lead)) {
          old_lead[key] = undefined
        }
      })
      // Insert log change
      const log_changes_params = {
        school_id: lead.school_id,
        user_id: lead.user_id,
        table_name: 'leads',
        row_id: lead.id,
        operation_type,
        old_values: old_lead,
        new_values: lead,
        created_at: moment().toDate()
      }
      await insertLogChange(log_changes_params)
      if(transportation) {
        await this.insertOrUpdateLeadTransportation({
          school_id: application_params?.school_id || lead_values.school_id,
          lead_id: lead.id,
          transportation:{
            ...transportation,
            need: transportation.need || 'No', // This is not present in application_params
            carpool: transportation.carpool || 'No', // This is not present in application_params
            free_transport_preferred: transportation.free_transport_preferred || 'No', // This is not present in application_params
            free_transport_preferred_other: transportation.free_transport_preferred_other || '' // This is not present in application_params
          },

        })
      }
      if(additional_data) {
        await this.insertLeadAdditionalData({
          school_id: application_params?.school_id || lead_values.school_id,
          lead_id: lead.id,
          student_zipcode: additional_data.zipcode,
          student_residence_address: additional_data.residence_address,
          student_residence_address2: additional_data.residence_address2,
          student_residence_type: additional_data.residence_type,
          student_lives_with: additional_data.lives_with,
          reasons_for_leaving: additional_data.reasons_for_leaving
        })
      }
    }

    const lead = leads[0]

    if (operation_type === 'add' && (lead?.user_id != 'system-schoolmint')){
      integrations.lead_id=lead.id
      await insertLeadIntegrations(integrations)
      if(integrations.meta_leadgen_id) {
        await trackEventFP('MetaFormLead', lead.id.toString(), {  distinct_id: lead.id.toString(), lead_id: lead.id, leadgen_id: integrations.meta_leadgen_id})
        await facebookUtils.createConversionEvent({eventName: 'Lead', payload: { lead_id: lead.id,  email: lead.email, phone: lead.phone, leadgen_id: integrations.meta_leadgen_id }})
      } else if(integrations.meta_fbclid) {
        await trackEventFP('MetaWebLead', lead.id.toString(), {   distinct_id: lead.id.toString(), lead_id: lead.id, fbclid: integrations.meta_fbclid})
        // the server event require a lead_id (leadgen_id)
        //await facebookUtils.createConversionEvent({eventName: 'Lead', payload: { lead_id: lead.id, email: lead.email, phone: lead.phone, fbclid: integrations.meta_fbclid }})
      }
    }
    if(operation_type === 'add' && lead.imported) {
      //set to false to skip executions of campaigns
      send_application = false
      console.log('Lead imported, skipping campaigns')
    }
    if(send_application){
      const scope = application_params?.scope || ['']
      const application = await sendApplicationToLead({ ...lead, logged_id }, false, scope)
      const application_url = application.short_url
      // Process lead when add schola system
      await marketingCampaignExecuteTrigger.executeTriggers(
        lead,
        application_url
      )
    }
    // Add note when lead added
    await addNoteWhenLeadAdded(lead)

    // salesforce process in case the lead source id came with -2
    // if (lead.lead_source_id == -2) {
    //   // return await salesforceController.creationParentStudentsHandler(lead)
    //   const values = await getObjectValues(lead) as Array<string>
    //   await googleSpreadSheetAppend([values], ASM_SHEET_ID, ASM_SHEET_RANGE, ASM_CLIENT_EMAIL, ASM_P_KEY)
    // }

    return lead
  } catch (error) {
    logger.error('createLeadError:', error)
    if (error.message.includes('leads_duplicated'))
      throw new ConflictError('duplicated')
    throw serverError
  }
}

export async function addNoteWhenLeadAdded(params: Lead) {
  try {
    const date = moment.utc().format('MM/DD/YYYY HH:mm UTC')
    const status_name = statusToTitleCase(params.status)
    const created_by = params.created_by || ''
    if(created_by) {
      const user = await auth0.getUser(created_by)
      let user_email = ''
      if(user) {
        user_email = user.email
      }
      // Add note
      const note = status_name ? `Lead added with status '${status_name === 'New'? 'Incoming Leads': status_name}' - ${date} - by ${user_email}` : `Lead entering as 'Incoming Leads' request information was sent by ${user_email} on ${date}`
      await notesController.createNote({
        object_type: 'lead',
        object_id: params.id,
        user_id: user_email,
        note
      })
    }
    return true
  } catch (error) {
    logger.error('addNoteWhenLeadAddedError:', error)
    throw serverError
  }
}

export async function insertOrUpdateLeadTransportation(params: LeadTransportation) {
  try {
    //Get lead transportation by lead id
    let db_response = await postgres.query(leads_transportation_queries.getLeadTransportationIdByLeadId(), [params.school_id, params.lead_id])
    let lead_transportation
    let lead_transportation_id: number

    const { reasons } = params.transportation
    const now = moment().toDate()

    if(!db_response.rowCount) {
      // Create lead transportation
      const insert_params = {
        school_id: params.school_id,
        lead_id: params.lead_id,
        need: params.transportation.need,
        carpool: params.transportation.carpool,
        free_transport_preferred: params.transportation.free_transport_preferred,
        free_transport_preferred_other: params.transportation.free_transport_preferred_other,
        created_at: now,
        updated_at: now
      }
      const values = await getObjectValues(insert_params)
      db_response = await postgres.query(
        insertQuery('lead_transportations', insert_params), values
      )
      lead_transportation = db_response.rows[0]
      lead_transportation_id = lead_transportation.id
    } else {
      // Update lead transportation
      lead_transportation_id = db_response.rows[0].id
      const condition = {id: lead_transportation_id}
      params.updated_at = moment().toDate()
      const update_params = {
        school_id: params.school_id,
        lead_id: params.lead_id,
        need: params.transportation.need,
        carpool: params.transportation.carpool,
        free_transport_preferred: params.transportation.free_transport_preferred,
        free_transport_preferred_other: params.transportation.free_transport_preferred_other,
        updated_at: now
      }
      const built_update = buildUpdate('lead_transportations', condition, update_params)
      db_response = await postgres.query(built_update.text, built_update.values)
      lead_transportation = db_response.rows[0]
      // Delete lead transportation reasons
      await postgres.query(leads_transportation_queries.deleteLeadTransportationReasons(), [lead_transportation_id, params.school_id])
    }
    // Build bulk insert for the lead transportation reasons
    if (reasons.length){
      const bulk_params = reasons.map( obj => (

        {
          school_id: params.school_id,
          lead_transportation_id,
          created_at: now,
          reason_id: obj.reason_id,
          other: obj.other || ''
        }
      ))
      const bulk = buildBulkInsert(
        leads_transportation_queries.bulkInsertLeadTransportationReasons(),
        bulk_params
      )
      // Insert application fields
      db_response = await postgres.query(bulk.text, bulk.values)
      lead_transportation.reasons = db_response.rows
    }
    return lead_transportation
  } catch (error) {
    logger.error('insertOrUpdateLeadTransportationError:', error)
    throw serverError
  }
}

export async function insertLeadAdditionalData(params:LeadAdditionalData) {
  let db_response
  try {
    params.created_at = moment().toDate()
    params.updated_at = moment().toDate()
    const {reasons_for_leaving} = params
    delete params.reasons_for_leaving
    const values = await getObjectValues(params)
    db_response = await postgres.query(
      insertQuery('lead_additional_data', params), values
    )
    const lead_additional_data = db_response.rows[0]
    // Build bulk insert for the lead additional data reasons for leaving
    if(reasons_for_leaving.length) {
      const bulk_params = reasons_for_leaving.map<{
        school_id: number;
        lead_additional_data_id: number;
        reason_id: string;
        other: string | null;
        created_at: Date;
      }>( obj => (
        {
          school_id: params.school_id,
          lead_additional_data_id: lead_additional_data.id,
          created_at: moment().toDate(),
          other: null,
          ...obj,
        }
      ))
      const bulk = buildBulkInsert(
        queries.bulkInsertLeadAdditionalDataReasonsForLeaving(),
        bulk_params
      )
      // Insert application fields
      db_response = await postgres.query(bulk.text, bulk.values)
    }
    return db_response.rows[0]
  } catch (error) {
    logger.error('insertLeadAdditionalDataError:', error)
    throw serverError
  }
}

export async function insertLeadIntegrations(params:LeadIntegrations) {
  let db_response
  try {

    const values = await getObjectValues(params)
    db_response = await postgres.query(
      insertQuery('lead_integrations', params), values
    )

    return db_response.rows[0]
  } catch (error) {
    logger.error('insertLeadAdditionalDataError:', error)
    throw serverError
  }
}

/**
*
* @param  {number} id
* @param  {number} school_id
* @returns {Promise} response object with the response from the database
*/
export async function getLead(id: number, school_id: number): Promise<Lead> {
  try {
    const db_response = await postgres.query(queries.getLead(), [id, school_id])
    return db_response.rows[0]
  } catch (error) {
    logger.error('getLeadError:', error)
    throw serverError
  }
}


/**
*
* @param  {number} id
* @param  {number} school_id
* @returns {Promise} response object with the response from the database
*/
export async function getLeadById(id: number): Promise<Lead> {
  try {
    const db_response = await postgres.query(queries.getLeadById(), [id])
    return db_response.rows[0]
  } catch (error) {
    logger.error('getLeadError:', error)
    throw serverError
  }
}

/**
*
* @param  {} params
* @param  {number} params.school_id
* @param  {array} params.lead_ids
* @param  {object} params.values
* @param  {string} params.created_by
* @param  {string} params.logged_id
* @param  {boolean} create_in_sf
* @returns {Promise} response object with the response from the database
*/
export async function editLeadsForSchool(params: EditLeadForSchool): Promise<Array<Lead> | object> {
  await validate(params, schemas.edit_leads_for_school)
  try {
    const reasons_res = await reasonsController.getReasonsByType({type: 'lead', school_id: 0})

    const lead_ids_int = ((typeof params.leads_ids[0] === 'string') ? params.leads_ids.map((lead_id_str: string) => Number(lead_id_str)) : params.leads_ids) as Array<number>
    const values_to_update = params.values
    const operation_type = 'edit'
    const table_name = 'leads'
    const o_type = 'lead'
    const transportation = values_to_update.transportation || undefined

    values_to_update.updated_at = moment().toDate()
    if (values_to_update.status && values_to_update.status !== LEAD_STATUSES.ARCHIVED){
      values_to_update.reason_id = null
      values_to_update.reason_other = null
    }

    delete values_to_update.transportation
    delete values_to_update.lead_transportation_need

    const db_res_sq = await postgres.query(selectQueryIn(table_name, lead_ids_int), [])
    const leads_to_update = db_res_sq.rows || undefined

    const keys = Object.keys(params.values)
    const values = Object.values(params.values)
    let updated_leads = []
    let non_updated_lead_ids: Array<number> = []

    try{
      const db_res_ul = await postgres.query(queries.updateLeads(keys, lead_ids_int), values)
      updated_leads = db_res_ul.rows
    } catch (error) {
      if (!error.message.includes('leads_duplicated'))
        throw serverError
      const res_query = queries.builSelectToUpdate(keys, values, lead_ids_int) as any
      const db_res_ul = await postgres.query(res_query, [...values, params.school_id])
      updated_leads = db_res_ul.rows
      if(updated_leads.length===0) throw new ConflictError('leads_duplicated')
    }

    if(updated_leads.length) {

      const updated_lead_ids = updated_leads.map(lead => lead.id)
      non_updated_lead_ids = lead_ids_int.filter(id => !updated_lead_ids.includes(id))

      for (const lead_id of updated_lead_ids){
        const old_lead = leads_to_update.find((lead: any) => lead.id === lead_id)
        // Insert log change
        const log_changes_params = {
          school_id: params.school_id,
          user_id: params.user_id,
          table_name,
          operation_type,
          new_values: values_to_update,
          old_values: old_lead,
          created_at: moment().toDate()
        }
        await insertLogChange(log_changes_params)

        if (transportation) {
          await this.insertOrUpdateLeadTransportation({
            school_id: params.school_id,
            lead_id: lead_id,
            transportation
          })
        }

        const new_lead = updated_leads.find(lead => lead.id === lead_id)
        // check change of data to track event
        if(new_lead.application_received && !old_lead.application_received){
          if(old_lead.meta_fbclid){
            await trackEventFP('SchoolApply-SetEnrollment', old_lead.id.toString(), {   distinct_id: old_lead.id.toString(), lead_id: old_lead.id, school_id: old_lead.school_id, fbclid: old_lead.meta_fbclid})
          } else if(old_lead.meta_leadgen_id){
            await trackEventFP('SchoolApply-SetEnrollment', old_lead.id.toString(), {   distinct_id: old_lead.id.toString(), lead_id: old_lead.id, school_id: old_lead.school_id, leadggen_id: old_lead.meta_leadgen_id})
          }
        }

        if (old_lead.status == new_lead.status) continue

        if (old_lead.status !== values_to_update.status){
          const add_log_evt_params = {
            id: lead_id,
            logged_id: params.user_id,
            status: values_to_update.status,
            oType: o_type
          }
          await addLogEventAsNote(add_log_evt_params)
        }

        const { reason_id, lead_source_id } = new_lead

        let seatOperation = 0

        // they askme to kill all process from archived leads for the moment
        // if (new_lead.status == LEAD_STATUSES.ARCHIVED){
        //   if (reason_id){
        //     //get reason to check dnc field
        //     const reason_row = reasons_res.find((r)=>r.id === reason_id)
        //     if (reason_row?.dnc) {
        //       continue
        //     }
        //     if ((lead_source_id == -4 || lead_source_id == -11) && new_lead?.reason_other != 'DNC' && !PREVENT_RUNNING_ASM.includes(reason_row?.name.toLowerCase())) {
        //       // if the lead has a source -4 only create the lead in schola school
        //       await this.addLeadToScholaSchool(new_lead)
        //     }
        //   } else if (lead_source_id == -4 || lead_source_id == -11){
        //     await this.addLeadToScholaSchool(new_lead)
        //   }
        //   continue
        // } else

        if (new_lead.status == LEAD_STATUSES.ACCEPTED){
          seatOperation -= 1
        } else {
          // check the history looking for change from acceted
          const db_res_lh = await postgres.query(queries.getLeadHistory(), [lead_id])
          if (db_res_lh.rowCount) {
            const lead_history = db_res_lh.rows[0]
            if (lead_history.old_value == LEAD_STATUSES.ACCEPTED && lead_history.new_value != LEAD_STATUSES.ACCEPTED) {
              seatOperation += 1
            }
          }
        }

        if (seatOperation != 0){
          const seats = await schoolController.getSchoolSeats(new_lead.school_id)
          const seats_num = seats.map((seat: any) => ({
            grade: seat.grade,
            availables: seat.grade == new_lead.grade ? seat.availables + seatOperation : seat.availables,
            total: seat.total
          }))
          if(seats_num.length>0) await schoolController.updateSchoolSeats(params.school_id, { seatsAvailables: seats_num })

          // // about_your_school_enrollments
          // await schoolController.syncAboutYourSchoolEnrollment(params.school_id, seats_num)
          // // onboarding_enrollments
          // await schoolController.syncOnboardingEnrollments(params.school_id, seats_num)
        }

        //execute trigger if the stage/status changed
        if(old_lead.status != new_lead.status){
          const scope =  ['']
          const application = await sendApplicationToLead({ ...new_lead, logged_id: params.user_id }, false, scope)
          const application_url = application.short_url
          // Process lead when add schola system
          await marketingCampaignExecuteTrigger.executeTriggers(
            new_lead,
            application_url
          )
        }

      }

    }

    if(params.version == 0 || !Object.prototype.hasOwnProperty.call(params, 'version'))
      return updated_leads
    else if (params.version == 1)
      return {
        updated: updated_leads,
        non_updated: non_updated_lead_ids
      }

  } catch (error) {
    logger.error('editLeadsForSchoolError:', error)
    if (error.message.includes('leads_duplicated'))
      throw new ConflictError('duplicated')

    throw serverError
  }
}

/**
*
* @param  { string } credential_sub
* @returns { Promise } response object with the response from the database
*/
export async function zapierArchiveLeads(credential_sub: string): Promise<Array<Lead>> {
  await validate({ user_id: credential_sub }, schemas.get_by_user_id)
  try {
    // getting all leads matching the query with this query we are only getting the expired ones from each tier
    const values = [SCHOLA_TIERS.recruiterPro.join('|'), SCHOLA_TIERS.scholaPlus.join('|'), SCHOLA_TIERS.legacy.join('|')]
    const all_expired_leads = await postgres.query(queries.getExpiredLeads(), values)
    if (all_expired_leads.rowCount) {
      // getting the lead id's from the ones we received with the query
      const leads_ids = all_expired_leads.rows.map(lead => lead.id)
      const added_leads = []
      // loop to archive the leads
      for (const lead of all_expired_leads.rows) {
        const values2 = [lead.parent_first_name, lead.parent_last_name, lead.child_first_name, lead.child_last_name, lead.grade]
        // searching if there is another lead that is pending to expire to prevent send it to sf
        const active_leads = await postgres.query(queries.getMatchingLeads(leads_ids), values2)
        // creating tag in case we have 2 leads with the same info and both expired to prevent creating 2 households in SF
        const tag = `${lead.child_last_name}/${lead.child_first_name}/${lead.parent_last_name}/${lead.parent_first_name}/${lead.grade}`
        const edit_lead = {
          school_id: lead.school_id,
          leads_ids: [lead.id],
          values: { status: 'archived' },
          created_by: lead.created_by,
          user_id: credential_sub
        }
        if (active_leads.rowCount || added_leads.indexOf(tag) >= 0) {
          // if we found non expired leads or if we found that we sent the lead to sf we are only changing the status
          await this.editLeadsForSchool(edit_lead)
        } else {
          // if we didn't find expired leads or if we have 2 same leads expired to add a hosehold in SF
          added_leads.push(tag)
          await this.editLeadsForSchool(edit_lead)
        }
      }
    }
    return all_expired_leads.rows
  } catch (error) {
    logger.error(error)
    throw serverError
  }
}

/**
*
* @param  {string} key
* @param  {object} obj
* @returns {string} type of Json value
*/
export function formatAsPrettyObject (key:string, obj: unknown) {
  if (key === 'address' || key.startsWith('parent_or')) {
    const address = obj as JotFormAddress
    return `${address.street_address} ${address.city}${address.city ? ',' : ''} ${address.state} ${address.zip}`
  } else if (key.startsWith('emergency_contact')) {
    const contact = obj as EmergencyContact
    return `Name: ${contact.name}\nPhone: ${contact.phone}\nRelationship: ${contact.relationship}`
  } else if (key.startsWith('doctor_name')) {
    const address = obj as DoctorAddress
    return `Name: ${address.name}\nPhone: ${address.phone}\nAddress: ${address.address} ${address.city}${address.city ? ',' : ''} ${address.state} ${address.zip}`
  } else {
    return JSON.stringify(obj)
  }
}

/**
*
* @param  {unknown} value
* @returns {string} type of Json value
*/
export function getTypeOfJsonValue(value: unknown): string {
  if(typeof value === 'string'){
    if(value.includes('data:image') || value.includes(';base64')) {
      return 'image'
    }
    return 'string'
  }else if(typeof value === 'object'){
    if(Array.isArray(value))
    {
      return 'array'
    }
    else{
      return 'object'
    }
  }
  return ''
}

/**
*
* @param  {Array<unknown>} array
* @param  {string} columnName
* @param  {Rocord<string, string>} cols
* @param  {Record<string, object>} propertyMap
* @returns {Promise} response object with the response from the database
*/
export function processValueTypeArrayJotForm(
  array: Array<unknown>,
  columnName: string,
  cols: Record<string, string>,
  propertyMap: Record<string, unknown>
): void {
  for(let i = 0; i < array.length; i++){
    const item = array[i]
    const type = getTypeOfJsonValue(item)
    const newColumnName = `${columnName}>>item_${i+1}`
    if(type === 'string') {
      processValueTypeStringJotForm(item,newColumnName,cols, propertyMap)
    }
    else if(type === 'object'){
      processValueTypeObjectJotForm(item as Record<string, unknown>,newColumnName,cols, propertyMap)
    }
  }
}

/**
*
* @param  {object} value
* @param  {string} columnName
* @param  {Rocord<string, string>} cols
* @param  {Record<string, object>} propertyMap
* @returns {Promise} response object with the response from the database
*/
export function processValueTypeObjectJotForm(
  value:Record<string, unknown>,
  columnName:string,
  cols:Record<string,string>,
  propertyMap:Record<string, unknown>
): void {
  for(const key of Object.keys(value)){
    const subValue = value[key]
    const newColumnName = `${columnName}>>${key}`
    processValueTypeStringJotForm(subValue,newColumnName, cols, propertyMap)
  }
}

/**
*
* @param  {unknown} value
* @param  {string} columnName
* @param  {Rocord<string, string>} cols
* @param  {Record<string, unknown>} propertyMap
* @returns {} response object with the response from the database
*/
export function processValueTypeStringJotForm(
  value:unknown,
  columnName:string,
  cols:Record<string,string>,
  propertyMap:Record<string, unknown>
): void {
  cols[columnName] = columnName
  propertyMap[columnName] = value
}

/**
*
* @param  {string} formData
* @returns {PrettyJotFormParam[]} response object with the response from the database
*/
export function getPrettyObjectJotForm(formData: string): PrettyJotFormParam[] | undefined {
  if(formData?.match(/\w+:\w+/)){
    const result:PrettyJotFormParam[] = []
    const values = formData.split(',')
    for(let i = 0; i < values.length; i++){
      const value = values[i]
      const params = value.split(':')
      result.push({
        index: (i+2),
        name: params[0],
        value: params[1],
      })
    }
    return result
  }
}

/**
*
* @param  {string} key
* @returns {string} response object with the response from the database
*/
export function getPrettyColumnNameByIndexJotForm(key: string): string {
  // we cannot map the pretty columns because the pretty value has issues if there are answers with ","
  return (key.startsWith('q') && key.indexOf('_')>0 ? key.split('_')[1] : undefined)
}

/**
*
* @param  {number} school_id
* @param  {LeadsExportSelection} params
* @returns {Promise} response object with the formated csv and the date
*/
export async function exportApplicationsByIds(school_id: number, lead_ids: Array<number>): Promise<CSVAndNow> {
  const result = {
    now: moment().format('YYYY-MM-DD'),
    csv: ''
  }
  try {
    const db_response = await postgres.query(queries.getApplicationsByIds(), [school_id, lead_ids])
    if(db_response.rowCount === 0) return result
    const {data, columns} = await getApplicationCSV(db_response.rows, false)
    const csv_stream = stringify(data, {columns, header: true })
    result.csv = csv_stream
    return result
  } catch (error) {
    logger.error('exportApplicationsByIdsError:', error)
    throw serverError
  }
}

/**
*
* @param  {ApplicationsForPrint} applications
* @returns {Promise} response object with the response from the database
*/
function makePrintApplicationFormat(applications: Array<ApplicationsForPrint>): any {
  const document_definition = {
    styles: {
      header: {
        fontSize: 16,
        bold: true,
        alignment: 'center'
      },
      subheader: {
        fontSize: 15,
        bold: true,
        alignment: 'center',
        margin: [10, 30, 10, 10]
      },
      fieldname: {
        bold: true,
        fontSize: 11,
        margin: [0, 10, 0, 5]
      },
      fieldvalue: {
        fontSize: 10,
        margin: [0, 0, 0, 10]
      }
    }
  }
  const content: any[] = []
  const divider = {
    text: '-----------------------------------------------------------------------------------------------------------------------------------------------------------'
  }
  applications.forEach( (application, index) => {
    content.push({
      text: application.school_name + ' Enrollment Application',
      style: 'header',
      pageBreak: (index != 0) ? 'before' : ''
    }, {
      text: 'General Information',
      style: 'subheader'
    })
    application.data.general.forEach((general, index) => {
      if(index == 0) content.push(_.clone(divider))
      content.push({
        text: APPLICATION_EXPORT_COLUMNS[general.key] || 'Unknown',
        style: 'fieldname'
      }, {
        text: general.value,
        style: 'fieldvalue'
      }, _.clone(divider))
    })
    if(application.data.extra.pretty) {
      content.push({
        text: 'Fields',
        style: 'subheader',
        pageBreak: 'before',
      })
      const raw_request = JSON.parse(application.data.extra.rawRequest)
      // const pretty = getPrettyObjectJotForm(application.data.extra.pretty)
      const columns = {}
      const properties:IObjectKeys = {}
      for(const key of Object.keys(raw_request)) {
        const column_name = getPrettyColumnNameByIndexJotForm(key)
        if(column_name){
          const value = raw_request[key]
          const type = getTypeOfJsonValue(value)
          if(type === 'string') processValueTypeStringJotForm(value, column_name, columns, properties)
          else if(type === 'object') processValueTypeObjectJotForm(value, column_name, columns, properties)
          else if(type === 'array') processValueTypeArrayJotForm(value, column_name, columns, properties)
        }
      }
      for(const col in columns) {
        const value = properties[col]
        content.push({
          text: col,
          style: 'fieldname',
        }, {
          text: (typeof value === 'object') ? formatAsPrettyObject(snakeCase(col), value): value,
          style: 'fieldvalue',
        }, _.clone(divider))
      }
    } else {
      application.data.extra.forEach( (extra: any) => {
        content.push({
          text: extra.name,
          style: 'subheader',
          pageBreak: 'before',
        })
        extra.fields.forEach( (field: any, index: number) => {
          if(index === 0) content.push(_.clone(divider))
          content.push({
            text: field.name,
            style: 'fieldname',
          }, {
            text: (typeof field.value === 'object') ? formatAsPrettyObject(snakeCase(field.name), field.value): field.value,
            style: 'fieldvalue',
          }, _.clone(divider))
        })
      })
    }
  })
  return {
    ...document_definition,
    content,
  }
}

/**
*
* @param  {number} school_id
* @param  {LeadsPrint} params
* @returns {Promise} response object with the response from the database
*/
export async function printApplicationsForSchool(school_id: number, params: LeadsPrint): Promise<any> {
  await validate(params, schemas.print_applications_for_school)
  try {
    const db_response = await postgres.query(queries.getCompletedApplicationsForschool(), [school_id, params.appIds])
    if(db_response.rowCount === 0) throw notFoundError
    const printer = new PdfPrinter(APPLICATION_PRINT_FONTS)
    if(!params.downloadzip) {
      const document_definition = makePrintApplicationFormat(db_response.rows)
      const pdf_doc = printer.createPdfKitDocument(document_definition)
      pdf_doc.end()
      return pdf_doc
    }
    const archive = archiver( 'zip', { zlib: { level: 9 } } )
    for(let index = 0; index < db_response.rowCount; index++) {
      const application = db_response.rows[index]
      let is_jot_form = false
      let jot_form_id
      let jot_submission_id
      if(application.lead_note) {
        const note = JSON.parse(application.lead_note)
        if(note.integration === 'jotforms') {
          if(note.formId) {
            jot_form_id = note.formId
            if(note.submission) {
              jot_submission_id = note.submission
              is_jot_form = true
            }
          }
        }
      }
      if(is_jot_form) {
        const url = `https://${ S3_BUCKET }.s3.amazonaws.com/school/${ school_id }/applications/${ jot_form_id }/${ jot_submission_id }.pdf`
        const {data} = await axios.get(url, {responseType: 'stream'})
        archive.append(data , { name: `${ ( index + 1 ) }_jotform_applicationId-${ application.id }.pdf` } )
      } else {
        const document = makePrintApplicationFormat( [ application ] )
        const pdf_doc = printer.createPdfKitDocument( document )
        pdf_doc.end()
        const stream = Readable.from( pdf_doc )
        archive.append( stream, { name: `${ ( index + 1 ) }_applicationId-${ application.id }.pdf` } )
      }
    }
    archive.finalize()
    return archive
  } catch (error) {
    logger.error('printApplicationsForSchoolError:', error)
    if(error.statusCode === notFoundError.statusCode) throw error
    throw serverError
  }
}

/**
*
* @param  {number} school_id
* @returns {Promise} response object with the response from the database
*/
export async function getPotentialLeadsForSchoolCount(school_id: number, filters?: PotentialLeadCountFilters): Promise<PotentialLeadCount> {
  const response = {
    total: 0,
  }
  const school = await schoolController.getSchoolById(school_id)
  if(!school) throw notFoundError
  const values = [school.id, school.latitude, school.longitude]
  try {
    if(filters) {
      if(filters.text) {
        values.push(`%${filters.text}%`)
      }
      if(filters.grade) {
        values.push(`%${filters.grade}%`)
      }
    }
    const db_response = await postgres.query(queries.getPotentialLeadsForSchoolCount(filters), values)
    if(db_response.rowCount > 0) {
      response.total = db_response.rows[0].count
    }
    return response
  } catch (error) {
    logger.error('getPotentialLeadsForSchoolCountError:', error)
    throw serverError
  }
}

/**
*
* @param  {} params
* @returns {Promise} response object with the response from the database
*/
export async function getPotentialLeadsForSchool(school_id: number, query_params: PotentialLeadsGet) {
  await validate(query_params, schemas.get_potential_leads_for_school)
  try {
    const pagination_options = {
      page: query_params.page || 1,
      pageSize: query_params.pageSize || 10
    }
    const sort = {
      field : query_params.fieldSort || 'created_at',
      direction: query_params.fieldDirection || 'DESC',
    }
    const filters = {
      text: query_params.text,
      grade: query_params.grade
    }

    const db_response_school_lat_lon = await postgres.query(queries.getSchoolLatLon(), [school_id])
    const { latitude, longitude } = db_response_school_lat_lon.rows[0]
    const { query_string, values } = queries.potentialLeadsForSchool(school_id, latitude, longitude, filters, sort, pagination_options)
    const db_response_potential_leads_for_school = await postgres.query(query_string, values)
    const potential_leads_for_school = db_response_potential_leads_for_school.rows
    const potential_leads_for_school_count = await getPotentialLeadsForSchoolCount(school_id, filters)

    return {
      results: potential_leads_for_school,
      pagination: {
        page: pagination_options.page,
        pageSize: pagination_options.pageSize,
        rowCount: Number(potential_leads_for_school_count.total),
        pageCount: Math.ceil(potential_leads_for_school_count.total / pagination_options.pageSize)
      }
    }
  } catch (error) {
    logger.error('getPotentialLeadsForSchoolError:', error)
    throw serverError
  }
}

/**
* @param {number} school_id
* @param  {GetSchoolIdLeads} query_params
* @returns {Promise} response object with the response from the database
*/
export async function getLeadsForSchool(school_id: number, query_params: GetSchoolIdLeads, get_app = false) {
  await validate(query_params, schemas.get_leads_for_school)
  const db_response_school = await postgres.query(queries.schoolExists(), [school_id])
  if(!db_response_school.rows[0].exists) throw notFoundError
  try {
    const lead_status = query_params.status || ''
    const { filters, pagination_options, lead_sort } = queries.proccessGetLeadsForSchoolQueryParams(query_params)
    const db_response_lead_fields = await postgres.query(queries.getLeadFields(), [school_id])
    const lead_fields = db_response_lead_fields.rows
    const { query_conditions, values } = queries.prepareQuery(filters, lead_fields)
    const db_response_count_leads = await postgres.query(queries.getLeadsForSchoolCountQuery(query_conditions), [school_id, lead_status, ...values])
    const count_leads = Number(db_response_count_leads.rows[0].count)
    const additional_values = [pagination_options.page_size, pagination_options.page_offset]
    const query = queries.getLeadsForSchoolMainQuery(query_conditions, values.length, lead_sort.direction, lead_sort.field, get_app)
    const db_response_main_query = await postgres.query( query, [school_id, lead_status, ...values, ...additional_values] )
    if (SCHOOLS_WITH_LOTTERY.includes(school_id.toString())){
      const db_res_par = await postgres.query(lottery_queries.getParticipantsJson(), [school_id])
      const participants = db_res_par.rows[0].participants
      if (participants){
        db_response_main_query.rows.map((lead: any) => {
          let lottery_status = participants[lead.id]
          if (participants[lead.id] == undefined && Object.prototype.hasOwnProperty.call(LOTTERY_SUBSTAGE, lead.lead_status) && LOTTERY_SUBSTAGE[lead.lead_status] == lead.reason){
            lottery_status = lead.lead_status
          } else if ( participants[lead.id] == undefined ){
            lottery_status = 'submitted'
          }
          lead.lottery_status = lottery_status
        })
      }
    }
    if (SCHOOLS_WITH_INTEGRATIONS.includes(school_id.toString())){
      const db_res_par = await postgres.query(queries.getLeadsWithExtraSMData(), [school_id])
      const school_mint_data = db_res_par.rows[0].school_mint
      if(school_mint_data){
        db_response_main_query.rows.map((lead: any) => {
          lead.school_mint = school_mint_data[lead.id]
        })
      }
    }
    return {
      results: db_response_main_query.rows,
      s3Bucket: S3_BUCKET,
      pagination: {
        page: pagination_options.page,
        pageSize: pagination_options.page_size,
        rowCount: count_leads,
        pageCount: Math.ceil(count_leads / pagination_options.page_size)
      }
    }
  } catch (error) {
    logger.error('getLeadsForSchoolError:', error)
    throw serverError
  }
}

/**
*
* @param  {number} school_id
* @param  {string} status
* @param  {string} title
* @returns {Promise} response object with the response from the database
*/
export async function getLeadsExport(school_id: number, status?: string): Promise<string> {
  const school = await schoolController.getSchoolById(school_id)
  if(!school) throw notFoundError
  const ids = status ? status.split('-'): []
  const status_array:Array<string> = []
  if(ids.length > 0) {
    ids.forEach( id => {
      switch(id) {
      case '1':
        status_array.push('new')
        break
      case '2':
        status_array.push('application-sent')
        break
      case '3':
        status_array.push('application-received')
        break
      case '4':
        status_array.push('waitlisted')
        break
      case '5':
        status_array.push('accepted')
        break
      case '6':
        status_array.push('archived')
        break
      }
    })
  }
  // const lead_fields = await getLeadFields(school_id) // Apparently this is not in use
  try {
    const db_response = await postgres.query(queries.getLeadsExport(), [school_id, status_array])
    const {csv_column_objects, columns} = await getColumnsForCSV(school_id)
    const data = db_response.rows.map(row => {
      const map:LeadCSVColumnNames = {}
      csv_column_objects.forEach(column_object => {
        if(column_object.keys==='child_birthdate'){
          map[column_object.name] = row[column_object.keys] ? moment(row[column_object.keys]).format('YYYY-MM-DD'): ''
        }
        else if(column_object.keys.includes('_at')) {
          if(column_object.keys.includes('application_received_at') && row['application_received'] === 'No') {
            row[column_object.keys] = null //clear date
          }
          if(column_object.keys.includes('enrollment_confirmed_at') && row['enrollment_confirmed'] === 'No') {
            row[column_object.keys] = null //clear date
          }
          map[column_object.name] = row[column_object.keys] ? moment(row[column_object.keys]).format('YYYY-MM-DD HH:mm:ss'): ''
        } else {
          map[column_object.name] = row[column_object.keys]
          if (column_object.keys === 'address' && ((row[column_object.keys] === null || row[column_object.keys] === '') && row['address_description'] !== null)) map[column_object.name] = row['address_description']
        }
      })
      return map
    })
    const csv_stream = stringify(data, {columns, header: true })
    return csv_stream
  } catch (error) {
    logger.error('getLeadsExportError:', error)
    throw serverError
  }
}

/**
*
* @param  {number} school_id
* @returns {Promise} response object with the response from the database
*/
export async function getAllLeadsExport(school_id: number): Promise<string> {
  const school = await schoolController.getSchoolById(school_id)
  if(!school) throw notFoundError
  try {
    const db_response = await postgres.query(queries.getAllLeadsExport(), [school_id])
    const {csv_column_objects, columns} = await getColumnsForCSV(school_id)
    const data = db_response.rows.map(row => {
      const map:LeadCSVColumnNames = {}
      csv_column_objects.forEach(column_object => {
        if(column_object.keys.includes('_at') || column_object.keys === 'child_birthdate') {
          map[column_object.name] = row[column_object.keys] ? moment(row[column_object.keys]).format('YYYY-MM-DD HH:mm:ss'): ''
        } else {
          map[column_object.name] = row[column_object.keys]
        }
      })
      return map
    })
    const csv_stream = stringify(data, {columns, header: true })
    return csv_stream
  } catch (error) {
    logger.error('getAllLeadsExportError:', error)
    throw serverError
  }
}

/**
*
* @param  {number} school_id
* @param  {number} hours_time_zones_offset
* @param  {LeadCountForMarketingCampaigns} params
* @returns {Promise} response object with the response from the database
*/
export async function getleadCountForMarketingCampaigns(school_id: number, hours_time_zones_offset: number) {
  try {
    const db_school_subscription_info = await postgres.query(queries.schoolSubscriptionInfo(), [school_id])
    const school_subscription_info = db_school_subscription_info.rows[0]
    if(!school_subscription_info) throw notFoundError
    const school_days = schoolUtils.getSchoolBusinessDays(school_subscription_info)
    const expiration_days = school_days.getExpirationDays()
    const db_response_lead_count = await postgres.query(queries.leadCountForMarketingCampaigns(), [school_id])
    const lead_count = db_response_lead_count.rows
    const new_leads = await getLeadsForSchool(school_id, {status: 'new'} as GetSchoolIdLeads)
    const archived_leads = await getLeadsForSchool(school_id, {status: 'archived'} as GetSchoolIdLeads)
    const leads_results = [...new_leads.results, ...archived_leads.results]

    if(leads_results.length) {
      const rows_expired = []
      if(expiration_days > 0) {
        for(const lead of leads_results) {
          if(lead.created_on !== 'admin' && lead.created_on !==  'admin-reset' && lead.lead_source_id < 0) {
            let expired = false
            if(lead.messages_count === '0') {
              //no contacted, check expiration
              if(!lead.created_at) {
                expired = true
              } else {
                const created_at = new Date(moment(lead.created_at).add(hours_time_zones_offset, 'hours').toDate().getTime()) //copy date
                if(expiration_days === 1 && !school_days.isBusinessDay(created_at)) { //for scholabasics extend the time on weekends & holidays
                  created_at.setHours(23,59,59,0)
                }
                const expiration_date = school_days.getNextBusinessDays(lead.created_at, expiration_days)
                const actual_date = moment.utc()
                const timer = moment.duration(expiration_date.diff(actual_date))
                expired = timer.asHours() <= 0
              }
            }

            if(expired) {
              const language = (lead.language.toLowerCase()==='spanish' ? 'es' : 'en')
              const lead_status_id = (!lead.lead_status_id || lead.lead_status_id === null ? -1 : lead.lead_status_id)
              const _row = rows_expired.filter((r) => r.status=== lead.status && r.language === language && r.lead_status_id === lead_status_id)

              if(_row.length>0){
                _row[0].count++
              } else {
                rows_expired.push({status: lead.status, language, lead_status_id,count: 1})
              }
            }
          }
        }
      }

      for(const lead of lead_count) {
        lead.expired = 0
        if(lead.status === 'new' || lead.status === 'archived') {
          const rowExpired = rows_expired.filter((re) => re.status === lead.status && re.language === lead.language && re.lead_status_id === lead.lead_status_id)
          if(rowExpired.length>0){
            lead.expired = rows_expired[0].count
          }
        }
      }
    }

    return { results: lead_count }
  } catch (error) {
    logger.error(error)
    throw serverError
  }
}

/**
*
* @param  { LeadsPost } params
* @param { number } params.school_id
* @param { string } params.user_id
* @param { string } params.parent_relationship
* @param { string } params.parent_first_name
* @param { string } params.parent_last_name
* @param { string } params.email
* @param { string } params.phone
* @param { string } params.preferred_contact
* @param { Array } params.students
* @param { boolean } params.tour_requested
* @param { string } params.language
* @param { string } params.note
* @param { string } params.year
* @param { string } params.year_accepted
* @param { string } params.custom_field_1
* @param { string } params.custom_field_2
* @param { string } params.custom_field_3
* @param { string } params.custom_field_4
* @param { string } params.custom_field_5
* @param { string } params.zipcode
* @param { number } params.lead_source_id
* @param { number } params.lead_status_id
* @param { string } params.application_source
* @param { string } params.application_form_id
* @param { string } params.viewed_at
* @param { string } params.created_by
* @param { string } params.address
* @param { number } params.latitude
* @param { number } params.longitude
* @param { string } params.city
* @param { string } params.state
* @param { string } params.address_description
* @param { string } params.status
* @param { boolean } params.send_notification
* @param { string } params.external_campaign_id
* @param { string } params.referred_by
* @param { string } params.referred_by_other
* @param { string } params.created_at
* @param { string } params.reason_id
* @param { string } params.reason_other
* @param { string } params.country
* @param { string } params.assigned_to
* @param { string } params.parent_referral_source
* @param { boolean } params.application_received
* @param { boolean } params.application_valid
* @param { string } params.application_received_at
* @param { boolean } params.enrollment_confirmed
* @param { string } params.enrollment_confirmed_at
* @param { string } params.created_on
* @returns {Promise} response object with the response from the database
*/
export async function createNewLead(params: LeadsPost): Promise<Array<Lead>> {
  await validate(params, schemas.create_lead_for_school)

  // review if use like this or use the direct query
  const school = await schoolController.getSchoolById(params.school_id)

  if(!school) throw notFoundError

  const students = params.students
  delete params.students
  // send_aplication should be false to be considered false, if is undefined then is considered true
  const send_notification = params.send_notification === false ? false : true
  delete params.send_notification
  const search_history_id = Number(params.search_history_id || 0)
  delete params.search_history_id

  const leads = []
  for (const student of students){
    if (!params.created_by && params.user_id) {
      params.created_by = params.user_id
    }

    if (student.child_birthdate === '') {
      delete student.child_birthdate
    }

    const lead_values = {
      ...params,
      ...student
    }

    const new_lead = await this.createLead(lead_values)

    const history_params = {
      lead_id: new_lead.id,
      field: 'status',
      old_value: '',
      new_value: 'new',
    }
    await this.addHistory(history_params)
    if(search_history_id>0){
      //save relation
      await searchHistory.insertSearchHistoryLead({
        school_id: new_lead.school_id,
        user_id: new_lead.user_id,
        search_history_id,
        lead_id: new_lead.id
      })

    }
    if (REACTIVATE_CAMPAIGN_SOURCES.includes(params?.source) && new_lead?.id){
      try{
        await postgres.query(queries.updateUnresponsiveReassignedLeads(), [params?.email?.toLowerCase(), new_lead.id])
      } catch (error) {
        logger.error('updateUnresponsiveReassignedLeads:', error)
        throw serverError
      }
    }
    leads.push(new_lead)
  }

  if (send_notification && params.school_id != DEFAULT_SCHOOL_CS) {
    const email_params = {
      school,
      lead: { ...params, students },
      admin_host: FRONTEND_HOST_ADMIN,
      search_host: FRONTEND_HOST_SEARCH
    } as LeadEmailContext
    const email_context = await applicationsController.buildEmailContext(email_params)
    email_context && this.sendEnrollmentNotification(email_context)
  }

  return leads
}

/**
*
* @param  {} params
* @returns {Promise} response object with the response from the database
*/
export async function addHistory(lead_history: HistoricalLeadData): Promise<Array<LeadsHistory>> {
  try {
    const now = moment().toDate()
    const params = { ...lead_history, created_at: now, updated_at: now}
    const values = await getObjectValues(params)
    const db_response = await postgres.query(insertQuery('leads_history', params), values)
    return db_response.rows
  } catch (error) {
    logger.error('addHistoryError:',error)
    throw serverError
  }
}

/**
*
* @param  {number} school_id
* @param  {Array<number>} leads_ids
* @param  {boolean} email_required
* @param  {string} logged_id
* @param  {Array<string | number>} scope
* @returns {Promise} array of responses from the sendApplicationToLead utils
*/
export async function sendApplications(school_id: number, leads_ids: Array<number>, email_required: boolean, logged_id: string, scope: Array<string | number>): Promise<Array<unknown>> {
  try {
    const db_response = await postgres.query(queries.getLeads(), [leads_ids, school_id])
    const leads = db_response.rows
    const promises = leads.map(lead => sendApplicationToLead({...lead, logged_id}, email_required, scope))
    return (await Promise.all(promises)).map(response => response.short_url)
  } catch (error) {
    logger.error('sendApplicationsError:', error)
    throw serverError
  }
}

/**
*
* @param  {string} user_id
* @returns {Promise} response object with the response from the database
*/
export async function associateLeadsWithUser(user_id: string): Promise<unknown> {
  const user = await auth0.getUser(user_id)
  if(!user) throw notFoundError
  try {
    const db_response = await postgres.query(queries.getUnassociatedLeadByEmail(), [user.email])
    const leads = db_response.rows
    const promises = leads.map(lead => postgres.query(queries.updateLeadUserId(), [lead.id, user.user_id]))
    return await Promise.all(promises)
  } catch (error) {
    logger.error('associateLeadsWithUserError:', error)
    throw serverError
  }
}

// function buildEmailContext - MIGRATED IN applications-controller

/**
 *
*
* @param  { EmailContext } email_context
* @param  { EmailContext } email_context.templateParams
* @param  { string } email_context.schoolType
* @param  { Array<string> } email_context.userEmails
* @returns {Promise} response object with the response from the database
*/
export async function sendEnrollmentNotification(email_context: any) {
  const { template_params, school_type, users_emails } = email_context

  let email_params = {} as EmailParams
  logger.debug('sendEnrollmentNotification', users_emails)
  logger.debug('sendEnrollmentNotification', school_type)
  if (users_emails && users_emails.length > 0 && (SCHOOL_ACCOUNT_TYPES.CLAIMED_PAID == school_type || SCHOOL_ACCOUNT_TYPES.CLAIMED_UNPAID == school_type)){
    const htmlTemplate = enrrolmentInterestNotificationClaimedPaid(template_params)
    const tag = (SCHOOL_ACCOUNT_TYPES.CLAIMED_PAID == school_type)? 'enrollment-interest-claimed--paid':'enrollment-interest-claimed--unpaid'

    email_params = {
      to: users_emails,
      subject: `You just received an Enrollment Interest Form!`,
      html: htmlTemplate,
      tags: ['notification', tag],
      bcc: REROUTE_5
    }
  } else if (SCHOOL_ACCOUNT_TYPES.UNCLAIMED == school_type) {
    const htmlTemplate = enrrolmentInterestNotificationUnclaimed(template_params)
    email_params = {
      to: INFO_RECIPIENT,
      subject: `Enrollment Interest Form Received for Unclaimed School`,
      html: htmlTemplate,
      tags: ['notification', 'enrollment-interest-unclaimed'],
      bcc: SALES_RECIPIENT
    }
  } else {
    return
  }
  sendEmail(email_params).catch(() => {
    logger.error('sendEnrollmentNotification -> sendEmailError')
  })
}

/**
*
* @param  { AutoScholaMatchChild } child_info
* @param  { AutoScholaMatchParent } parent_info
* @returns {Promise} response object with the response from the database
*/
export async function verifyLeadDuplicate(child_info: AutoScholaMatchChild, parent_info: AutoScholaMatchParent): Promise<boolean> {
  // make the query to search the records from main school and find if there is any duplicate lead
  const db_response = await postgres.query(queries.getLeadByEmail(), [ parent_info.email])
  // cross check the leads from the main and duplicate schools
  // this will return only the leads we need to migrate (no duplicate ones)
  const leads_to_migrate = db_response.rows.filter(lead2C => (
    // this is a filter if you need to add another field to prevent duplicate add it at the end
    lead2C.school_id == parent_info.school_id &&
    lead2C.parent_first_name.toLowerCase() == parent_info.parent_first_name.toLowerCase() &&
    lead2C.parent_last_name.toLowerCase() == parent_info.parent_last_name.toLowerCase() &&
    lead2C.email.toLowerCase() == parent_info.email.toLowerCase() &&
    lead2C.child_first_name.toLowerCase() == child_info.child_first_name.toLowerCase() &&
    lead2C.child_last_name.toLowerCase() == child_info.child_last_name.toLowerCase() &&
    lead2C.grade.toLowerCase() == child_info.grades[0].toLowerCase()
  ))
  return leads_to_migrate.length !== 0
}

/**
*
* @param  {} params
* @returns {Promise} response object with the response from the database
*/
export async function getNotMatchedSchools(lead_info: Lead, schools: Array<School>): Promise<unknown> {
  // make the query to search the records from main school and find if there is any duplicate lead
  try {
    const res_existing_leads = await postgres.query(queries.getLeadByEmail(), [lead_info.email])
    // cross check the leads from the db with the schools to get the schools
    // without a lead
    return schools.filter((schools2C: School) => res_existing_leads.rows.every((lead2C) => (
      lead2C.school_id !== schools2C.id ||
      lead2C.parent_first_name.toLowerCase() !== lead_info.parent_first_name.toLowerCase() ||
      lead2C.parent_last_name.toLowerCase() !== lead_info.parent_last_name.toLowerCase() ||
      lead2C.email.toLowerCase() !== lead_info.email.toLowerCase() ||
      lead2C.child_first_name.toLowerCase() !== lead_info.child_first_name.toLowerCase() ||
      lead2C.child_last_name.toLowerCase() !== lead_info.child_last_name.toLowerCase() ||
      lead2C.grade !== lead_info.grade
    )))
  } catch (error) {
    logger.error(error)
    throw serverError
  }

}

/**
*
* @param  {number} school_id
* @param {Array<LeadFields>} lead_fields
* @returns {Promise} response object with the response from the database
*/
export async function setLeadFields(school_id: number, lead_fields: Array<LeadFields>) {
  await validate(lead_fields, schemas.set_lead_fields)
  try {
    const current_lead_fields = await getLeadFields(school_id)
    const add_lead_fields: Array<LeadFields> = []
    const update_lead_fields: Array<LeadFields> = []

    lead_fields.map(lead_field => {
      const found_lead_field = current_lead_fields.filter(x => x.field_indentifier === lead_field.field_indentifier)[0]
      if(!found_lead_field){
        add_lead_fields.push(lead_field)
      } else {
        lead_field.id = found_lead_field.id
        update_lead_fields.push(lead_field)
      }
    })

    const now = moment().toDate()
    // Build bulk insert for the new lead_fields
    if(add_lead_fields.length) {
      const bulk_params = add_lead_fields.map(lead_field => ({
        school_id,
        display_name: lead_field.display_name,
        enabled: lead_field.enabled,
        field_indentifier: lead_field.field_indentifier,
        type: lead_field.type,
        created_at: now,
        updated_at: now
      }))
      const bulk = buildBulkInsert(leads_queries.bulkInsertLeadFields(), bulk_params)
      await postgres.query(bulk.text, bulk.values)
    }

    // Update lead_fields
    for(let i = 0; i < update_lead_fields.length; i++) {
      const values = [
        school_id,
        now,  //updated at
        update_lead_fields[i].type,
        update_lead_fields[i].field_indentifier,
        update_lead_fields[i].display_name,
        update_lead_fields[i].enabled,
        update_lead_fields[i].id
      ]
      await postgres.query(queries.updateLeadFields(), values)
    }

    return await getLeadFields(school_id)
  } catch (error) {
    logger.error('setLeadFieldsError:',error)
    throw serverError
  }
}

/**
*
* @param  {number} school_id
* @returns {Promise} response object with the response from the database
*/
export async function getLeadFields(school_id: number): Promise<Array<LeadFieldDB>> {
  try {
    const db_response_lead_fields = await postgres.query(queries.getLeadFields(), [school_id])
    return db_response_lead_fields.rows
  } catch (error) {
    logger.error('getLeadFieldsError:', error)
    throw serverError
  }
}

/**
*
* @param  {number} school_id
* @param  {Array<string | number>} scope
* @returns {Promise} array of lead sources from the database plus the system sources
*/
export async function getLeadSources(school_id: number, scope: Array<string | number>): Promise<Array<unknown>> {
  try {
    const db_response = await postgres.query(queries.getLeadSources(), [school_id])
    const system_lead_sources = scope.includes('superadmin') ? getSystemAdminLeadSources() : getSystemLeadSources()
    return [...db_response.rows, ...system_lead_sources]
  } catch (error) {
    logger.error('getLeadSourcesError:', error)
    throw serverError
  }
}

/**
* Creates a new lead source for the given school
* @param  {number} school_id
* @param  {string} name
* @returns {Promise} response object with the response from the database
*/
export async function addLeadSource(school_id: number, name: string): Promise<LeadSource> {
  try {
    const params = { school_id, name, created_at: moment().toDate(), updated_at: moment().toDate(), deleted: false }
    const values = await getObjectValues(params)
    const db_response = await postgres.query(insertQuery('lead_sources', params), values)
    return db_response.rows[0]
  } catch (error) {
    logger.error('addLeadSourceError:', error)
    throw serverError
  }
}

/**
 * Updates a lead source's details for the given school.
 * @param  {number} school_id - The ID of the school associated with the lead source.
 * @param  {number} source_id - The ID of the lead source to be updated.
 * @param  {string} name - The new name for the lead source.
 * @returns {Promise<LeadSource>} - Promise object representing the updated lead source from the database.
 */
export async function updateLeadSource(school_id: number, source_id: number, name: string, user_id: string, oldName: string): Promise<LeadSource> {
  try {
    const values = [school_id, source_id, name, new Date()]
    const db_response = await postgres.query(queries.updateSchoolLeadSource(), values)

    if (db_response.rows.length === 0) {
      throw notFoundError
    }

    await insertLogChange({
      school_id: school_id,
      user_id: user_id,
      table_name: 'lead_sources',
      row_id: db_response.rows[0].id,
      operation_type: 'UPDATE',
      old_values: { name: oldName },
      new_values: { name: name },
      created_at: moment().toDate()
    })

    return db_response.rows[0]
  } catch (error) {
    logger.error('updateLeadSourceError:', error)
    throw serverError
  }
}

/**
 * Soft deletes (marks as removed) a lead source for the given school.
 * @param {number} school_id - The ID of the school associated with the lead source to be removed.
 * @param {number} source_id - The ID of the lead source to be soft deleted.
 * @returns {Promise<LeadSource>} - Promise object representing the lead source marked as deleted.
 */
export async function removeLeadSource(school_id: number, source_id: number, user_id: string): Promise<RemovedLeadSource> {
  try {
    await postgres.query('BEGIN')
    const values = [school_id, source_id]

    //count affected leads
    const countResponse = await postgres.query(queries.countLeadsBySource(), values)
    const affected_leads = parseInt(countResponse.rows[0].count, 10)

    //unlink source leads
    await postgres.query(queries.leadSourceUnlink(), values)

    const db_response = await postgres.query(queries.removeSchoolLeadSource(), values)

    if (db_response.rows.length === 0) {
      throw notFoundError
    }

    await insertLogChange({
      school_id: school_id,
      user_id: user_id,
      table_name: 'lead_sources',
      row_id: db_response.rows[0].id,
      operation_type: 'UPDATE',
      old_values: { deleted: false },
      new_values: { deleted: true },
      created_at: moment().toDate()
    })

    await postgres.query('COMMIT')

    return { lead_source: db_response.rows[0], affected_leads }
  } catch (error) {
    await postgres.query('ROLLBACK')
    logger.error('removeLeadSourceError:', error)
    throw serverError
  }
}

/**
*
* @param  {number} school_id
* @returns {Promise} response object with the response from the database
*/
export async function getLeadStatuses(school_id: number): Promise<Array<LeadStatus>> {
  try {
    const db_response = await postgres.query(queries.getLeadStatuses(), [school_id])
    return db_response.rows
  } catch (error) {
    logger.error('getLeadStatusesError:', error)
    throw serverError
  }
}

/**
*
* @param  {number} school_id
* @param  {string} name
* @returns {Promise} response object with the response from the database
*/
export async function addLeadStatus(school_id: number, name: string): Promise<LeadStatus> {
  try {
    const params = { school_id, name, created_at: moment().toDate(), updated_at: moment().toDate(), deleted: false }
    const values = await getObjectValues(params)
    const db_response_search = await postgres.query(queries.getLeadStatusByName(), [school_id, name.trim().toLowerCase()])
    if(db_response_search.rows.length>0) throw new ConflictError('duplicated')

    const db_response = await postgres.query(insertQuery('lead_statuses', params), values)
    return db_response.rows[0]
  } catch (error) {
    logger.error('addLeadStatusError:', error)
    if (error.message.includes('duplicated'))
      throw new ConflictError('duplicated')
    throw serverError
  }
}

/**
 * Updates the name of a lead status for the given school.
 * @param {number} school_id - The ID of the school associated with the lead status to be updated.
 * @param {number} stage_id - The ID of the lead stage to be updated.
 * @param {string} name - The new name for the lead status.
 * @returns {Promise<LeadStatus>} - Promise object representing the updated lead status from the database.
 */
export async function updateLeadStatus(school_id: number, stage_id: number, name: string, user_id:string, oldName:string): Promise<LeadStatus> {
  try {
    const values = [school_id, stage_id, name]
    const db_response = await postgres.query(queries.updateSchoolLeadStage(), values)

    if (db_response.rows.length === 0) {
      throw notFoundError
    }

    await insertLogChange({
      school_id: school_id,
      user_id: user_id,
      table_name: 'lead_statuses',
      row_id: db_response.rows[0].id,
      operation_type: 'UPDATE',
      old_values: { name: oldName },
      new_values: { name: name },
      created_at: moment().toDate()
    })

    return db_response.rows[0]
  } catch (error) {
    logger.error('updateLeadStatusError:', error)
    throw serverError
  }
}


/**
 * Soft deletes (marks as removed) a lead status for the given school.
 * @param {number} school_id - The ID of the school associated with the lead status to be removed.
 * @param {number} stage_id - The ID of the lead stage associated with the status to be removed.
 * @returns {Promise<LeadStatus>} - Promise object representing the lead status marked as deleted.
 */
export async function removeLeadStatus(school_id: number, stage_id: number, user_id: string): Promise<RemovedLeadStatus> {
  try {
    await postgres.query('BEGIN')
    const values = [school_id, stage_id]

    //count affected leads
    const countResponse = await postgres.query(queries.countLeadsByStage(), values)
    const affected_leads = parseInt(countResponse.rows[0].count, 10)

    //unlink stage leads
    await postgres.query(queries.leadStageUnlink(), values)

    const db_response = await postgres.query(queries.removeSchoolLeadStage(), values)

    if (db_response.rows.length === 0) {
      throw notFoundError
    }

    await insertLogChange({
      school_id: school_id,
      user_id: user_id,
      table_name: 'lead_statuses',
      row_id: db_response.rows[0].id,
      operation_type: 'UPDATE',
      old_values: { deleted: false },
      new_values: { deleted: true },
      created_at: moment().toDate()
    })

    await postgres.query('COMMIT')

    return { lead_stage: db_response.rows[0], affected_leads }
  } catch (error) {
    await postgres.query('ROLLBACK')
    logger.error('removeLeadStatusError:', error)
    throw serverError
  }
}


/**
* Ex]
* @param  {number} school_id
* @returns {Promise<Array<unknown>>}
*/
export async function getExportTypeApplicationsForSchool(school_id: number): Promise<Array<unknown>> {
  try {
    const db_response = await postgres.query(queries.getExportTypeApplicationsForSchool(), [school_id])
    return db_response.rows
  } catch (error) {
    logger.error(error)
    throw serverError
  }
}

/**
*
* @param  {number} school_id
* @param  {Array<number>} leads_ids
* @param  {string} deleted_by
* @returns {Promise} array with the logically deleted leads
*/
export async function deleteLeads(school_id: number, leads_ids: Array<number>, deleted_by: string): Promise<unknown> {
  try {
    const db_response = await postgres.query(queries.deleteLeads(), [leads_ids, school_id, deleted_by])
    return db_response.rows
  } catch (error) {
    logger.error('deleteLeadsError:', error)
    throw serverError
  }
}

/**
* Get a lead's source cost
* @param {LeadSourceCostGet} query_params
* @param {boolean} only_count
* @returns {Promise} response object with the response from the database
*/
export async function getLeadSourceCost(query_params: LeadSourceCostGet, only_count = false) {
  await validate(query_params, schemas.get_lead_source_cost)
  try {
    if(!query_params.page) query_params.page = 1
    if(!query_params.pageSize) query_params.pageSize = 10
    if(!query_params.fieldSort) query_params.fieldSort = 'created_at'
    if(!query_params.fieldDirection) query_params.fieldDirection = 'DESC'

    const {main_query, count_query, values} = queries.leadSourceCost(query_params)
    const page_offset = (query_params.page - 1) * query_params.pageSize
    const db_response_main = await postgres.query(main_query, [...values, query_params.pageSize, page_offset])

    let count = 0
    if(!only_count) {
      const db_response_count = await postgres.query(count_query, values)
      count = Number(db_response_count.rows[0].count)
    }

    return {
      results: db_response_main.rows,
      pagination: {
        page: query_params.page,
        pageSize: query_params.pageSize,
        rowCount: count,
        pageCount: Math.ceil(count/query_params.pageSize)
      }
    }
  } catch (error) {
    logger.error('getLeadSourceCostError:',error)
    throw serverError
  }
}

/**
*
* @param  {LeadSourceCostPost} params
* @returns {Promise} response object with the inserted record
*/
export async function addLeadSourceCost(params: LeadSourceCostPost): Promise<unknown> {
  await validate(params, schemas.add_lead_source_cost)
  try {
    const insert_params = {
      ...params,
      deleted: false,
      created_at: moment().toDate(),
    }
    const values = await getObjectValues(insert_params)
    const db_response = await postgres.query(insertQuery('lead_source_costs', insert_params), values)
    return db_response.rows[0]
  } catch (error) {
    logger.error('addLeadSourceCostError:', error)
    throw serverError
  }
}

/**
*
* @param  {number} id
* @param  {LeadSourceCostsPatch} params
* @returns {Promise} updated object from the database
*/
export async function updateLeadSourceCost(id: number, params: LeadSourceCostsPatch): Promise<unknown> {
  await validate(params, schemas.update_lead_source_cost)
  try {
    const update_params = {
      ...params,
      updated_at: moment().toDate(),
    }
    const built_update = buildUpdate('lead_source_costs', { id }, update_params)
    const db_response = await postgres.query(built_update.text, built_update.values)
    return db_response.rows[0]
  } catch (error) {
    logger.error('updateLeadSourceCostError:', error)
    throw serverError
  }
}

/**
*
* @param  {number} id
* @returns {Promise} an object with the logically deleted record
*/
export async function deleteLeadSourceCost(id: number): Promise<unknown> {
  try {
    const update_params = {
      deleted: true,
      updated_at: moment().toDate(),
    }
    const built_update = buildUpdate('lead_source_costs', { id }, update_params)
    const db_response = await postgres.query(built_update.text, built_update.values)
    return db_response.rows[0]
  } catch (error) {
    logger.error(error)
    throw serverError
  }
}

/**
*
* @param  {number} school_id
* @returns {Promise} response array with the results from the database
*/
export async function getLeadsToReminder(school_id: number): Promise<Array<unknown>> {
  try {
    const db_response = await postgres.query(queries.getLeadsToRemind(), [school_id])
    return db_response.rows
  } catch (error) {
    logger.error('getLeadsToReminderError:', error)
    throw serverError
  }
}

/**
*
* @param  {number} school_id
* @returns {Promise} response object with the response from the database
*/
export async function markLeadsAsReminded(school_id: number, period?: Array<any>): Promise<Array<unknown>> {
  try {
    const periods = (period && period.length > 0) ? period : REMINDER_PERIODS
    const updates = []
    for (let i = 0; i < periods.length; i++) {
      const current_period = periods[i]
      const values = [
        `${current_period.init} hours`,
        school_id,
        current_period.init,
        current_period.end,
      ]
      if(current_period.lastNotify) values.push(current_period.lastNotify)
      updates.push(postgres.query(queries.markLeadsAsReminded(current_period.lastNotify), values))
    }
    return await Promise.all(updates)
  } catch (error) {
    logger.error('markLeadsAsRemindedError:', error)
    throw serverError
  }
}

/**
*
* @returns {Promise} responds with true if no error arises
*/
export async function handleRemainderLeadEmail(): Promise<unknown> {
  const to_remind_by_period = await getCountBySchool()
  const filtered = filterOut(to_remind_by_period)
  try {
    const email_promises: Promise<unknown>[] = []
    for (let index = 0; index < filtered.length; index++) {
      const to_remind = filtered[index]
      const {data, period} = to_remind
      for (let j = 0; j < data.length; j++) {
        const row = data[j]
        const { school_id } = row
        markLeadsAsReminded(school_id, [period])
        const users_details = await getSchoolUserEmails(school_id)
        const {template, subject, period_tag} = getPeriodEmailInfo(period.init)
        users_details.forEach(user => {
          logger.info({email: user.email, subject, period_tag})
          email_promises.push(sendEmail({
            to: user.email,
            subject,
            html: template({
              name: user.name,
              schola_link: `${FRONTEND_HOST_ADMIN}/admin/v2/schools/${school_id}/relationship-manager?status=new`,
            }),
            bcc: INFO_RECIPIENT,
            tags: ['notification', 'reminder', period_tag]
          }))
        })
      }
    }
    await Promise.all(email_promises)
    return true
  } catch (error) {
    logger.error('handleRemainderLeadEmailError:', error)
    throw serverError
  }
}

/**
*
* @param  {number} school_id
* @param  {string} name
* @returns {Promise} response object with the response from the database
*/
export async function getOrCreateLeadSourceByName(school_id: number, name: string): Promise<LeadSource> {

  try {

    switch (name.toLowerCase()) {
    case 'scholamatch':
      return getSystemScholaMatch()
    case 'generallead':
      return getSystemGeneralLead()
    case 'scholamatch2':
      return getSystemScholaMatch2()
    case 'schola school campaign':
      return getSystemScholaSchoolCampaign()
    case 'scholamatch3':
      return getSystemScholaMatch3()
    case 'potentialleadsro':
      return getSystemPotentialLeadsRO()
    case 'autoscholamatch':
      return getSystemAutoScholaMatch()
    case 'pre-qualify':
      return getSystemScholaPreQualify()
    case 'scholamatch-spot':
      return getSystemScholaMatchSPOT()
    case 'scholamatch spot':
      return getSystemScholaMatchSPOT()
    case 'schola profile a':
      return getSystemScholaProfileA()
    case 'schola general campaign - asm':
      return getSystemScholaGeneralCampaign()
    }
    const db_response = await postgres.query(queries.getLeadSource(), [school_id, name])
    if (db_response.rowCount > 0) return db_response.rows[0]

    return await addLeadSource(school_id, name)
  } catch (error) {
    logger.error('getOrCreateLeadSourceByNameError:', error)
    throw serverError
  }
}

/**
*
* @param  {number} school_id
* @returns {Promise} response object with the response from the database
*/
export async function getGeneralLeads(school_id: number): Promise<Array<unknown>> {
  try {
    const db_response = await postgres.query(queries.getGeneralLeads(), [school_id])
    return db_response.rows
  } catch (error) {
    logger.error('getGeneralLeadsError:', error)
    if(error.statusCode === notFoundError.statusCode) throw notFoundError
    throw serverError
  }
}

/**
*
* @param  {number} school_id
* @param  {string} user_id
* @param  {number} search_history_id
* @returns {Promise} response object with the response from the database
*/
export async function convertPotentialLeadToLead(school_id: number, user_id: string, search_history_id: number): Promise<unknown> {
  try {
    let search_history_lead = await searchHistory.getSearchHistoryLeadByUserId({school_id, user_id})
    if(!search_history_lead) {
      const user_details = await usersController.getUserDetails(user_id)
      const user_profile = await usersController.getUserProfile(user_id) as UserProfileLeads
      const user_name = splitFullName(user_details.name || '')
      const lead_data = {
        lead_source_id : -6, //PotentialLeadsRO
        school_id,
        preferred_contact: 'email',
        phone: user_profile.phone || '',
        email: user_profile.email || user_details.email,
        parent_first_name: user_profile.first_name|| user_name.first_name,
        parent_last_name: user_profile.last_name|| user_name.last_name,
        child_first_name: '',
        child_last_name:'',
        grade:'',
        status:'new',
        zipcode: user_profile?.zipcode || undefined
      } as any

      const lead = await this.createLead(lead_data)
      if(lead) {
        search_history_lead = await searchHistory.insertOrUpdateSearchHistoryLead({
          school_id,
          user_id,
          search_history_id,
          lead_id: lead.id
        })
        return {success: true, searchHistoryLead: search_history_lead}
      } else {
        return {success: false, messageError: 'lead could not be created'}
      }
    } else {
      return {
        success: true,
        searchHistoryLead: search_history_lead,
        note: 'this user already has a lead'
      }
    }
  } catch (error) {
    logger.error('convertPotentialLeadToLeadError', error)
    throw serverError
  }
}

/**
*
* @param  {number} school_id
* @param  {number} lead_id
* @returns {Promise} response object with the response from the database
*/
export async function getLeadTransportation(school_id: number, lead_id: number): Promise<LeadTransportationResponse> {
  try {
    const db_response = await postgres.query(queries.getLeadTransportation(), [lead_id, school_id])
    const reasons = LEAD_TRANSPORTATION_REASONS.map(reason => {
      let checked = false
      if(db_response.rowCount > 0) {
        const found_reason = db_response.rows.find(db_reason => db_reason.reason_id === reason.reason_id)
        if(found_reason) {
          checked = true
        }
      }
      return {
        reason_id: reason.reason_id,
        label: reason.label,
        checked,
      }
    })
    const now = moment().toDate()
    const result = db_response.rows[0] || {
      id: -1,
      school_id: school_id,
      lead_id: lead_id,
      created_at: now,
      updated_at: now,
      need: 'No',
      carpool: 'No',
      free_transport_preferred: 'No',
      free_transport_preferred_other: '',
    }
    return {
      ...result,
      reasons,
    }
  } catch (error) {
    logger.error('getLeadTransportationError:', error)
    throw serverError
  }
}

/**
*
* @param  {number} school_id
* @param  {string} since_date
* @returns {Promise} the total count of latest new leads for the given school since the given date
*/
type LatestLeads = {
  leads: number
  applications: number
  accepted: number
}
export async function getCountLatestNewLeads(school_id: number, since_date: string): Promise<LatestLeads> {
  const formated_date = moment(since_date).format('YYYY-MM-DD HH:mm:ss')
  try {
    const db_response = await postgres.query(queries.getCountLatestNewLeads(), [school_id, formated_date])

    const applicationsQuery = await postgres.query(queries.getCountLatestNewApplications(), [school_id, formated_date])

    const acceptedQuery = await postgres.query(queries.getCountLatestNewAccepted(), [school_id, formated_date])

    return {
      leads: db_response.rows[0]?.count || 0,
      applications: applicationsQuery.rows[0]?.count || 0,
      accepted: acceptedQuery.rows[0]?.count || 0
    }

  } catch (error) {
    logger.error('getCountLatestNewLeadsError:', error)
    throw serverError
  }
}

/**
*
* @param  {number} school_id
* @returns {Promise} formatted array with the response from the database
*/
export async function getLeadYearOptions(school_id: number): Promise<Array<unknown>> {
  const current_year = new Date().getFullYear()
  const result = [{
    label: `${current_year - 1}-${current_year}`,
    value: `${current_year - 1}-${current_year}`,
  }, {
    label: `${current_year}-${current_year + 1}`,
    value: `${current_year}-${current_year + 1}`,
  }, {
    label: `${current_year + 1}-${current_year + 2}`,
    value: `${current_year + 1}-${current_year + 2}`,
  }, {
    label: `${current_year + 2}-${current_year + 3}`,
    value: `${current_year + 2}-${current_year + 3}`,
  }, {
    label: `${current_year + 3}-${current_year + 4}`,
    value: `${current_year + 3}-${current_year + 4}`,
  }, {
    label: `${current_year + 4}-${current_year + 5}`,
    value: `${current_year + 4}-${current_year + 5}`,
  }, {
    label: `N/A`,
    value: `N/A`,
  }] // We initialize the result array to have these objects
  try {
    const db_response = await postgres.query(queries.getLeadYearOptions(), [school_id])
    if(db_response.rowCount > 0) {
      db_response.rows.forEach(row => {
        const found = result.find(item => item.value === row.year)
        if(!found) result.push({label: row.year, value: row.year})
      })
      result.sort( (a, b) => {
        if(a.value < b.value) return -1
        if(a.value > b.value) return 1
        return 0
      })
    }
    return result
  } catch (error) {
    logger.error('getLeadYearOptionsError:', error)
    throw serverError
  }
}

export async function getIntegrationStatusBySchool(school_id: number) {
  const intgStatus = [
    { id: 'rv', name: 'RV - Accepted' },
    { id: 'rc', name: 'RC - Accepted' },
    { id: 'rp', name: 'RP - Application Received' },
    { id: 'ac', name: 'AC - Application Received' },
    { id: 'of', name: 'OF - Application Received' },
    { id: 'po', name: 'PO - Application Received' },
    { id: 'su', name: 'SU - Application Received' },
    { id: 'wa', name: 'WA - Waitlisted' },
    { id: 'ca', name: 'CA - Archived' },
    { id: 'de', name: 'DE - Archived' },
    { id: 'rs', name: 'RS - Archived' },
    { id: 'wd', name: 'WD - Archived' },
  ]

  try {
    const db_response = await postgres.query(queries.getIntegrationStatus(), [school_id])
    const status = db_response.rows.map(({ sync_intg_status }) => sync_intg_status)
    const options = intgStatus.filter((item) => status.includes(item.id))

    return options

  } catch (error) {
    logger.error('getIntegrationStatusBySchool:', error)
    throw serverError
  }
}

export async function getIntegrationCampus(school_id: number) {
  try {
    const db_response = await postgres.query(queries.getIntegrationCampus(), [school_id])
    const options = db_response.rows.map(({ campus }) => ({ id: campus, name: campus }))

    return options

  } catch (error) {
    logger.error('getIntegrationStatusBySchool:', error)
    throw serverError
  }
}

/**
*
* @param  {number} lead_id
* @param  {number} school_id
* @returns {Promise} response object with the response from the database
*/
export async function getLeadsRelated(lead_id: number, school_id: number): Promise<Array<unknown>> {
  try {
    const db_response = await postgres.query(queries.getLeadsRelated(), [lead_id, school_id])
    return db_response.rows
  } catch (error) {
    logger.error(error)
    throw serverError
  }
}

/**
*
* @param  {} params
* @returns {Promise} response object with the response from the database
*/
export async function updateFeedback(params: EditLeadForSchool): Promise<Lead> {
  await validate(params, schemas.parent_feedback)
  try {
    const lead = await this.getLead(params.leads_ids[0], params.school_id)
    if (!lead) throw notFoundError
    if (lead.user_id !== params.user_id) throw unauthorizedError
    await this.editLeadsForSchool(params)
    return lead
  } catch (error) {
    logger.error(error)
    throw serverError
  }
}

/**
*
* @param  {} params
* @returns {Promise} response object with the response from the database
*/
export async function updateAttendance(params: EditLeadForSchool): Promise<Lead> {
  await validate(params, schemas.lead_attendance)
  try {
    const lead = await this.getLead(params.leads_ids[0], params.school_id)
    if (!lead) throw notFoundError
    if (lead.user_id !== params.user_id) throw unauthorizedError
    await this.editLeadsForSchool(params)
    return lead
  } catch (error) {
    logger.error(error)
    throw serverError
  }
}

/**
*
* @param  {number} lead_id
* @param  {number} school_id
* @param  {string} message
* @returns {Promise} formatted response object
*/
export async function contactSPOT(lead_id: number, school_id: number, message: string): Promise<unknown> {
  const lead = await getLead(lead_id, school_id)
  if(!lead) throw notFoundError
  const school = await schoolController.getSchoolById(school_id)
  if(!school) throw notFoundError
  const lead_data = {
    parent_name: `${lead.parent_first_name} ${lead.parent_last_name}`,
    student_name: `${lead.child_first_name} ${lead.child_last_name}`,
    email: `${lead.email}`,
    phone: `${lead.phone}`,
    grade: `${lead.grade}`,
    school_id: lead.school_id,
    school_name: `${lead.school_name}`,
    status: `${lead.status}`,
    lead_id: lead.id,
    message,
  }
  const email_data: EmailParams = {
    to: SPOT_EMAIL,
    subject: `Help for ${lead.child_first_name} ${lead.child_last_name} (Lead: ${lead.id})`,
    html: getContactSPOTTemplate(lead_data),
  }
  try {
    await sendEmail(email_data)
    return {ok: true}
  } catch (error) {
    logger.error('contactSPOTError:', error)
    return {ok: false}
  }
}

/**
*
* @param  {number} school_id
* @param  {ExportApplicationsForSchool} params
* @returns {Promise} response csv with the exported applications
*/
export async function exportApplicationsForSchool(school_id: number, params: ExportApplicationsForSchool): Promise<string> {
  await validate(params, schemas.export_applications)
  try {
    let db_response
    let external = false
    if(params.application_source === 'jotform') {
      db_response = await postgres.query(queries.getApplicationsForExportJotform(), [school_id, params.application_form_id])
      external = true
    } else {
      db_response = await postgres.query(queries.getApplicationsForExport(), [school_id])
    }
    if(db_response.rowCount === 0) return ''
    const {data, columns} = await getApplicationCSV(db_response.rows, external)
    const csv_stream = stringify(data, {columns, header: true })
    return csv_stream
  } catch (error) {
    logger.error('exportApplicationsForSchoolError:', error)
    throw serverError
  }
}

/**
*
* @param  {number} school_id
* @param  {number} application_id
* @param  {boolean} external
* @param  {string} token
* @returns {Promise} response csv with the given application
*/
export async function exportApplicationsIndividual(school_id: number, application_id: number, external: boolean): Promise<string> {
  try {
    const db_response = await postgres.query(queries.getApplicationsForExportIndividual(), [school_id, application_id])
    if(db_response.rowCount === 0) return ''
    const {data, columns} = await getApplicationCSV(db_response.rows, external)
    const csv_stream = stringify(data, {columns, header: true })
    return csv_stream
  } catch (error) {
    logger.error('exportApplicationsIndividualError:', error)
    throw serverError
  }
}

/**
*
* @param  {} params
* @returns {Promise} response object with the response from the database
*/
export async function addLeadFB(school_id: number, params: LeadFB): Promise<Array<unknown>> {
  await validate(params, schemas.add_lead_fb)
  if(params?.source === '' || params?.source === 'null') params.source = 'External'
  if(school_id === 0) return []
  const school = await schoolController.getSchoolById(school_id)
  if(!school) throw notFoundError
  params.lead_source_id = -4 // Schola School Campaign
  const names = params.parent_name.split(' ')
  params.parent_first_name = names[0]
  params.parent_last_name = names.length > 1 ? names[1] : ''
  if(params.parent_last_name && params.parent_last_name.toLowerCase() === 'null') params.parent_last_name = ''
  params.year = (params.year && params.year.trim() === '') ? null : params.year
  delete params.parent_name
  // Students
  const students = params.students
  delete params.students
  const payloads = students.map( student => {
    const student_names = student.child_name.split(' ')
    student.grade = student.grade.replace(/st/ig, '').replace(/th/ig, '').replace(/nd/ig, '') //remove ordinal nomenclature
    student.child_first_name = student_names[0]
    student.child_last_name = student_names.length > 1 ? student_names[1] : ''
    student.child_birthdate = student.birthdate || null
    if(student.child_last_name && student.child_last_name.toLowerCase() === 'null') student.child_last_name = ''
    delete student.child_name
    return Object.assign({}, {school_id, user_id: '', ...params}, student)
  })

  const promises = []
  for (let index = 0; index < payloads.length; index++) {
    const payload:any = payloads[index]

    if(payload.source) {
      if(payload.source.trim() !== '') {
        const source = await getOrCreateLeadSourceByName(payload.school_id, payload.source)
        delete payload.source
        payload.lead_source_id = source.id
        promises.push(createLead(payload))
      } else {
        delete payload.source
        promises.push(createLead(payload))
      }
    } else {
      promises.push(createLead(payload))
    }
  }
  try {
    const created_leads = await Promise.all(promises)
    const payload = Object.assign({}, params, {
      students: created_leads.map( async (lead: any) => {
        await leadUtils.addLeadHistory(lead.id, 'status', 'new')
        return lead
      })
    })
    // building email to send to the parent
    const email_params = {
      school,
      lead: payload,
      admin_host: FRONTEND_HOST_ADMIN,
      search_host: FRONTEND_HOST_SEARCH
    }
    const email_context = await applicationsController.buildEmailContext(email_params as any)

    if (school_id != DEFAULT_SCHOOL_CS) {
      await sendEnrollmentNotification(email_context)
    }
    const notes_promises = []
    for (let index = 0; index < created_leads.length; index++) {
      const lead = created_leads[index]
      if(lead.note) {
        notes_promises.push(notesController.createNote({
          object_type: 'lead',
          object_id: lead.id,
          user_id: '',
          note: lead.note
        }))
      }
    }
    await Promise.all(notes_promises)
    return created_leads
  } catch (error) {
    logger.error('addLeadFBError:', error)
    throw serverError
  }
}

/**
*
* @param  {} params
* @returns {Promise} response object with the response from the database
* new function to create leads with autoscholamatch
*/
export async function addLeadWithAutoscholaMatch(lead: Lead, school_id: number = null, lead_source_id = -7): Promise<unknown> {
  const leads_added = []
  try {
    const zipcodeN = Number(lead.zipcode)
    const lead_info = {
      grade: lead.grade,
      child_first_name: lead.child_first_name,
      child_last_name: lead.child_last_name,
      child_birthdate: lead.child_birthdate,
      custom_field_1: lead.custom_field_1 || null,
      custom_field_2: lead.custom_field_2 || null,
      custom_field_3: lead.custom_field_3 || null,
      custom_field_4: lead.custom_field_4 || null,
      custom_field_5: lead.custom_field_5 || null,
      address: lead.address || null,
      address_description: lead.address || null,
      parent_first_name: lead.parent_first_name,
      parent_last_name: lead.parent_last_name,
      email: lead.email,
      state: findStateByZipcode.findStateByZipcode(zipcodeN),
      zipcode: lead.zipcode,
      parent_relationship: lead.parent_relationship,
      phone: lead.phone,
      preferred_contact: lead.preferred_contact,
      tour_requested: lead.tour_requested,
      note: lead.note,
      language: lead.language,
      lead_source_id: lead_source_id
    } as any

    const fromCatchBucketSchool = (school_id == DEFAULT_SCHOOL_CS) ? true : false

    const { direction, zipcode, address, city, state_short } = await googleSearches.searchByZipState(lead_info.zipcode, lead_info?.address)
    // If we found a direction
    let schools = []
    if (direction?.lat && direction?.lon) {
      lead_info['address'] = address
      lead_info['address_description'] = address
      lead_info['zipcode'] = zipcode
      lead_info['latitude'] = direction?.lat
      lead_info['longitude'] = direction?.lon
      lead_info['city'] = city
      lead_info['state'] = state_short
      // building the data for the request
      const search_req = {
        ...direction,
        grades: [lead_info.grade],
        f: [],
        sped_service: false,
        ignore: 'true',
        bestMatch: true,
        pageSize: 5000,
        page: 1
      } as any

      // getting the nearest schools with schola match
      const schoolsRes = await schoolController.searchSchoolsV2(search_req)
      schools = schoolsRes.results
    } else {
      schools = [{ id: DEFAULT_SCHOOL_CS, reason: 'No Matching Address' }]
    }

    const schola_tier: any = {}
    if (schools.length != 0 && schools[0]?.id != DEFAULT_SCHOOL_CS){
      const SCHOOLS_TO_PREVENT = [128377]
      const SCHOLA_PLANS = ['payg', 'scholarecruiterpro']

      schools = await this.getNotMatchedSchools(lead_info, schools)

      const campaign_cases: any = []
      if (school_id == null || school_id >= 0) {
        const db_res = await postgres.query(schools_queries.getActiveSchools(), [])
        db_res.rows.map((school: any) => {
          campaign_cases.push(school.id)
          schola_tier[school.id] = school.plan_id
        })
        // deleting the school id of the lead that we have in DB
        const index_school_id = campaign_cases.indexOf(lead.school_id)
        if (index_school_id >= 0) {
          delete schola_tier[campaign_cases[index_school_id]]
          campaign_cases.splice(index_school_id, 1)
        }
      }

      const db_response = await postgres.query(queries.getSchoolsNonASMBlocked(), [campaign_cases, lead_info.grade])
      const s_ngb = db_response.rows[0].school_ids || [] //schools_non_grade_block

      // filtering the schools to get the matches we need
      schools = schools.filter((school: School) => (((school_id == null || school_id >= 0) ? (campaign_cases.indexOf(school.id) < 0) : (school.has_premium_access && SCHOLA_PLANS.includes(school.plan_id))) || school.name.toUpperCase().includes('ANIMO') || SCHOOLS_TO_PREVENT.includes(school.id) || (school.type == 'virtual/online' && school.state != direction.state_short)) ? false : (s_ngb.includes(school.id) ? true : false))

      // if scholamatch dont find a school to match
      if (schools.slice(0, SCHOOLS_TO_AUTOMATCH_2).length == 0) {
        schools = [{ id: DEFAULT_SCHOOL_CS, reason: `Schools Found Not Accepting This Grade [${campaign_cases}] some of them were descarted by the filter` }]
      }
    }

    // if scholamatch dont find a school to match
    if (schools.slice(0, SCHOOLS_TO_AUTOMATCH_2).length == 0) {
      schools = [{ id: DEFAULT_SCHOOL_CS, reason: 'Matching School Not Found' }]
    }

    const note = lead_info.note
    // loop to create leads with the matched schools
    for (const SCHOOL_TO_MATCH of schools.slice(0, SCHOOLS_TO_AUTOMATCH_2)) {
    // add the school id from the school to make match
      lead_info.school_id = SCHOOL_TO_MATCH.id

      if (lead_info.school_id != DEFAULT_SCHOOL_CS){
        lead_info.note = `${(note) ? `${note}, ` : ''}lead from AutoScholaMatch-${(school_id == null || school_id >= 0) ? (schola_tier[SCHOOL_TO_MATCH.id] || 'Legacy') : 'GeneralCampaign'},`
      } else {
        lead_info.note = `${(note) ? `${note}, ` : ''}| last source id: ${lead_info.lead_source_id} | reason: ${SCHOOL_TO_MATCH.reason}`
        if(fromCatchBucketSchool){
          await this.editLeadsForSchool({
            school_id: school_id,
            leads_ids: [lead.id],
            values: { status: 'new' },
            user_id: 'system-auto-schola-match'
          })
          await notesController.createNote({
            object_type: 'lead',
            object_id: lead.id,
            user_id: '',
            note: lead_info.note,
          })
          const history_params = {
            lead_id: lead.id,
            field: 'status',
            old_value: lead?.status || '',
            new_value: 'new',
          }
          await this.addHistory(history_params)
          leads_added.push(lead_info)
          break
        }
        lead_info.lead_source_id = -7
        lead_info.status = 'new'
      }

      // create the lead
      const student_lead = await this.createLead(lead_info)

      if (student_lead){
        leads_added.push(lead_info)

        // add the status of the lead as a history
        const history_params = {
          lead_id: student_lead.id,
          field: 'status',
          old_value: '',
          new_value: 'new',
        }
        this.addHistory(history_params)

        if (lead_info.school_id != DEFAULT_SCHOOL_CS) {
          // building email to send to the parent
          const email_params = {
            school: SCHOOL_TO_MATCH,
            lead: { ...lead, students: [lead_info] },
            admin_host: FRONTEND_HOST_ADMIN,
            search_host: FRONTEND_HOST_SEARCH
          }
          const email_context = await applicationsController.buildEmailContext(email_params as any)
          email_context && this.sendEnrollmentNotification(email_context)
        }

        if (lead_info.note && lead_info.note !== '') {
          notesController.createNote({
            object_type: 'lead',
            object_id: student_lead.id,
            user_id: '',
            note: lead_info.note,
          })
        }
      }
    }

  } catch (error) {
    logger.error('addLeadWithAutoscholaMatchError:', error)
  }
  return leads_added
}

/**
*
* @param  { Lead } lead
* @param  { number } school_id
* @param  { number } lead_source_id
* @returns {Promise} response object with the response from the database
*/
export async function addLeadToScholaSchool(lead: Lead, school_id: number = null, lead_source_id = -7): Promise<unknown> {
  if (school_id == DEFAULT_SCHOOL_CS) return []
  const leads_added = []
  try {
    const zipcodeN = Number(lead.zipcode)
    const lead_info = {
      grade: lead.grade,
      child_first_name: lead.child_first_name,
      child_last_name: lead.child_last_name,
      child_birthdate: lead.child_birthdate,
      custom_field_1: lead.custom_field_1 || null,
      custom_field_2: lead.custom_field_2 || null,
      custom_field_3: lead.custom_field_3 || null,
      custom_field_4: lead.custom_field_4 || null,
      custom_field_5: lead.custom_field_5 || null,
      address: lead.address || null,
      address_description: lead.address || null,
      parent_first_name: lead.parent_first_name,
      parent_last_name: lead.parent_last_name,
      email: lead.email,
      state: findStateByZipcode.findStateByZipcode(zipcodeN),
      zipcode: lead.zipcode,
      parent_relationship: lead.parent_relationship,
      phone: lead.phone,
      preferred_contact: lead.preferred_contact,
      tour_requested: lead.tour_requested,
      note: lead.note,
      language: lead.language,
      lead_source_id: lead_source_id
    } as any

    const { direction, zipcode, address, city, state_short } = await googleSearches.searchByZipState(lead_info.zipcode, lead_info?.address)
    if (direction?.lat && direction?.lon) {
      lead_info['address'] = address
      lead_info['address_description'] = address
      lead_info['zipcode'] = zipcode
      lead_info['latitude'] = direction?.lat
      lead_info['longitude'] = direction?.lon
      lead_info['city'] = city
      lead_info['state'] = state_short
    }

    lead_info.school_id = DEFAULT_SCHOOL_CS

    const note = lead_info.note
    lead_info.note = `${(note) ? `${note}, ` : ''}| last source id: ${lead_info.lead_source_id}`

    lead_info.lead_source_id = -7
    lead_info.status = 'new'

    // create the lead
    const student_lead = await this.createLead(lead_info)

    if (student_lead){
      leads_added.push(lead_info)

      // add the status of the lead as a history
      const history_params = {
        lead_id: student_lead.id,
        field: 'status',
        old_value: '',
        new_value: 'new',
      }
      this.addHistory(history_params)

      if (lead_info.note && lead_info.note !== '') {
        notesController.createNote({
          object_type: 'lead',
          object_id: student_lead.id,
          user_id: '',
          note: lead_info.note,
        })
      }
    }

  } catch (error) {
    logger.error('addLeadToScholaSchoolError:', error)
  }
  return leads_added
}

/**
*
* @param  { LeadsFromFB } params
* @returns {Promise} response object with the response from the database
* new function to create leads from facebook
*/
export async function addLeadFromFB(params: LeadsFromFB): Promise<AutoScholaMatchRes> {
  await validate(params, schemas.add_lead_from_fb)
  // checking if preferred contact is not in the list we set phone as preferred
  if (!VALID_CONTACT.includes(params.preferred_contact)) {
    params.preferred_contact = 'phone'
  }

  // split the parent name into first name and last name
  const names = params.parent_name.split(' ')
  params.parent_first_name = names[0].toLowerCase()
  params.parent_last_name = (names.length > 1 ? names[1].toLowerCase() : '')
  params.email = params.email.toLowerCase()
  const tmp_phone = params.phone.replace(/[^0-9+]/g, '')
  // cleaning phone number and letting only 10 digits commented because Shawn wanted the contry code
  //params.phone = tmp_phone.substring(tmp_phone.length-10, tmp_phone.length) || 'nophonenumber'
  params.phone = tmp_phone || 'nophonenumber'

  // verify if last name is an invalid name
  if (params.parent_last_name && INVALID_NAMES.includes(params.parent_last_name) || params.parent_last_name == '') {
    params.parent_last_name = 'nolastname'
  }

  // verify if first name is an invalid name
  if (params.parent_first_name && INVALID_NAMES.includes(params.parent_first_name) || params.parent_first_name == '') {
    params.parent_first_name = 'nofirstname'
  }

  // verify if the year is empty
  if (params.year && params.year.trim() === '') {
    params.year = null
  }

  let school = {}
  if (params.school_id > 0) {
    // get school information to send the email
    school = await schoolController.getSchoolById(params.school_id)
  }

  // array to return the leads we created
  const results = [] as Array<AutoScholaMatchLead>
  for (const student of params.students) {
    const student_name = student.child_name.split(' ')
    const student_grade = (GRADE_NAMES.includes(student.grade.toLowerCase())) ? student.grade : student.grade.replace(/\D+/g, '')
    const student_obj = {
      grade: student_grade,
      child_first_name: student_name[0].toLowerCase(),
      child_last_name: (student_name.length > 1 ? student_name[1].toLowerCase() : ''),
      child_birthdate: student.child_birthdate ? student.child_birthdate : null,
      grades: [ student_grade ],
      user_id: ''
    } as AutoScholaMatchChild

    // verify if last name is an invalid name
    if (!student_obj.child_last_name || INVALID_NAMES.includes(student_obj.child_last_name) || params.child_last_name == '') {
      student_obj['child_last_name'] = params.parent_last_name
    }

    // verify if first name is an invalid name
    if (!student_obj.child_first_name || INVALID_NAMES.includes(student_obj.child_first_name) || params.child_first_name == '') {
      student_obj['child_first_name'] = 'nofirstname'
    }

    // verify if lead exists in DB
    if (await this.verifyLeadDuplicate(student_obj, params)) {
      continue
    }

    // delete all the info we dont need for fourther requests
    delete student_obj.grades
    delete params.students
    delete params.parent_name


    // we create the lead json structure
    const lead_info:any = Object.assign({}, params, student_obj)

    const source = params.source || 'Schola School Campaign'

    let sourceRes = null
    // check if the source exists
    if (source) {
      sourceRes = await this.getOrCreateLeadSourceByName(params.school_id, source.toLowerCase())
    }

    // add the school id from the school to make match
    lead_info.school_id = params.school_id
    lead_info.lead_source_id = sourceRes.id
    lead_info.address_description = params?.address || null
    delete lead_info.source

    results.push({ ...lead_info } as AutoScholaMatchLead)

    if (params.school_id <= 0) {
      // call autoschola match
      await this.addLeadToScholaSchool(lead_info, params.school_id, ((params.school_id == 0) ? -11 : -7))
      continue
    }
    // create the lead
    const student_lead = await this.createLead(lead_info)

    // add the status of the lead as a history
    const history_params = {
      lead_id: student_lead.id,
      field: 'status',
      old_value: '',
      new_value: 'new',
    }
    await this.addHistory(history_params)

    // building email to send to the parent
    const email_params = {
      school,
      lead: { ...params, students: [student] },
      admin_host: FRONTEND_HOST_ADMIN,
      search_host: FRONTEND_HOST_SEARCH
    }

    if (params.school_id != DEFAULT_SCHOOL_CS) {
      const email_context = await applicationsController.buildEmailContext(email_params as any)
      email_context && this.sendEnrollmentNotification(email_context)
    }

    if (params.note && params.note !== '') {
      notesController.createNote({
        object_type: 'lead',
        object_id: student_lead.id,
        user_id: '',
        note: params.note,
      })
    }
  }

  return { leads: results }
}

/**
*
* @param  {number} school_id
* @param  {number} lead_id
* @param  {string} note
* @returns {Promise} updated lead object from the database
*/
export async function addLeadNote(school_id: number, lead_id: number, note: string): Promise<unknown> {
  const lead = await getLead(lead_id, school_id)
  if(!lead) throw notFoundError
  try {
    const db_response = await postgres.query(queries.updateLeadNote(), [note, lead.id])
    return db_response.rows[0]
  } catch (error) {
    logger.error('addLeadNoteError:', error)
    throw serverError
  }
}

/**
*
* @param  {number} school_id
* @returns {Promise} formatted response array with the total count for every status
*/
export async function getLeadsForSchoolCounts(school_id: number): Promise<Array<LeadCountByStatus>> {
  try {
    const db_response = await postgres.query(queries.getLeadsForSchoolCountByStatus(), [school_id])
    const result:Array<LeadCountByStatus>  = []
    Object.values(STATUSES).forEach((status) => {
      const found_status = db_response.rows.find((row) => row.status === status)
      if(status !== 'declined') {
        result.push({
          status,
          total: found_status ? found_status.count : 0
        })
      }
    })
    return result
  } catch (error) {
    logger.error('getLeadsForSchoolCountsError:', error)
    throw serverError
  }
}

/**
*
* @param  {number} school_id
* @returns {Promise} array of objects with the response from the database
*/
export async function getLeadsHistory(school_id: number): Promise<unknown> {
  const school = await schoolController.getSchoolById(school_id)
  if(!school) throw notFoundError
  const leads_ids = await getLeadsIdsBySchool(school.id)
  try {
    const db_response = await postgres.query(queries.getLeadsHistory(), [school.id, leads_ids])
    return db_response.rows
  } catch (error) {
    logger.error('getLeadsHistoryError:', error)
    throw serverError
  }
}

/**
*
* @param  {number} school_id
* @param  {LeadCustomImport} params
* @returns {Promise} response object with the response from the database
*/
export async function handleLeadCustomImport(school_id: number, params: LeadCustomImport, user_id: string): Promise<unknown> {
  await validate(params, schemas.custom_import)
  const school = await schoolController.getSchoolById(school_id)
  if(!school) throw notFoundError
  try {
    const response = []
    const batch_id = uuidv4()

    for(const row of params.data) {
      try {
        await processLead(school_id, row, params.mapping, user_id, batch_id)
        response.push(true)
      } catch (error) {
        logger.error(`Error procesando lead: ${error}`)
        response.push(false)
      }
    }

    return response
  } catch (error) {
    logger.error('handleLeadCustomImportError:', error)
    throw serverError
  }
}

function splitFullName(full_name: string) {
  const result = {first_name: '', last_name: ''}
  if(full_name){
    const [firstName, ...lastName] = full_name.split(' ').filter(Boolean)
    result.first_name = firstName
    result.last_name = lastName.join(' ')
  }
  return result
}

async function getLeadsIdsBySchool(school_id: number) {
  try {
    const db_response = await postgres.query(queries.getLeadsIdsBySchool(), [school_id])
    return db_response.rows.map(row => row.id)
  } catch (error) {
    logger.error('getLeadsIdsBySchoolError:', error )
    throw serverError
  }
}

async function getColumnsForCSV(school_id: number) {
  const custom_lead_fields = await getLeadFields(school_id)
  const custom_name:Array<LeadColumnObject> = []
  custom_lead_fields.forEach(field => {
    if(field.display_name && field.display_name !== '') {
      custom_name.push({
        name: field.display_name,
        keys: field.field_indentifier
      })
    }
  })
  const csv_column_objects = [...LEAD_CSV_EXPORT_COLUMNS, ...custom_name]
  const columns:Array<string> = csv_column_objects.map(row => row.name)
  return {csv_column_objects, columns}
}

async function getApplicationFields(): Promise<Array<ApplicationFields>>{
  try {
    const db_response = await postgres.query(queries.getApplicationFields())
    return db_response.rows
  } catch (error) {
    logger.error('getApplicationFieldsError:', error)
    throw serverError
  }
}

async function getApplicationCSV(rows: Array<any>, external: boolean) {

  const data: Input = []
  let columns:any = {}
  columns = Object.assign({}, APPLICATION_EXPORT_COLUMNS)

  rows.forEach(element => {
    const application = element.data
    if(application) {
      if(external && Array.isArray(application.extra)) application.extra = application.extra[0]
      // only for the universal app should verify the extra fields
      if(!external && application.extra && application.extra.length > 0) {
        application.extra.forEach((extra: any) => {
          extra.fields.forEach((field: any) => {
            const key = snakeCase(field.name)
            if(!columns[key]) {
              columns[key] = field.name
            }
          })
        })
      }
    }
    const properties = Object.assign({}, element)
    delete properties.data
    if(external) {
      const raw_request = JSON.parse(application.extra.rawRequest)
      // const pretty = getPrettyObjectJotForm(application.extra.pretty)
      for(const key of Object.keys(raw_request)) {
        let column_name = getPrettyColumnNameByIndexJotForm(key)
        if(column_name){
          column_name =  'Form>>' + column_name
          const value = raw_request[key]
          const type = getTypeOfJsonValue(value)
          if(type === 'string') processValueTypeStringJotForm(value, column_name, columns, properties)
          else if(type === 'object') processValueTypeObjectJotForm(value, column_name, columns, properties)
          else if(type === 'array') processValueTypeArrayJotForm(value, column_name, columns, properties)
        }
      }
      data.push(properties)
    } else {
      if(application && application.extra && application.extra.length > 0) {
        application.extra.forEach((extra: ApplicationExtra) => {
          extra.fields.forEach((field: ApplicationExtraField) => {
            const key = snakeCase(field.name)
            properties[key] = (typeof field.value === 'object') ? formatAsPrettyObject(key, field.value) : field.value
          })
        })
      }
      data.push(properties)
    }
  })
  return {data, columns}
}

async function getCountBySchool() {
  try {
    const result = []
    const selects = []
    for (let i = 0; i < REMINDER_PERIODS.length; i++) {
      const period = REMINDER_PERIODS[i]
      const values:unknown[] = [
        period.init,
        period.end,
      ]
      if(period.lastNotify) values.push(period.lastNotify)
      selects.push(postgres.query(queries.getCountBySchool(period.lastNotify), values))
    }
    for (let index = 0; index < selects.length; index++) {
      const select = selects[index]
      const period = REMINDER_PERIODS[index]
      const db_response = await select
      result.push({
        data: db_response.rows,
        period,
      })
    }
    return result
  } catch (error) {
    logger.error('getCountBySchoolError:', error)
    throw serverError
  }
}

async function getSchoolUserEmails(school_id: number) {
  const school = await schoolController.getSchoolById(school_id)
  if(!school) throw notFoundError
  const legacy_plans = SCHOLA_CONSTANTS.LEGACY_PLANS
  // Check if school has premium but doesn't have a pro plan
  const pro_plans = [legacy_plans.YEARLY_PRO, legacy_plans.MONTHLY_PRO]
  if((school.plan_id && new Date(school.subscription_end) >= new Date())
    && !pro_plans.includes(school.plan_id)) {
    try {
      const school_users = await schoolController.getSchoolUsers(school_id)
      return Promise.all(school_users.users.map(user => usersController.getUserDetails(user.user_id)))
    } catch (error) {
      logger.error('getSchoolUserEmailsError:', error)
      throw serverError
    }
  } else return []
}

// This function has a lot of improvements to be made
function filterOut(arr: Array<any>) {
  arr.sort((a, b) => a.period.init > b.period.init ? -1 : a.period.init < b.period.init ? 1 : 0)
  for (const i in arr) {
    arr[i].data.forEach((d1: any) => {
      for (let j = parseInt(i) + 1; j < arr.length; j++) {
        const idx = arr[j].data.findIndex((e: any) => e.school_id == d1.school_id)
        if (idx > -1)
          arr[j].data.splice(idx, 1)
      }
    })
  }
  return arr
}

function getPeriodEmailInfo(init: number) {
  let template
  let subject
  let period_tag
  switch(init) {
  case 24:
    template = lead_reminder_1_day
    subject = '[1-Day Notice] Your new student lead is waiting to be viewed - act now!'
    period_tag = '1-Day'
    break
  case 72:
    template = lead_reminder_3_days
    subject = `[3-Day Notice] Your new student lead hasn't been viewed yet - act now!`
    period_tag = '3-Day'
    break
  case 168:
    template = lead_reminder_1_week
    subject = '[1-Week Notice] Your student lead is splipping away - ACTION REQUIRED'
    period_tag = '1-Week'
    break
  case 240:
    template = lead_reminder_10_days
    subject = '[10-Day Notice] Your student lead will be redirected soon - LAST CHANCE!'
    period_tag = '10-Day'
    break
  case 336:
    template = lead_reminder_2_weeks
    subject = '[2-Week Notice] Your student lead is being redirected to other schools - ACT NOW!'
    period_tag = '2-Week'
    break
  default:
    break
  }
  return {template, subject, period_tag}
}

async function processLead(school_id: number, row: any, mapping: Array<any>, user_id: string, batch_id:string) {
  const lead_data: any = {
    imported: true,
    import_batch_id: batch_id,
    created_on: 'admin',
    '"importedByUserId"': user_id || '',
    school_id,
    parent_relationship: 'guardian',
    tour_requested: false,
    preferred_contact: 'email',
    viewed_at: new Date(),
  }
  const lead_statuses = await getLeadStatuses(school_id)
  let note = ''
  for (let index = 0; index < mapping.length; index++) {
    const entry = mapping[index]
    let value = row[entry.fileField]
    if(value) value.trim()
    let status = 'new'
    switch(entry.leadField.toLowerCase()) {
    case 'language':
      if (value === null || value === undefined) {
        value = 'english'
      } else {
        if(value.toLowerCase().indexOf('english') !== -1) value = 'english'
        else if(value.toLowerCase().indexOf('spanish') !== -1) value = 'spanish'
        else if(value.toLowerCase().indexOf('español') !== -1) value = 'spanish'
        else value = 'english'
      }
      lead_data[entry.leadField] = value
      break
    case 'lead_source':
      if (value !== null || value === undefined) {
        const source = await getOrCreateLeadSourceByName(school_id, value)
        if(source) lead_data.lead_source_id = source.id
        else logger.error('Error creating lead source:', value)
      }
      break
    case 'note':
      note = value
      break
    case 'status':
      if (value !== '') {
        switch(value.toLowerCase()){
        case 'application sent':
          status = 'application-sent'
          break
        case 'application received':
          status = 'application-received'
          break
        case 'waitlisted':
        case 'accepted':
        case 'archived':
          status = value.trim().toLowerCase()
          break
        }
      }
      lead_data.status = status
      break
    case 'lead_status':
      if(value !== null || value === undefined) {
        let lead_status_id = -1
        lead_statuses.forEach(lead_status => {
          if(lead_status.name.toLowerCase() === value.toLowerCase()) {
            lead_status_id = lead_status.id
          }
        })
        if(lead_status_id === -1) {
          const lead_status = await addLeadStatus(school_id, value)
          if(lead_status) lead_status_id = lead_status.id
          else logger.error('Error creating lead status:', value)
        } else lead_data.lead_status_id = lead_status_id
      }
      break
    default:
      lead_data[entry.leadField] = value
      break
    }
  } // iterate over the mapping array

  const lead = await createLead({...lead_data, logged_id: user_id})
  await notesController.createNote({
    object_type: 'lead',
    object_id: lead.id || 1,
    user_id,
    note,
  })
  return lead_data
}

/**
*
* @param  { FeedbackSurvey } params
* @param  { string } params.name
* @param  { string } params.org
* @param  { string } params.phone
* @returns {Promise} response object with the response from the database
*/
export async function feedbackSurvey(params: FeedbackSurvey): Promise<object> {
  await validate(params, schemas.feedback_survey)
  try {
    // letting only 10 digits to run the query and compare 10 digits with 10 digits
    let tmp_phone = params.phone.replace(/[^0-9+]/g, '')
    tmp_phone = tmp_phone.substring(tmp_phone.length - 10, tmp_phone.length)
    const db_response = await postgres.query(queries.getLeadEmailFromPhone(), [tmp_phone])
    const lead = db_response?.rows[0]
    // sending sms to parent
    const sms_en = `Thanks for choosing Schola! Please take our survey on your recent Parent Outreach Team interaction:\n${FEEDBACK_SURVEY}?language=en&f=1&p=${tmp_phone}\nYour feedback matters!`
    const sms_es = `Gracias por elegir Schola. Por favor, realiza nuestra encuesta sobre tu reciente interacción con nuestro Equipo de Atención a Padres:\n${FEEDBACK_SURVEY}?language=es-419&f=1&p=${tmp_phone}\n¡Tu opinión es importante para nosotros!`
    const sms_to_send = (lead?.language == 'spanish') ? sms_es : sms_en
    // review if the phone number has zone digits
    if (params.phone.startsWith('+')){
      await sendSms({
        from: TWILIO_FROM,
        body: sms_to_send,
        to: params.phone,
        unsubscribeMsg: false
      })
    }
    if (lead) {
      // sending email to parent
      const template_params = {
        parent_name: (lead.last_name != '' && lead.last_name != undefined) ? `${lead.parent_first_name} ${lead.last_name}` : lead.parent_first_name,
        url_survey: `${FEEDBACK_SURVEY}?language=${(lead.language == 'spanish') ? 'es-419' : 'en'}&f=2&p=${tmp_phone}`
      }
      const html = (lead.language == 'spanish') ? feedbackSurveyEmailES(template_params) : feedbackSurveyEmailEN(template_params)
      const subject = (lead.language == 'spanish') ? '¡Tu opinión es valiosa para nosotros!' : 'Your Feedback is Valuable to Us!'
      const from = (lead.language == 'spanish') ? `Encuesta Schola <${BEN_CEO_CONTACT}>` : `Schola Survey <${BEN_CEO_CONTACT}>`
      const email_params = {
        to: lead.email,
        from,
        subject,
        html,
        tags: ['notification', 'feedback', 'survey']
      }
      await sendEmail(email_params)
    }
    return {matched_leads: db_response.rowCount}
  } catch (error) {
    logger.error('feedbackSurvey:', error)
    throw serverError
  }
}

/**
*
* @param  { JotformLeadSubmitted } params
* @param  { string } params.phone
* @param  { string } params.parent_name
* @param  { string } params.available_time
* @param  { string } params.language
* @returns {Promise} response object with the response
*/
export async function smsLeadJotformSubmitted(params: JotformLeadSubmitted): Promise<object> {
  await validate(params, schemas.sms_lead_jotform_submitted)
  try {
    // letting only 10 digits to add then +1 (this campaign is working only with USA phone numbers ask Alejandra SPOT for more info)
    let phone = params.phone.replace(/[^0-9+]/g, '')
    phone = `+1${phone.substring(phone.length - 10, phone.length)}`
    const parent_name = toTitleCase(params.parent_name)
    const time_to_call = /\((.*?)\)/.exec(params.available_time)
    let sms = ''
    if(time_to_call?.length == 2){
      // sending sms to parent
      sms = `Hi ${parent_name}, this is Alejandra from Schola. We'll be calling you between ${time_to_call[1]} to discuss school options for your child. Talk to you then!`
      if (params.language?.toLowerCase() == 'spanish') {
        sms = `Hola ${parent_name}, soy Alejandra de Schola. Estaremos llamándote entre ${time_to_call[1]} para discutir opciones escolares para tu hijo/a. ¡Hablamos después!`
      }
    }else{
      sms = `Hi ${parent_name}, this is Alejandra from Schola. We'll be calling you during work hours to discuss school options for your child. Talk to you then!`
      if (params.language?.toLowerCase() == 'spanish'){
        sms = `Hola ${parent_name}, soy Alejandra de Schola. Estaremos llamándote durante las horas de trabajo para discutir opciones escolares para tu hijo/a. ¡Hablamos después!`
      }
    }
    // review if the phone number has zone digits
    const result = await sendSms({
      from: TWILIO_FROM,
      body: sms,
      to: phone,
      unsubscribeMsg: false
    })
    return { sms_sent: result.success }
  } catch (error) {
    logger.error('smsLeadJotformSubmitted:', error)
    throw serverError
  }
}

/**
*
* @param  { SendScholaMatchJotform } params
* @param  { string } params.parent_name
* @param  { string } params.student_name
* @param  { string } params.grade
* @param  { string } params.phone
* @param  { string } params.email
* @param  { string } params.zipcode
* @param  { string } params.language
* @returns {Promise} response object with the response from the database
*/
export async function sendScholaMatchJotform(params: SendScholaMatchJotform): Promise<object> {
  await validate(params, schemas.send_scholamatch_jotform)
  try {
    const scholamatch_url = params.scholamatch_url || 'https://form.jotform.com/scholaschools/SML'
    // formatting all strings to create the jotform url
    const parent_name = toTitleCase(params.parent_name)
    const student_name = toTitleCase(params.student_name)
    const grade = (params.grade.toLowerCase() == 'kindergarten') ? 'K' : (params.grade.toLowerCase() == 'pre-k') ? 'Pre-K' : undefined
    const email = params.email.toLowerCase()
    const zipCode = (params.zipcode.length > 5) ? undefined : params.zipcode
    // letting only 10 digits to add then +1 (this campaign is working only with USA phone numbers ask Alejandra SPOT for more info)
    let phone = params.phone.replace(/[^0-9+]/g, '')
    phone = phone.substring(phone.length - 10, phone.length)

    // building jotform url
    let jotform_url = `${scholamatch_url}?pN=${parent_name}&sN=${student_name}&e=${email}&p=${phone}&`
    jotform_url += (grade) ? `g=${grade}&` : ''
    jotform_url += (zipCode) ? `z=${zipCode}&` : ''

    // let add more custom query params to the url
    if (Object.prototype.hasOwnProperty.call(params, 'other_query_params')){
      for (const query in params.other_query_params){
        jotform_url += `${query}=${params.other_query_params[query]}&`
      }
    }

    // we are only sending this sms to parents in USA (ask SPOT why)
    if(phone.length == 10){
      // creating new shorurl for the sms case
      const { short_url } = await urlController.createShortUrlByLongUrl(encodeURI(`${jotform_url}v=s`))
      let sms = `Hello from Schola! Ready to find the ideal school for your child? Complete our easy quiz now and discover the best school choices in your area: ${short_url}. Your child’s future awaits!`
      if (params.language?.toLowerCase() == 'spanish'){
        sms = `¡Hola de parte de Schola! ¿Listo para encontrar la escuela ideal para tu hijo/a? Completa nuestro sencillo cuestionario ahora y descubre las mejores opciones escolares en tu área: ${short_url}. ¡El futuro de tu hijo/a te espera!`
      }


      // sending sms to parent
      await sendSms({
        from: TWILIO_FROM,
        body: sms,
        to: `+1${phone}`,
        unsubscribeMsg: false
      })
    }

    // searching if the email contains @ to send the email
    if (email.indexOf('@')) {
      // we don't need short URL here because is in the email
      let html = smEmailUnresponsiveLeadsEn(encodeURI(`${jotform_url}v=e`))
      let subject = 'You’re 5 Minutes Away From Finding the Perfect School!'

      if (params.language?.toLowerCase() == 'spanish') {
        html = smEmailUnresponsiveLeadsEs(encodeURI(`${jotform_url}v=e`))
        subject = '¡Estás a 5 minutos de encontrar la escuela perfecta!'
      }

      const from = `ScholaMatch <${FROM_EMAIL}>`
      const email_params = {
        to: email,
        from,
        subject,
        html,
        tags: ['notification', 'ScholaMatch', 'school', 'Find perfect school', 'Schola']
      }
      await sendEmail(email_params)
    }
    return { success: true }
  } catch (error) {
    logger.error('sendScholaMatchJotform:', error)
    throw serverError
  }
}


/**
*
* @param  { SchoolMintProcess } params
* @returns {Promise} response object with the response
*/
export async function schoolmintProcess(params: SchoolMintProcess, school_id: number): Promise<object> {
  await validate(params, schemas.schoolmint_process)

  try {

    const edit_lead_error = [] as Array<string>
    for (const lead of params.schola_leads) {
      const sm_lead_data = [lead.app_id, lead.submission_date]
      lead.school_id = school_id
      lead.user_id = 'system-schoolmint'
      if (lead.values.enrollment_confirmed_at == ''){
        delete lead.values.enrollment_confirmed_at
      }else{
        lead.values.enrollment_confirmed = true
      }
      lead.values.application_received_at = lead.submission_date
      lead.values.application_received = true
      lead.values.application_valid = true
      lead.values.intg_app_id = lead.app_id
      const all_info = lead.values.all_info
      delete lead.values.all_info
      delete lead.app_id
      delete lead.submission_date
      try {
        await this.editLeadsForSchool(lead)
      } catch (error) {
        try{
          console.error('editLeadsForSchoolDup 2nd Run')
          lead.values = { ...lead.values, key_duplicated: `sys-sm-${lead.leads_ids[0]}` }
          await this.editLeadsForSchool(lead)
        } catch (error){
          console.error(`editLeadsForSchool: ${JSON.stringify(lead)} - ${error}`)
          edit_lead_error.push(lead.leads_ids[0])
        }
      }
      await this.addIntegrationExtraData(lead.leads_ids[0], all_info, lead.values.sync_intg_with)
      lead.app_id = sm_lead_data[0]
      lead.submission_date = sm_lead_data[1]
    }

    params.schola_leads = params.schola_leads.filter((lead) => !edit_lead_error.includes(lead.leads_ids[0]))

    const create_lead_error = [] as Array<string>
    for (const lead of params.leads_to_create) {
      const sm_lead_data = [lead.app_id, lead.submission_date]
      lead.school_id = school_id
      lead.user_id = 'system-schoolmint'
      lead.source = lead?.source || 'SchoolMint'
      if (lead.enrollment_confirmed_at == '') {
        delete lead.enrollment_confirmed_at
      } else {
        lead.enrollment_confirmed = true
      }
      lead.application_received_at = lead.submission_date
      lead.created_at = lead.submission_date
      lead.application_received = true
      lead.application_valid = true
      lead.intg_app_id = lead.app_id
      const all_info = lead.all_info
      delete lead.all_info
      delete lead.submission_date
      delete lead.app_id
      try{
        const new_lead = await this.createLead(lead)
        await this.addIntegrationExtraData(new_lead.id, all_info, lead.sync_intg_with)
        lead.lead_id = new_lead.id
      } catch (error){
        lead.lead_id = 'N/A'
        console.error('schoolmintProcess:', error)
        create_lead_error.push(lead.email)
      }
      lead.app_id = sm_lead_data[0]
      lead.submission_date = sm_lead_data[1]
    }

    params.leads_to_create = params.leads_to_create.filter((lead) => !create_lead_error.includes(lead.email))

    if (!params.non_schola_leads.length && !params.schola_leads.length && !params.leads_to_create.length)
      return

    if (params.send_email){
      const users_res = await postgres.query(user_schools_queries.getSchoolUsersEmails(), [school_id])
      users_res.rows.push({ email: '<EMAIL>' })
      const html = schoolmintAppNotification(params.schola_leads, params.non_schola_leads, params.leads_to_create, school_id)
      for (const user of users_res.rows){
        await sendEmail({
          to: user.email,
          from: `Schola Notification <${FROM_EMAIL}>`,
          subject: 'New Application Sync Update from Schola',
          html,
          tags: ['notification', 'Schola', 'New Application', 'Find perfect school']
        })
      }
    }
  } catch (error) {
    console.error('schoolmintProcess:', error)
    throw serverError
  }
  return { success: true }
}


/**
*
* @param  { number } lead_id
* @param  { any } all_info
* @param  { string } sync_intg_with
* @returns {Promise} response object with the response
*/
export async function addIntegrationExtraData(lead_id: number, all_info: any, sync_intg_with: string): Promise<object> {
  try{
    const db_response = await postgres.query(queries.createLeadIntegration(), [lead_id, sync_intg_with, all_info])
    return db_response.rows
  } catch (error) {
    logger.error('addIntegrationExtraData:', error)
    throw serverError
  }
}

/**
*
* @param  { number } lead_id
* @param  { number } school_id
* @returns {Promise} response object with the response
*/
export async function getIntegrationExtraData(lead_id: number, school_id: number): Promise<object> {
  try{
    const db_response = await postgres.query(leads_queries.selectLeadIntegrationExtraData(), [lead_id, school_id])
    return db_response.rows[0]
  } catch (error) {
    logger.error('getIntegrationExtraData:', error)
    throw serverError
  }
}

/**
*
* @param  { number } lead_id
* @returns {Promise} response object with the response
*/
export async function getAiResponses(lead_id: number): Promise<object> {
  try{
    const db_response = await postgres.query(queries.getAiResponses(), [lead_id])
    return db_response.rows
  } catch (error) {
    logger.error('getAiResponses:', error)
    throw serverError
  }
}

/**
*
* @param  { LeadAIResponses } params
* @param  { number } lead_id
* @returns {Promise} response object with the response
*/
export async function addAiResponse(params: LeadAIResponses, lead_id: number): Promise<object> {
  await validate(params, schemas.add_lead_ai_response)
  params.lead_id = lead_id
  params.created_at = moment.utc()
  const values = await getObjectValues(params)
  const new_ai_res = await postgres.query(insertQuery('leads_ai_responses', params), values )
  return new_ai_res.rows
}


/**
*
* @param  { CustomSurvey } params
* @returns {Promise} response object with the response from the database
*/
export async function customSurvey(params: CustomSurvey): Promise<object> {
  await validate(params, schemas.custom_survey)
  if (!('survey_type' in params) || params.survey_type.length == 0) {
    params.survey_type = ['sms', 'email']
  }
  try {
    for(const lead of params.leads){
      lead.language = lead.language.toLowerCase()
      const language = (lead.language && (lead.language.includes('es') || lead.language.includes('spa'))) ? 'spanish' : 'english'
      // letting only 10 digits to add then +1 (this campaign is working only with USA phone numbers ask Alejandra SPOT for more info)
      let phone = lead.phone?.replace(/[^0-9+]/g, '')
      phone = phone.substring(phone?.length - 10, phone.length)
      const parent_name = toTitleCase(lead?.parent_name || '')
      const parent_name_split = parent_name?.split(' ') || []
      const student_name = toTitleCase(lead?.student_name || '')
      const student_name_split = student_name?.split(' ') || []
      const email = lead.email?.toLowerCase() || ''
      const grade = lead.grade?.toLowerCase() || ''
      const parent_relationship = lead.parent_relationship?.toLocaleLowerCase() || ''
      const preferred_contact = lead.preferred_contact?.toLocaleLowerCase() || ''

      let url_sms = ''
      let url_email = ''

      if (Object.prototype.hasOwnProperty.call(params,'jotform_url')){
        // building jotform url
        let jotform_url = `${params.jotform_url}?`
        jotform_url += (params.school_id) ? `sid=${params.school_id}&` : ''
        jotform_url += (phone) ? `p=${phone}&` : ''
        jotform_url += (lead.zipcode && lead.zipcode.length > 5) ? `a[postal]=${lead.zipcode}&` : ''
        jotform_url += (language) ? `l=${language}&` : ''
        jotform_url += (parent_relationship) ? `pr=${parent_relationship}&` : ''
        jotform_url += (preferred_contact) ? `pc=${preferred_contact}&` : ''
        jotform_url += (parent_name_split.length >= 1) ? `pn[first]=${parent_name_split[0]}&` : ''
        jotform_url += (parent_name_split.length >= 2) ? `pn[last]=${parent_name_split.slice(1, parent_name_split.length).join(' ')}&` : ''
        jotform_url += (student_name_split.length >= 1) ? `sn[first]=${student_name_split[0]}&` : ''
        jotform_url += (student_name_split.length >= 2) ? `sn[last]=${student_name_split.slice(1, student_name_split.length).join(' ')}&` : ''
        jotform_url += (email) ? `e=${email}&` : ''
        jotform_url += (grade) ? `g=${grade}&` : ''
        jotform_url += `language=${ (language == 'spanish') ? 'es-419' : 'en' }`
        const short_url = await urlController.createShortUrlByLongUrl(encodeURI(`${jotform_url}&v=sms`))
        url_sms = short_url.short_url
        url_email = `${ jotform_url }&v=email`
      }
      else if (Object.prototype.hasOwnProperty.call(params, 'other_url')){
        url_sms = params.other_url
        url_email = params.other_url
      }

      // we are only sending this sms to parents in USA (ask SPOT why)
      if (phone.length == 10 && params.survey_type.includes('sms')) {
        // creating new shorurl for the sms case
        let sms = params.sms_template.msg_en.replace(/\$parent_name/g, parent_name)
        sms = sms.replace(/\$survey_link/g, url_sms)
        if (language == 'spanish') {
          sms = params.sms_template.msg_es.replace(/\$parent_name/g, parent_name)
          sms = sms.replace(/\$survey_link/g, url_sms)
        }

        // sending sms to parent
        await sendSms({
          from: TWILIO_FROM,
          body: sms,
          to: `+1${phone}`,
          unsubscribeMsg: false
        })
      }

      // searching if the email contains @ to send the email
      if (email.indexOf('@') != -1 && params.survey_type.includes('email')) {
        const email_opt = params.email_template.email_options

        email_opt.message_text = params.email_template.msg_en.replace(/\$parent_name/g, parent_name)
        let subject = params.email_template.subject_en

        if (language == 'spanish') {
          email_opt.message_text = params.email_template.msg_es.replace(/\$parent_name/g, parent_name)
          subject = params.email_template.subject_es
        }

        email_opt.special_call_to_action = encodeURI(url_email)
        const html = customSurveyEmail(email_opt)

        const from = `${params.school_name} Survey <${FROM_EMAIL}>`
        const email_params = {
          to: email,
          from,
          subject,
          html,
          tags: ['notification', 'survey', 'school', 'Find perfect school', 'Schola']
        }
        await sendEmail(email_params)
      }
    }
    return { success: true }
  } catch (error) {
    logger.error('customSurvey:', error)
    throw serverError
  }
}

/**
*
* @param  {number} school_id
* @returns {Promise} response array with the results from the database
*/
export async function getHearAboutUs(school_id: number): Promise<Array<unknown>> {
  try {
    const db_response = await postgres.query(queries.getHearAboutUs(), [school_id])
    const labels = new Set(['Google', 'Facebook', 'Arizona Federation For Children Powered By Schola®', 'Other'])
    for (const row of db_response.rows) {
      if (row.referred_by) {
        const title_case = toTitleCase(row.referred_by)
        labels.add(title_case)
      }
    }
    const res = [{ label: 'Select', value: '' }]
    for (const label of labels) {
      res.push({ label: label, value: label })
    }
    return res
  } catch (error) {
    logger.error('getHearAboutUs:', error)
    throw serverError
  }
}

/**
*
* @param  { SchoolMintWebScraper } params
* @returns { Promise<object> } response object with the response from google api
*/
export async function runSchoolMintScrapper(params: SchoolMintWebScraper): Promise<any> {
  const url = `https://sbbgmyilowcufpyn5owe2ofcn40hlitr.lambda-url.us-west-2.on.aws/`
  try {
    axios.post(url, {...params}).then((res) => {
      console.log(`runSchoolMintScrapper-request: ${res?.data}`)
    }).catch((error) => {
      console.error('runSchoolMintScrapper-request', error)
    })
    return { success: true }
  } catch (error) {
    console.error('runSchoolMintScrapper', error)
    throw serverError
  }
}

/**
*
* @param  { SchoolMintMatch } params
* @returns { Promise<object> } response object with the response from google api
*/
export async function runSchoolMintMatch(params: SchoolMintMatch): Promise<any> {
  const url = `https://5lg36ujmnrmwpskysaiibrhtm40bvpgd.lambda-url.us-west-2.on.aws/`
  try {
    axios.post(url, {...params}).then((res) => {
      console.log(`runSchoolMintMatch-request: ${res?.data}`)
    }).catch((error) => {
      console.error('runSchoolMintMatch-request', error)
    })
    return { success: true }
  } catch (error) {
    console.error('runSchoolMintMatch', error)
    throw serverError
  }
}

/**
* @param  { SubmitApplicationExternal } params
* @returns { Promise<object> } response object
*/
export async function freshpaintTrack(params: SubmitApplicationExternal): Promise<any> {
  try {
    const data = JSON.parse(params.rawRequest)
    const new_payload = {
      parent_name: '',
      student_name: '',
      email: '',
      phone :'',
      formTitle: params.formTitle,
      submissionId: params.submissionID,
      distinct_id: params.submissionID,
      fbclid: '',
      gclid: '',
    }
    for (const attribute_name in data) {
      if(attribute_name.startsWith('q')){
        const element = data[attribute_name]
        const params = [attribute_name.substring(attribute_name.indexOf('_') + 1).replace(/[0-9]/g, ''), element]
        switch (params[0].trim()) {
        case 'phoneNumber':
          if (typeof params[1].trim === 'function') {
            new_payload.phone = params[1].trim()
          } else {
            if(params[1].full !== undefined){
              new_payload.phone = params[1].full
            } else{
              new_payload.phone = params[1].area +params[1].phone
            }
          }
          break
        case 'parentName':
          new_payload.parent_name =  params[1].trim()
          break
        case 'studentName':
          new_payload.student_name =  params[1].trim()
          break
        case 'email':
          new_payload.email =  params[1].trim()
          break
        case 'fbclid':
          new_payload.fbclid =  params[1].trim()
          break
        case 'gclid':
          new_payload.gclid =  params[1].trim()
          break
        }
      }

    }
    if(new_payload.fbclid){
      await trackEventFP('ScholaLead-fbclid', params.submissionID, new_payload)
    } else {
      await trackEventFP('ScholaLead', params.submissionID, new_payload)
    }

    return { success: true }
  } catch (error) {
    console.error('trackFreshpaint', error)
    throw serverError
  }
}

/**
* @param  { SchoolMintWebScraper } params
* @returns { Promise<object> } response objects from db
*/
export async function getUnresponsiveLeads(days: number, page: number): Promise<any> {
  try {
    const date = moment.utc().subtract(days, 'days')
    const offset = page * 900
    const db_response = await postgres.query(queries.getUnresponsiveLeads(), [date, offset])
    return db_response.rows
  } catch (error) {
    console.error('getUnresponsiveLeads', error)
    throw serverError
  }
}

/**
* @param  { SchoolMintWebScraper } params
* @returns { Promise<object> } response objects from db
*/
export async function flagDuplicates(school_id:number, filters = ''): Promise<any> {

  const db_response_school = await postgres.query(queries.schoolExists(), [school_id])
  if (!db_response_school.rows[0].exists) throw notFoundError

  //validated in route
  try {
    const db_res_p = await postgres.query(queries.getDuplicatesToFlagByPhone(filters), [school_id])
    const db_res_e = await postgres.query(queries.getDuplicatesToFlagByEmail(filters), [school_id])
    const dups_ids: Array<string> = []
    const dups: any = {}
    const res_dups = new Set(db_res_p.rows.concat(db_res_e.rows))
    for (const dup of res_dups){
      const dup_tag = `${dup.id_dup1}:${dup.id_dup2}`
      if(!dups_ids.includes(dup_tag)){
        dups_ids.push(dup_tag)
        if (!Object.prototype.hasOwnProperty.call(dups, dup.id_dup1))
          dups[dup.id_dup1] = []
        if (!Object.prototype.hasOwnProperty.call(dups, dup.id_dup2))
          dups[dup.id_dup2] = []

        if (Object.prototype.hasOwnProperty.call(dup,'email_sim')){
          dup['phone_sim'] = 1
        }else{
          dup['email_sim'] = 1
        }
        dup['dup_avg'] = 0
        for (const tag of ['pf_sim', 'pl_sim', 'cf_sim', 'cl_sim', 'phone_sim', 'child_birthdate', 'email_sim']){
          if (dup[tag]){
            dup['dup_avg'] += dup[tag]
            dup['dup_avg'] = (dup['dup_avg'] / 2)
          }
        }
        const id_dup1 = dup.id_dup1
        const id_dup2 = dup.id_dup2
        delete dup.id_dup1
        delete dup.id_dup2
        dups[id_dup1].push({ dup_id: id_dup2, ...dup})
        dups[id_dup2].push({ dup_id: id_dup1, ...dup})
      }
    }
    return dups
  } catch (error) {
    console.error('flagDuplicates', error)
    throw serverError
  }
}

/**
* @param  { MergeDupLeads } params
* @param  { number } school_id
* @returns { Promise<object> } response objects from db
*/
export async function mergeDupLeads(school_id: number, params: MergeDupLeads): Promise<any> {
  await validate(params, schemas.merge_dup_leads)
  try {
    const db_res = await postgres.query(queries.getLeads(), [[params.id_to_keep,...params.ids_to_delete],school_id])
    if (db_res.rows.length < 2) throw conflictError
    let updated_lead = []
    const error = {
      message: ''
    }
    try {
      updated_lead = await this.editLeadsForSchool({
        school_id: school_id,
        leads_ids: [params.id_to_keep],
        values: { ...params.params },
        user_id: 'system-merge-leads'
      })
    } catch (err) { error.message = err.message }

    if (error.message.includes('duplicated') || updated_lead.length == 0){
      updated_lead = await this.editLeadsForSchool({
        school_id: school_id,
        leads_ids: [params.id_to_keep],
        values: { ...params.params, key_duplicated: `sys-ml-${params.ids_to_delete[0]}` },
        user_id: 'system-merge-leads'
      })
    } else if (error.message != ''){
      throw serverError
    }

    await this.editLeadsForSchool({
      school_id: school_id,
      leads_ids: [...params.ids_to_delete],
      values: { deleted: true, deleted_by: 'system-merge-leads', deleted_at: moment.utc(), key_duplicated_auto: `DEL--` },
      user_id: 'system-merge-leads'
    })
    await postgres.query(notes_queries.mergeLeadNotes(params.ids_to_delete), [...params.ids_to_delete, params.id_to_keep])
    return updated_lead
  } catch (error) {
    console.error('mergeDupLeads', error)
    throw serverError
  }
}


/**
* @param  { number } school_id
* @param  { number } main_id
* @param  { number } non_dup_id
* @returns { Promise<object> } response objects from db
*/
export async function markAsNonDup(school_id: number, main_id: number, non_dup_id: number): Promise<any> {
  try {
    const db_res = await postgres.query(queries.nonDupLead(), [main_id, non_dup_id, school_id])
    return { 'non-dups': db_res.rows }
  } catch (error) {
    console.error('markAsNonDup', error)
    throw serverError
  }
}

/**
 * Creates a lead from data received from another webpage.
 *
 * @param  {any} body
 * @returns {Promise<any>}
 */
export async function createLeadFromOtherWebpage(body: any): Promise<any> {
  // Validate required fields that must have a value
  await validate(body, schemas.add_lead_webpage)

  // Optional: If you need to verify the school actually exists:
  const school = await schoolController.getSchoolById(body.school_id)
  if (!school) throw notFoundError

  // Define defaults
  const currentYear = new Date().getFullYear().toString()
  const year = body.year ?? currentYear
  const parentRelationship = body.parent_relationship ?? 'guardian'
  const phone = body.phone ?? ''
  const preferredContact = body.preferred_contact ?? 'email'
  const childFirstName = body.child_first_name ?? 'nofirstname'
  const childLastName = body.child_last_name ?? 'nolastname'
  const source = body.source ?? 'webpage'

  const note = [
    `Submission ID: ${body['submission_id'] ?? ''}`,
    `Created At: ${body['created_at'] ?? ''}`,
    `Referrer: ${body['referrer'] ?? ''}`
  ].join(' - ')

  try {
    // Build the lead object
    const newLead = {
      school_id: body.school_id,
      parent_first_name: body.parent_first_name,
      parent_last_name: body.parent_last_name,
      email: body.email,
      parent_relationship: parentRelationship,
      phone: phone,
      preferred_contact: preferredContact,
      students: [
        {
          child_first_name: childFirstName,
          child_last_name: childLastName,
          grade: body.grade,
          parent_relationship: parentRelationship,
          year: year
        }
      ],
      zipcode: body.zipcode,
      source: source,
      note: note,
      year: year
    } as any

    const res = await this.createNewLead(newLead)
    return res[0]
  } catch (error) {
    logger.error('createLeadFromOtherWebpageError:', error)
    throw serverError
  }
}

/**
 * @param  {number} lead_id
 * @returns {Promise<object>} response objects from db
 */
export async function getSHLeadByLeadId(lead_id: number): Promise<any> {
  const db_response = await postgres.query(sh_queries.getSHLeadByLeadId(), [lead_id])
  return db_response.rows
}

/**
 * @param  {number} lead_id
 * @param  {number} school_id
 * @returns {Promise<object>} response objects from db
 */
export async function getSHLeadByLeadIdAndSchoolId(lead_id: number, school_id: number): Promise<any> {
  const db_response = await postgres.query(sh_queries.getSHLeadByLeadIdAndSchoolId(), [lead_id, school_id])
  return db_response.rows
}
