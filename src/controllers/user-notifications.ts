import moment from 'moment'
import { postgres } from '@connectors/postgres'
import { notFoundError, serverError } from '@errors'
import { UserNotification, UserNotificationUpdate } from '../interfaces/user-notifications'
import { validate, getObjectValues, insertLogChange, buildUpdate } from '@utils'
import { insertQuery } from '../queries/common'
import * as schemas from '../schemas'

/**
 * Get notifications for a user in a school
 */
export async function getSchoolUserNotifications(user_id: string, school_id?: string, unreadOnly?: boolean): Promise<UserNotification[]> {
  try {
    let params = [user_id]
    let query = 'SELECT * FROM user_notifications WHERE user_id = $1'
    if (school_id !== undefined) {
      query = 'SELECT * FROM user_notifications WHERE school_id = $1 AND user_id = $2'
      params = [school_id, user_id]
    }
    query += ' AND COALESCE(deleted_at, NULL) IS NULL'
    if (unreadOnly) {
      query += ' AND read_at IS NULL'
    }
    query += ' ORDER BY created_at DESC'
    const db_response = await postgres.query(query, params)
    if (db_response.rowCount === 0) return []
    return db_response.rows as UserNotification[]
  } catch (error) {
    console.error('getSchoolUserNotificationsError:', error)
    if (error.statusCode === notFoundError.statusCode) throw notFoundError
    throw serverError
  }
}

/**
 * Get a user notification by id
 */
export async function getUserNotificationById(id: number): Promise<UserNotification | null> {
  try {
    const db_response = await postgres.query('SELECT * FROM user_notifications WHERE id = $1', [id])
    if (db_response.rowCount === 0) return null
    return db_response.rows[0] as UserNotification
  } catch (error) {
    console.error('getUserNotificationByIdError:', error)
    throw serverError
  }
}

/**
 * Add a notification for a user in a school
 */
export async function addUserNotification(params: UserNotification): Promise<UserNotification> {
  // await validate(params, schemas.add_user_notification)
  const now = moment.utc().toISOString()
  const insertParams = {
    ...params,
    created_at: now,
    updated_at: now,
  }
  try {
    const values = await getObjectValues(insertParams)
    const query = insertQuery('user_notifications', insertParams)
    const db_response = await postgres.query(query, values)
    const newNotification = db_response.rows[0]
    // Log the change
    await insertLogChange({
      user_id: params.user_id,
      table_name: 'user_notifications',
      operation_type: 'add',
      new_values: newNotification as unknown as Record<string, unknown>,
      old_values: {},
      created_at: moment().utc(),
      updated_at: moment().utc(),
    })
    return newNotification
  } catch (error) {
    console.error('addUserNotificationError:', error)
    throw serverError
  }
}

/**
 * Update user notifications (by id)
 */
export async function updateUserNotifications(
  id: number,
  user_id: string,
  update: UserNotificationUpdate
): Promise<UserNotification> {
  // Get old values for logging
  const oldNotification = await getUserNotificationById(id)
  if (!oldNotification) throw notFoundError
  update.updated_at = moment.utc().toISOString()
  const condition = { id, user_id }
  const built_update = buildUpdate('user_notifications', condition, update)
  const db_response = await postgres.query(built_update.text, built_update.values)
  if (db_response.rowCount === 0) throw notFoundError
  const updatedNotification = db_response.rows[0]
  // Log the change
  await insertLogChange({
    user_id: String(updatedNotification.user_id),
    table_name: 'user_notifications',
    operation_type: 'edit',
    new_values: updatedNotification as Record<string, unknown>,
    old_values: oldNotification as unknown as Record<string, unknown>,
    created_at: moment().utc(),
    updated_at: moment().utc(),
  })
  return updatedNotification as UserNotification
}

export async function updateNotificationsBatch(ids: number[], user_id: string, key: 'read_at'|'deleted_at'): Promise<number> {
  try {
    const now = moment.utc().toISOString()
    const query = `
      UPDATE user_notifications
      SET ${key} = $1, updated_at = $2
      WHERE user_id = $3 AND id = ANY($4::int[])
      RETURNING id
    `
    const params = [now, now, user_id, ids]
    const db_response = await postgres.query(query, params)
    return db_response.rowCount
  } catch (error) {
    console.error('updateNotificationsBatch:', error)
    throw serverError
  }
}
