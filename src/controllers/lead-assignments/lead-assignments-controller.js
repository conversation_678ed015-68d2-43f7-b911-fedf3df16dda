import client from '../client';

export const _createAssignLead = async ({ assignedUserId, leadId }) => {
  return await client.post(
    `lead-assignments`,
    {
      user_id: assignedUserId,
      lead_id: leadId,
      status: 'active',
      sub_status: '',
      assignment_type: 'general',
      assignment_method: 'manual',
    },
    { credentials: 'omit' }
  );
};

export const _updateSubStatus = async (user_id, assignment_id, sub_status) => {
  const payload = { user_id, sub_status };
  return await client.patch(`lead-assignments/${assignment_id}/sub_status`, payload, { credentials: 'omit' });
};

export const _reassignLead = async (user_id, assignment_id, sub_status) => {
  const payload = { user_id, status: 'active' };
  if (sub_status !== undefined) {
    payload.sub_status = sub_status;
  }
  return await client.patch(`lead-assignments/${assignment_id}/reassign`, payload, { credentials: 'omit' });
};

export const _getUserLeadAssignments = async (payload) => {
  return await client.post(`lead-assignments/search`, payload, { credentials: 'omit' });
};

export const _getLeadsManagement = async (payload) => {
  return await client.post(`lead-assignments/generalAssignments`, payload, { credentials: 'omit' });
};

// TODO analytics, data for supervisor etc
export const _getTeamBalance = async () => {
  return await client.get(`lead-assignments/getTeamBalanceStatus`, {}, { credentials: 'omit' });
};

export const _getAnalytics = async ({ from, to }) => {
  return await client.post(`lead-assignments/getAnalytics`, { from, to }, { credentials: 'omit' });
};

export const _getUserAnalytics = async ({ user_id, from, to }) => {
  return await client.post(`lead-assignments/getUserAnalytics`, { user_id, from, to }, { credentials: 'omit' });
};

export const _getSPOTUsers = async () => {
  return await client.get(`users/spot-users`, {}, { credentials: 'omit' });
};

export const _getSMschools = async (payload) => {
  return await client.post(`lead-assignments/getTop3Scholamatch`, payload, { credentials: 'omit' });
};

export const _getAssignmentsCount = async (user_id) => {
  return await client.get(`lead-assignments/getAssignmentCount/${user_id}`, {}, { credentials: 'omit' });
};
