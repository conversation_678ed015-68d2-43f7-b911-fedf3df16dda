import axios from 'axios'
import { ServerError } from '../error-handling/api-errors/server-error'
import {
  CLOUDTALK_BASE_URL,
  CLOUDTALK_ACCESS_KEY_ID,
  CLOUDTALK_ACCESS_KEY_SECRET,
} from '@constants'

// Import connectors
import { postgres } from '@connectors/postgres'

// Import queries
import { users_queries } from '@queries'

import { validate } from '@utils'

import { notFoundError } from '@errors'

// Import object schemas
import * as schemas from '@schemas'

const cloudtalk = axios.create({
  baseURL: CLOUDTALK_BASE_URL,
  auth: {
    username: CLOUDTALK_ACCESS_KEY_ID,
    password: CLOUDTALK_ACCESS_KEY_SECRET,
  },
  headers: {
    'Content-Type': 'application/json',
  },
})

/**
 * Adds a new agent
 *
 * @param {object} agentData
 * @returns {Promise<object>}
 */
export async function addAgent(agentData: any, user_id: string): Promise<any> {
  try {
    await validate(agentData, schemas.create_agent_cloudtalk)
    const res = await cloudtalk.put('/agents/add.json', agentData)
    const updated_user = await postgres.query(users_queries.udpdateCloudtalkUser(), [res.data.responseData.data.id, user_id])
    if (res.status === 201) {
      console.log('CloudTalk addAgent response:', res.data)
      return updated_user.rows[0]
    } else {
      console.error('CloudTalk addAgent: unexpected status', res.status)
      throw new ServerError(`Unexpected status code ${res.status}`)
    }
  } catch (err: any) {
    console.error('CloudTalk addAgent error:', err)
    throw new ServerError(err)
  }
}

/**
 * Edits an existing agent
 *
 * @param {string | number} user_id
 * @param {object} agentData
 * @returns {Promise<object>}
 */
export async function editAgent(user_id: string | number, agentData: any): Promise<any> {
  try {
    const db_res = await postgres.query(users_queries.getSpotUser(), [user_id])
    if (db_res.rowCount === 0) throw notFoundError
    const res = await cloudtalk.post(`/agents/edit/${db_res.rows[0].cloudtalk_id}.json`, agentData)
    if (res.status === 200) {
      console.log('CloudTalk editAgent response:', res.data)
      return res.data
    } else {
      console.error('CloudTalk editAgent: unexpected status', res.status)
      throw new ServerError(`Unexpected status code ${res.status}`)
    }
  } catch (err: any) {
    console.error('CloudTalk editAgent error:', err)
    throw new ServerError(err)
  }
}

/**
 * Deletes an existing agent
 *
 * @param {string | number} user_id
 * @returns {Promise<object>}
 */
export async function deleteAgent(user_id: string | number): Promise<any> {
  try {
    const db_res = await postgres.query(users_queries.getSpotUser(), [user_id])
    if (db_res.rowCount === 0) throw notFoundError

    if (db_res.rows[0].cloudtalk_id) {
      const res = await cloudtalk.delete(`/agents/delete/${db_res.rows[0].cloudtalk_id}.json`)
      if (res.status === 200) {
        console.log('CloudTalk deleteAgent response:', res.data)
      } else {
        console.error('CloudTalk deleteAgent: unexpected status', res.status)
      }
    }
    await postgres.query(users_queries.udpdateCloudtalkUser(), [null, user_id])
    return db_res.rows[0]
  } catch (err: any) {
    console.error('CloudTalk deleteAgent error:', err)
    throw new ServerError(err)
  }
}

/**
 * Lists all agents
 *
 * @returns {Promise<object>}
 */
export async function listAgents(): Promise<any> {
  try {
    const res = await cloudtalk.get('/agents/index.json')
    console.log('CLOUDTALK_BASE_URL', CLOUDTALK_BASE_URL)
    console.log('CLOUDTALK_ACCESS_KEY_ID', CLOUDTALK_ACCESS_KEY_ID)
    console.log('CLOUDTALK_ACCESS_KEY_SECRET', CLOUDTALK_ACCESS_KEY_SECRET)
    if (res.status === 200) {
      console.log('CloudTalk listAgents response:', res.data)
      return res.data
    } else {
      console.error('CloudTalk listAgents: unexpected status', res.status)
      throw new ServerError(`Unexpected status code ${res.status}`)
    }
  } catch (err: any) {
    console.error('CloudTalk listAgents error:', err)
    throw new ServerError(err)
  }
}

/**
 * Initiates a new call
 *
 * @param {number} user_id
 * @param {string} calleeNumber
 * @returns {Promise<object>}
 */
export async function createCall(user_id: string, calleeNumber: string): Promise<any> {
  try {
    const db_res = await postgres.query(users_queries.getSpotUser(), [user_id])
    console.log('CLOUDTALK_BASE_URL', CLOUDTALK_BASE_URL)
    console.log('CLOUDTALK_ACCESS_KEY_ID', CLOUDTALK_ACCESS_KEY_ID)
    console.log('CLOUDTALK_ACCESS_KEY_SECRET', CLOUDTALK_ACCESS_KEY_SECRET)
    if (db_res.rowCount === 0) throw notFoundError
    const res = await cloudtalk.post(
      '/calls/create.json',
      {
        agent_id: db_res.rows[0].cloudtalk_id,
        callee_number: calleeNumber,
      }
    )
    if (res.status === 200) {
      console.log('CloudTalk createCall response:', res.data)
      return res.data
    } else {
      console.error('CloudTalk createCall: unexpected status', res.status)
      throw new ServerError(`Unexpected status code ${res.status}`)
    }
  } catch (err: any) {
    console.error('CloudTalk createCall error:', err)
    throw new ServerError(err)
  }
}

/**
 * Call history
 *
 * @param {String} callId
 * @returns {Promise<object>}
 */
export async function getCallHistory(callId: number): Promise<any> {
  try {
    const res = await cloudtalk.get('/calls/index.json',{params: {call_id: callId}})
    if (res.status === 200) {
      console.log('CloudTalk getCallHistory response:', res.data)
      return res.data
    } else {
      console.error('CloudTalk getCallHistory: unexpected status', res.status)
      throw new ServerError(`Unexpected status code ${res.status}`)
    }
  } catch (err: any) {
    console.error('CloudTalk getCallHistory error:', err)
    throw new ServerError(err)
  }
}

/**
 * Retrieves call history with optional filters
 *
 * @param {object} filters
 * @returns {Promise<object>}
 */
export async function listCalls(filters: Record<string, any>, user_id: string): Promise<any> {
  try {
    const db_res = await postgres.query(users_queries.getSpotUser(), [user_id])
    if (db_res.rowCount === 0) throw notFoundError
    const res = await cloudtalk.get('/calls/index.json', { params: { ...filters, user_id: db_res.rows[0].cloudtalk_id } })
    if (res.status === 200) {
      console.log('CloudTalk listCalls response:', res.data)
      return res.data
    } else {
      console.error('CloudTalk listCalls: unexpected status', res.status)
      throw new ServerError(`Unexpected status code ${res.status}`)
    }
  } catch (err: any) {
    console.error('CloudTalk listCalls error:', err)
    throw new ServerError(err)
  }
}
