export interface LeadAssignment {
  id?: number;
  user_id: string;
  lead_id: number;
  status: string;
  sub_status: string;
  assignment_type: string;
  assignment_method: string;
  created_at?: Date;
  updated_at?: Date;
  lead_name?: string;
}

export interface LeadAssignmentParams {
  id: number;
  user_id: string;
  status?: string;
  sub_status?: string;
  updated_at?: Date;
}

export interface LeadAssignmentSearchParams {
  user_id?: string;
  status?: string;
  sub_status?: string;
  createdAt_from?: string;
  createdAt_to?: string;
  grades?: Array<string>;
  sources?: Array<string>;
  search_notes?: string;
  assigned_schools?: boolean;
  school_id?: number;
  page: string
  pageSize: string
  fieldSort: string
  fieldDirection: string
}

export interface ManagementLeadAssignmentSearchParams {
  assignedStatus: 'all'|'unassigned'|'assigned'
  status: 'new'|'application-received'
  schoolId?: number
  page: number
  pageSize: number
}

export interface AnalyticsParams {
  from?: string;
  to?: string;
}

export interface Top3Params {
  grade: string;
  zip: string;
  features: string;
  school_types: string;
}

export interface AssignmentCounts {
  general: number
  assigned: number
}
