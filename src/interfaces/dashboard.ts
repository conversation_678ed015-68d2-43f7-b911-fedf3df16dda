import {Moment} from 'moment'

export interface GetData {
  school_id: number
  startDate: string
  endDate: string
}

export interface GroupLeadHistory {
  key: number
  items: Array<LeadsHistory>
}

export interface LeadsHistory {
  lead_id: number
  old_status: string
  new_status: string
  current_status: string
  lead_year: string
  has_completed_application: boolean
  application_received: boolean
  application_valid: boolean
  lead_created_at: Moment
  status_updated_at: string
}

export interface OverviewData {
  applications: number
  applicationsReceived: number
  applicationsWaitlisted: number
  currentEnrollments: number
  archivedApplications: number
  currentEnrollmentsDetails: CurrentEnrollmentsDetails
}

export interface CurrentEnrollmentsDetails {
  current: number
  nextYear: number
}

export interface ProgressData {
  key: string
  date: Moment
  isMonthly: boolean
  applications: number
  applicationsreceived: number
  currentEnrollment: number
  nextYear: number
  waitlisted: number
  archived: number
}

export interface OverviewDataDates {
  start: string
  end: string
}

export interface OverviewDataCurrVs {
  current: number | string
  versus: number | string
}

export interface OverviewDataApplicationsReceived {
  current: number
  versus: number
  details: Array<OverviewDataDetails>
}

export interface OverviewDataDetails {
  name: string
  color: string
  value: number
}

export interface OverviewDataArchivedApplications {
  current: number
  versus: number
  details: OverviewDataArchivedDetails
}

export interface OverviewDataArchivedDetails {
  totalApplications: number
  outApplications: number
}

export interface GetOverviewDataResponse {
  versusDate: OverviewDataDates
  currentDate: OverviewDataDates
  guarantee: number
  goal: number
  conversionRate: OverviewDataCurrVs
  projectedROI: OverviewDataCurrVs
  applicationsReceived: OverviewDataApplicationsReceived
  currentEnrollments: OverviewDataApplicationsReceived
  archivedApplications: OverviewDataArchivedApplications
}

export interface ApplicationsProgressDataObject {
  date: string
  applications: number
  currentEnrollment: number
  nextYear: number
  waitlisted: number
  archived: number
}

export interface ApplicationsProgressData {
  applicationGuarantee: number
  data: Array<ApplicationsProgressDataObject>
}

export interface StatusByMonth {
  month: string
  Leads: number
  Applications: number
  Enrollments: number
}

export interface PerformanceByStatusDates {
  schoolId: string,
  today: string,
  rangeDate: string,
  previousRangeDate: string
}

export interface CountPerformanceDBresponse {
  previous_applications: string,
  current_applications: string,
  previous_new: string,
  current_new: string,
  previous_enrollments: string,
  current_enrollments: string
}

export interface PerformanceByStatusPromise {
  new: string
  //newLeadsPerformance: Performance
  applications: string
  //applicationsPerformance: Performance
  enrollments: string
  //enrollmentPerformance: Performance
}

export type Performance = number | 'firstPerformance'

export interface SPOTMessageCount {
  emailsCount: number
  smsCount: number
}
