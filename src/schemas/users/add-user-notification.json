{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"school_id": {"type": ["number", "null"]}, "user_id": {"type": "string"}, "priority": {"type": ["string", "null"]}, "type": {"type": ["string", "null"]}, "message": {"type": ["string", "null"]}, "read_at": {"type": ["string", "null"], "format": "date-time"}, "deleted_at": {"type": ["string", "null"], "format": "date-time"}}, "required": ["user_id"]}