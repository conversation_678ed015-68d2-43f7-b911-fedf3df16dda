const express = require('express');
const helmet = require('helmet');
const cors = require('cors');
const path = require('path');
const ejs_engine = require('ejs');

const morgan = require('morgan');

const { createProxyMiddleware } = require('http-proxy-middleware');

const { json, urlencoded } = express;

const router = require('./src/routes');

const __BACKEND__ = `https://${process.env.API_URL}`;

const app = express();
const __PORT__ = process.env.PORT || 5001;

app.use(helmet());
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        // Solo modificamos frameAncestors
        frameAncestors: [
          "'self'",
          'schola.com',
          'schools.schola.com',
          'dev-admin.schola.com',
          'staging-admin.schola.com',
          'pages.schola.com',
          'claim.schola.com',
          'schola.com',
        ],
      },
    },
  })
);
app.use(urlencoded({ extended: false }));

// EJS configuration to change the html metatags
ejs_engine.delimiter = '?';
app.set('views', path.join(__dirname, '../build'));
app.engine('html', ejs_engine.renderFile);
app.set('view engine', 'html');

// CORS options
const corsOptions = {
  origin: '*',
  optionsSuccessStatus: 200,
};
app.use(cors(corsOptions));

// Proxy to API
app.use(morgan('dev'));
app.use('/api/**', createProxyMiddleware({ target: __BACKEND__, changeOrigin: true, pathRewrite: { [`^/api`]: '' } }));

// body parser for all the other endpoints
app.use(json());

// MetaTags
const defaultMetadataTags = require('./src/util/metatags');

function redirectToScholaMatch(req, res) {
  let subdomain = '';
  if (process.env.APP_ENV === 'staging') subdomain = 'staging.';
  else if (process.env.APP_ENV === 'development' || process.env.APP_ENV === 'dev') subdomain = 'dev.';
  const newDomain = `https://${subdomain}scholamatch.com`;
  const fullPath = req.originalUrl.toLowerCase() === '/scholamatch' ? '/schola-match-fo' : req.originalUrl;
  return res.redirect(301, `${newDomain}${fullPath}`);
}

app.use(
  [
    '/schools',
    '/scholaMatch',
    '/scholaMatchv5',
    '/auth',
    '/schola-match-fo',
    '/lottery-status',
    '/scholaMatch-spot',
    '/schools-results',
    '/personality',
    '/parent-portal',
    '/personality-match',
  ],
  redirectToScholaMatch
);

app.get('/', (req, res) => {
  if (process.env.APP_ENV === 'production' && req.headers['x-forwarded-proto'] !== 'https') {
    return res.redirect(`https://${req.headers.host}${req.path}`).code(301);
  }
  res.render('index.html', { data: { ...defaultMetadataTags } });
  console.log('get on app.js');
});

app.use(express.static(path.join(__dirname, '../build')));
app.use(router);

app.get('*', (req, res) => {
  res.status(404).send('Not Found');
});

app.listen(__PORT__, () => {
  console.log(`Server listening on port ${__PORT__}`);
});
