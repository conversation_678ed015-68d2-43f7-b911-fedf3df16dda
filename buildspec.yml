version: 0.2

phases:
  pre_build:
    commands:
      ### DOCKER
      - echo Logging in to Amazon ECR...
      - aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin $(aws sts get-caller-identity --query 'Account' --output text).dkr.ecr.us-west-2.amazonaws.com
      - REPOSITORY_URI=************.dkr.ecr.us-west-2.amazonaws.com/${ENV}-schola-api-ts
      - COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)
      - IMAGE_TAG=${COMMIT_HASH:=latest}
      ###
      - |
        if [ "$ENV" = "prod" ]; then
          echo "Configuring New Relic for production environment"
          sed -i -e "s@NEW_RELIC_APP_NAME@$ENV-schola-api@g" newrelic.js
          sed -i -e "s@NEW_RELIC_LICENSE_KEY@$NEW_RELIC_LICENSE_KEY@g" newrelic.js
          sed -i -e "s@NEW_RELIC_ENABLED_FALSE@true@g" newrelic.js
        else
          echo "Disabling New Relic for non-production environment"
          sed -i -e "s@NEW_RELIC_ENABLED_FALSE@false@g" newrelic.js
        fi

  build:
    commands:
      - echo Build started on `date`
      - echo Building the Docker image...
      - docker build -t $REPOSITORY_URI:latest .
      - docker tag $REPOSITORY_URI:latest $REPOSITORY_URI:$IMAGE_TAG
  post_build:
    commands:
      - echo Build completed on `date`
      - echo Pushing the Docker images...
      - docker push $REPOSITORY_URI:latest
      - docker push $REPOSITORY_URI:$IMAGE_TAG
      - echo Writing image definitions file...
      - printf '[{"name":"schola_api_ts","imageUri":"%s"}]' $REPOSITORY_URI:$IMAGE_TAG > schola-api-ts-imagedef.json

cache:
  paths:
    - '**/*/node_modules/**/*'
artifacts:
    files:
      - schola-api-ts-imagedef.json