//New relic
if (process.env.NODE_ENV == "production") {
  require("newrelic");
}
const Glue = require("glue");
const manifest = require("./manifest");

const Inert = require("inert");
const Vision = require("vision");
const HapiSwagger = require("hapi-swagger");

const options = {
  relativeTo: __dirname + "/src",
};

Glue.compose(manifest, options, (err, server) => {
  if (err) {
    throw err;
  }

  const optionsSwagger = {
    info: {
      title: "Schola API Documentation",
      version: "0.0.1",
    },
    documentationPage: true,
    swaggerUI: true,
    deReference: true,
  };

  server.register(
    [
      Inert,
      Vision,
      {
        register: HapiSwagger,
        options: optionsSwagger,
      },
    ],
    (err) => {
      server.start((err) => {
        if (err) {
          console.log(err);
        } else {
          console.log("Server running at:", server.info.uri);
        }
      });
    }
  );

  // server.start(() => {
  //   console.log('Server running at:', server.info.uri);
  // });
});
