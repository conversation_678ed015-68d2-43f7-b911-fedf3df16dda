<!DOCTYPE html>
<html lang="en">
  <head>
    <% if (process.env.REACT_APP_ENV === 'production') { %>
    <script async src="%PUBLIC_URL%/newrelic-spa.js"></script>
    <% } %> <% if (process.env.REACT_APP_ENV === 'production') { %>
    <!-- Google Tag Manager -->
    <script>
      (function (w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({ 'gtm.start': new Date().getTime(), event: 'gtm.js' });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != 'dataLayer' ? '&l=' + l : '';
        j.async = true;
        j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, 'script', 'dataLayer', 'GTM-MX237SC');
    </script>
    <!-- End Google Tag Manager -->

    <!-- updated call -->
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-ERJWFTG0LQ"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag('js', new Date());

      gtag('config', 'G-ERJWFTG0LQ');
    </script>
    <!-- End Google Tag Manager -->
    <!-- Start VWO Async SmartCode -->
    <!-- <link rel="preconnect" href="https://dev.visualwebsiteoptimizer.com" />
    <script type="text/javascript" id="vwoCode">
      window._vwo_code ||
        (function () {
          var account_id = 782923,
            version = 2.1,
            settings_tolerance = 2000,
            hide_element = 'body',
            hide_element_style =
              'opacity:0 !important;filter:alpha(opacity=0) !important;background:none !important;transition:none !important;',
            /* DO NOT EDIT BELOW THIS LINE */
            f = false,
            w = window,
            d = document,
            v = d.querySelector('#vwoCode'),
            cK = '_vwo_' + account_id + '_settings',
            cc = {};
          try {
            var c = JSON.parse(localStorage.getItem('_vwo_' + account_id + '_config'));
            cc = c && typeof c === 'object' ? c : {};
          } catch (e) {}
          var stT = cc.stT === 'session' ? w.sessionStorage : w.localStorage;
          code = {
            nonce: v && v.nonce,
            use_existing_jquery: function () {
              return typeof use_existing_jquery !== 'undefined' ? use_existing_jquery : undefined;
            },
            library_tolerance: function () {
              return typeof library_tolerance !== 'undefined' ? library_tolerance : undefined;
            },
            settings_tolerance: function () {
              return cc.sT || settings_tolerance;
            },
            hide_element_style: function () {
              return '{' + (cc.hES || hide_element_style) + '}';
            },
            hide_element: function () {
              if (performance.getEntriesByName('first-contentful-paint')[0]) {
                return '';
              }
              return typeof cc.hE === 'string' ? cc.hE : hide_element;
            },
            getVersion: function () {
              return version;
            },
            finish: function (e) {
              if (!f) {
                f = true;
                var t = d.getElementById('_vis_opt_path_hides');
                if (t) t.parentNode.removeChild(t);
                if (e) new Image().src = 'https://dev.visualwebsiteoptimizer.com/ee.gif?a=' + account_id + e;
              }
            },
            finished: function () {
              return f;
            },
            addScript: function (e) {
              var t = d.createElement('script');
              t.type = 'text/javascript';
              if (e.src) {
                t.src = e.src;
              } else {
                t.text = e.text;
              }
              v && t.setAttribute('nonce', v.nonce);
              d.getElementsByTagName('head')[0].appendChild(t);
            },
            load: function (e, t) {
              var n = this.getSettings(),
                i = d.createElement('script'),
                r = this;
              t = t || {};
              if (n) {
                i.textContent = n;
                d.getElementsByTagName('head')[0].appendChild(i);
                if (!w.VWO || VWO.caE) {
                  stT.removeItem(cK);
                  r.load(e);
                }
              } else {
                var o = new XMLHttpRequest();
                o.open('GET', e, true);
                o.withCredentials = !t.dSC;
                o.responseType = t.responseType || 'text';
                o.onload = function () {
                  if (t.onloadCb) {
                    return t.onloadCb(o, e);
                  }
                  if (o.status === 200 || o.status === 304) {
                    _vwo_code.addScript({ text: o.responseText });
                  } else {
                    _vwo_code.finish('&e=loading_failure:' + e);
                  }
                };
                o.onerror = function () {
                  if (t.onerrorCb) {
                    return t.onerrorCb(e);
                  }
                  _vwo_code.finish('&e=loading_failure:' + e);
                };
                o.send();
              }
            },
            getSettings: function () {
              try {
                var e = stT.getItem(cK);
                if (!e) {
                  return;
                }
                e = JSON.parse(e);
                if (Date.now() > e.e) {
                  stT.removeItem(cK);
                  return;
                }
                return e.s;
              } catch (e) {
                return;
              }
            },
            init: function () {
              if (d.URL.indexOf('__vwo_disable__') > -1) return;
              var e = this.settings_tolerance();
              w._vwo_settings_timer = setTimeout(function () {
                _vwo_code.finish();
                stT.removeItem(cK);
              }, e);
              var t;
              if (this.hide_element() !== 'body') {
                t = d.createElement('style');
                var n = this.hide_element(),
                  i = n ? n + this.hide_element_style() : '',
                  r = d.getElementsByTagName('head')[0];
                t.setAttribute('id', '_vis_opt_path_hides');
                v && t.setAttribute('nonce', v.nonce);
                t.setAttribute('type', 'text/css');
                if (t.styleSheet) t.styleSheet.cssText = i;
                else t.appendChild(d.createTextNode(i));
                r.appendChild(t);
              } else {
                t = d.getElementsByTagName('head')[0];
                var i = d.createElement('div');
                i.style.cssText =
                  'z-index: 2147483647 !important;position: fixed !important;left: 0 !important;top: 0 !important;width: 100% !important;height: 100% !important;background: white !important;';
                i.setAttribute('id', '_vis_opt_path_hides');
                i.classList.add('_vis_hide_layer');
                t.parentNode.insertBefore(i, t.nextSibling);
              }
              var o = window._vis_opt_url || d.URL,
                s =
                  'https://dev.visualwebsiteoptimizer.com/j.php?a=' +
                  account_id +
                  '&u=' +
                  encodeURIComponent(o) +
                  '&vn=' +
                  version;
              if (w.location.search.indexOf('_vwo_xhr') !== -1) {
                this.addScript({ src: s });
              } else {
                this.load(s + '&x=true');
              }
            },
          };
          w._vwo_code = code;
          code.init();
        })();
    </script> -->
    <!-- End VWO Async SmartCode -->
    <% } %>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title><?=data.title?></title>
    <meta property="og:description" content="<?=data.description?>" />
    <meta name="description" content="<?=data.description?>" />
    <meta property="og:type" content="<?=data.type?>" />
    <meta property="og:title" content="<?=data.title?>" />
    <meta property="og:image" content="<?=data.image?>" />
    <% if (process.env.REACT_APP_ENV !== 'production') { %>
    <meta name="robots" content="noindex" />
    <% } %>
    <script>
      !(function (doc) {
        const defaultMetadataTags = {
          title: `Power Your K-12 Enrollment: Strategy, Outreach, Analysis | Schola`,
          description: `Schola powers your school's PK-12 recruitment, enrollment, and retention, boosting your school’s financial health through expert partnership and powerful tools.`,
          image: '/logo.png',
          type: 'website',
        };
        if ('<?=data.title?>'.startsWith('<') && '<?=data.title?>'.endsWith('>')) {
          doc.title = defaultMetadataTags.title;
          document.querySelector('meta[name="description"]').setAttribute('content', defaultMetadataTags.description);
          document
            .querySelector('meta[property="og:description"]')
            .setAttribute('content', defaultMetadataTags.description);
          document.querySelector('meta[property="og:type"]').setAttribute('content', defaultMetadataTags.type);
          document.querySelector('meta[property="og:title"]').setAttribute('content', defaultMetadataTags.title);
          document.querySelector('meta[property="og:image"]').setAttribute('content', defaultMetadataTags.image);
        }
      })(document);
    </script>
    <link rel="shortcut icon" href="%PUBLIC_URL%/favicon.ico" />
    <link rel="preconnect" href="https://fonts.gstatic.com" />
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&display=swap" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@400;700&display=swap" rel="stylesheet" />
    <link
      rel="stylesheet"
      href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css"
      integrity="sha384-MCw98/SFnGE8fJT3GXwEOngsV7Zt27NXFoaoApmYm81iuXoPkFOJwJ8ERdknLPMO"
      crossorigin="anonymous"
      media="none"
      onload="if(media!='all')media='all'"
    />
    <link
      href="https://fonts.googleapis.com/icon?family=Material+Icons"
      rel="stylesheet"
      media="none"
      onload="if(media!='all')media='all'"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
      rel="stylesheet"
      media="none"
      onload="if(media!='all')media='all'"
    />

    <!--
      Notice the use of %PUBLIC_URL% in the tag above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <script id="stripe-js" src="https://js.stripe.com/v3/" async defer></script>
    <!-- <script id="profitwell-js" data-pw-auth="9db81c47736b9aa60c7e44da7a34f33d">
      (function (i, s, o, g, r, a, m) {
        i[o] =
          i[o] ||
          function () {
            (i[o].q = i[o].q || []).push(arguments);
          };
        a = s.createElement(g);
        m = s.getElementsByTagName(g)[0];
        a.async = 1;
        a.src = r + '?auth=' + s.getElementById(o + '-js').getAttribute('data-pw-auth');
        m.parentNode.insertBefore(a, m);
      })(window, document, 'profitwell', 'script', 'https://public.profitwell.com/js/profitwell.js');
      //profitwell('start', { 'user_email': 'USER_EMAIL_HERE' });
    </script> -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.2/styles/github.min.css" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.2/highlight.min.js"></script>
    <script
      charset="UTF-8"
      src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.2/languages/xml.min.js"
    ></script>
    <title>Schola® Admin</title>
  </head>
  <body>
    <% if (process.env.REACT_APP_ENV === 'production') { %>
    <!-- Google Tag Manager (noscript) -->
    <noscript
      ><iframe
        src="https://www.googletagmanager.com/ns.html?id=GTM-MX237SC"
        height="0"
        width="0"
        style="display: none; visibility: hidden"
      ></iframe
    ></noscript>
    <!-- End Google Tag Manager (noscript) -->
    <% } %>

    <!-- Document360 knowledge base widget start -->
    <script>
      (function (w, d, s, o, f, js, fjs) {
        w['JS-Widget'] = o;
        w[o] =
          w[o] ||
          function () {
            (w[o].q = w[o].q || []).push(arguments);
          };
        (js = d.createElement(s)), (fjs = d.getElementsByTagName(s)[0]);
        js.id = o;
        js.src = f;
        js.async = 1;
        fjs.parentNode.insertBefore(js, fjs);
      })(window, document, 'script', 'mw', 'https://cdn.us.document360.io/static/js/widget.js');
      mw('init', {
        apiKey:
          'Ouvh0mgMyd8so9w9dDkx0kuFvtnd8O2VWXObWhtfx+Kgxx0NhvS03+O3MZBlzsOrqeqpZNvwA39P9mjrZZFN3SUDBM5p59g5l8CSe/B9zdl0qTTH9ou57QbbHhLFZxjHN8qD2tuaqOE6RpOf9PUYRA==',
      });
    </script>
    <!-- Document360 knowledge base widget end -->
    <script async src="https://www.google-analytics.com/analytics.js"></script>

    <script src="https://form.jotform.com/static/feedback2.js"></script>
    <script src="https://cdn.jotfor.ms/s/umd/latest/for-form-embed-handler.js"></script>
    <div id="root"></div>
    <!--FORCE
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start`.
      To create a production bundle, use `npm run build`.
    -->
  </body>
</html>
